import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useLocation } from 'wouter';
import { 
  PlusCircle, 
  Edit, 
  Trash2, 
  ChevronLeft, 
  Search,
  FileCheck,
  Plus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { apiRequest } from '@/lib/queryClient';
import { toast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import Sidebar from '@/components/layout/sidebar';
import Header from '@/components/layout/header';

// Interface para representar um serviço
interface Service {
  id: number;
  name: string;
  description: string | null;
  price: number;
  active: boolean;
}

export default function ServicesPage() {
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const queryClient = useQueryClient();
  const isMobile = useIsMobile();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Busca a lista de serviços
  const { data: services, isLoading } = useQuery<Service[]>({
    queryKey: ['/api/services'],
    refetchOnWindowFocus: false,
  });

  // Mutação para excluir serviço
  const deleteServiceMutation = useMutation({
    mutationFn: async (id: number) => {
      return await apiRequest('DELETE', `/api/services/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/services'] });
      toast({
        title: 'Serviço excluído',
        description: 'O serviço foi excluído com sucesso.',
      });
    },
    onError: (error) => {
      console.error('Erro ao excluir serviço:', error);
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao excluir o serviço.',
        variant: 'destructive',
      });
    },
  });

  // Filtra serviços com base na pesquisa
  const filteredServices = services?.filter((service) => 
    service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (service.description && service.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Formata preço em Reais
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(price / 100);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Serviços" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-medium text-gray-800">Todos os Serviços</h3>
                <p className="text-sm text-gray-500">Gerenciamento de serviços oferecidos</p>
              </div>
              <div className="flex flex-col md:flex-row gap-2 mt-2 md:mt-0">
                <Button onClick={() => navigate('/services/new')} className="bg-primary hover:bg-primary-dark">
                  <Plus className="h-4 w-4 mr-1" />
                  {isMobile ? 'Novo' : 'Novo Serviço'}
                </Button>
              </div>
            </div>
            
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar serviços..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            
            <div className="p-6">
                {isLoading ? (
                  <div className="text-center py-4">Carregando serviços...</div>
                ) : filteredServices && filteredServices.length > 0 ? (
                  <div className="overflow-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Nome</TableHead>
                          <TableHead>Descrição</TableHead>
                          <TableHead>Preço</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Ações</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredServices.map((service) => (
                          <TableRow key={service.id}>
                            <TableCell className="font-medium">{service.id}</TableCell>
                            <TableCell>{service.name}</TableCell>
                            <TableCell>{service.description || '-'}</TableCell>
                            <TableCell>{formatPrice(service.price)}</TableCell>
                            <TableCell>
                              {service.active ? (
                                <Badge>Ativo</Badge>
                              ) : (
                                <Badge variant="outline">Inativo</Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => navigate(`/services/edit/${service.id}`)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      className="text-destructive"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Tem certeza que deseja excluir o serviço "{service.name}"?
                                        Esta ação não pode ser desfeita.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                      <AlertDialogAction
                                        className="bg-destructive text-destructive-foreground"
                                        onClick={() => deleteServiceMutation.mutate(service.id)}
                                      >
                                        Excluir
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-10 flex flex-col items-center">
                    <FileCheck className="h-10 w-10 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold">Nenhum serviço encontrado</h3>
                    <p className="text-muted-foreground">
                      {searchQuery
                        ? "Nenhum serviço corresponde à sua pesquisa."
                        : "Você ainda não cadastrou nenhum serviço."}
                    </p>
                    {!searchQuery && (
                      <Button
                        variant="outline"
                        onClick={() => navigate('/services/new')}
                        className="mt-4"
                      >
                        <PlusCircle className="h-4 w-4 mr-2" />
                        Adicionar Serviço
                      </Button>
                    )}
                  </div>
                )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}