import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Client, ServiceOrder } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format, addDays } from "date-fns";
import { ptBR } from "date-fns/locale";

// Form schema for creating a new quote
const formSchema = z.object({
  clientId: z.string().min(1, "Cliente é obrigatório"),
  serviceOrderId: z.string().optional(),
  total: z.string().min(1, "Valor total é obrigatório"),
  validUntil: z.date({
    required_error: "Por favor, selecione uma data de validade",
  }),
  notes: z.string().optional(),
  description: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function NewQuote() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get clients for select dropdown
  const { data: clients = [], isLoading: isLoadingClients } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  // Get service orders for select dropdown
  const { data: serviceOrders = [], isLoading: isLoadingServiceOrders } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
  });

  const defaultValues: Partial<FormValues> = {
    validUntil: addDays(new Date(), 15), // default validity of 15 days
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  // Create quote mutation
  const mutation = useMutation({
    mutationFn: async (data: any) => {
      return apiRequest("POST", "/api/quotes", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/quotes'] });
      toast({
        title: "Orçamento criado",
        description: "O orçamento foi criado com sucesso",
      });
      navigate("/quotes");
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar orçamento",
        description: error.message || "Ocorreu um erro ao criar o orçamento",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: FormValues) => {
    // Format data for API
    const formattedData = {
      clientId: parseInt(data.clientId),
      serviceOrderId: data.serviceOrderId ? parseInt(data.serviceOrderId) : undefined,
      total: parseInt((parseFloat(data.total) * 100).toString()), // Convert to cents
      validUntil: data.validUntil,
      notes: data.notes,
      description: data.description,
    };

    mutation.mutate(formattedData);
  };

  // Format currency input
  const formatCurrency = (value: string) => {
    const onlyNumbers = value.replace(/\D/g, "");
    
    if (onlyNumbers === "") return "";
    
    const number = parseInt(onlyNumbers);
    const formattedValue = (number / 100).toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
    
    return formattedValue;
  };

  const handleCurrencyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    
    if (value === "") {
      form.setValue("total", "");
      return;
    }
    
    const number = parseInt(value);
    const formatted = (number / 100).toFixed(2);
    form.setValue("total", formatted);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Novo Orçamento" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-medium text-gray-800">Criar Novo Orçamento</h3>
              <p className="text-sm text-gray-500 mt-1">Preencha os campos abaixo para criar um novo orçamento.</p>
            </div>
            
            <div className="p-6">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Client Select */}
                    <FormField
                      control={form.control}
                      name="clientId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cliente</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione um cliente" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {clients.map((client) => (
                                <SelectItem key={client.id} value={client.id.toString()}>
                                  {client.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Service Order Select (Optional) */}
                    <FormField
                      control={form.control}
                      name="serviceOrderId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ordem de Serviço (Opcional)</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione uma OS (opcional)" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {serviceOrders.map((order) => (
                                <SelectItem key={order.id} value={order.id.toString()}>
                                  {order.orderNumber}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Total Value */}
                    <FormField
                      control={form.control}
                      name="total"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor Total</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="R$ 0,00"
                              value={field.value}
                              onChange={(e) => {
                                handleCurrencyChange(e);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Valid Until Date */}
                    <FormField
                      control={form.control}
                      name="validUntil"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Válido Até</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={
                                    "w-full pl-3 text-left font-normal"
                                  }
                                >
                                  {field.value ? (
                                    format(field.value, "PPP", { locale: ptBR })
                                  ) : (
                                    <span>Selecione uma data</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < new Date()}
                                locale={ptBR}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  {/* Description */}
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Descrição</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Descreva brevemente o orçamento"
                            className="resize-none min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {/* Notes */}
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Observações</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Notas adicionais ou termos sobre o orçamento"
                            className="resize-none min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <Button 
                      type="button"
                      variant="outline"
                      onClick={() => navigate("/quotes")}
                      className="sm:order-1 order-2"
                    >
                      Cancelar
                    </Button>
                    <Button 
                      type="submit"
                      className="bg-primary hover:bg-primary-dark sm:order-2 order-1"
                      disabled={mutation.isPending}
                    >
                      {mutation.isPending ? "Criando..." : "Criar Orçamento"}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}