import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Client, Equipment, ServiceOrder } from "@/lib/types";
import { formatDateTime, getStatusColor, getStatusLabel } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Link } from "wouter";
import {
  Edit,
  Trash,
  Plus,
  Eye,
  Building,
  Phone,
  Mail,
  MapPin,
  FileText,
  Clipboard,
} from "lucide-react";

export default function ClientDetails() {
  const params = useParams<{ id: string }>();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const clientId = parseInt(params.id);
  
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Load client details
  const {
    data: client,
    isLoading: isLoadingClient,
    isError,
    error,
  } = useQuery<Client>({
    queryKey: [`/api/clients/${clientId}`],
    enabled: !isNaN(clientId),
  });

  // Load client's equipment
  const { data: equipment = [], isLoading: isLoadingEquipment } = useQuery<Equipment[]>({
    queryKey: [`/api/equipment/client/${clientId}`],
    enabled: !isNaN(clientId),
  });

  // Load client's service orders
  const { data: allServiceOrders = [] } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
    enabled: !isNaN(clientId),
  });

  // Filter service orders for this client
  const clientServiceOrders = allServiceOrders.filter(
    (order) => order.clientId === clientId
  );

  // Métricas do cliente
  const metrics = {
    totalRevenue: clientServiceOrders.reduce((acc, order) => acc + (order.totalAmount || 0), 0),
    avgTicket: clientServiceOrders.length > 0 
      ? clientServiceOrders.reduce((acc, order) => acc + (order.totalAmount || 0), 0) / clientServiceOrders.length 
      : 0,
    lastVisit: clientServiceOrders.length > 0 
      ? new Date(Math.max(...clientServiceOrders.map(o => new Date(o.createdAt).getTime())))
      : null,
    totalOrders: clientServiceOrders.length,
    status: client?.active ? "Ativo" : "Inativo",
    lifetime: client ? Math.floor((new Date().getTime() - new Date(client.createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 0
  };

  // Create form schema for client edit
  const editClientSchema = z.object({
    name: z.string().min(2, "O nome deve ter pelo menos 2 caracteres"),
    email: z.string().email("Formato de email inválido").optional().or(z.literal('')),
    phone: z.string().optional().or(z.literal('')),
    document: z.string().optional().or(z.literal('')),
    address: z.string().optional().or(z.literal('')),
    city: z.string().optional().or(z.literal('')),
    state: z.string().optional().or(z.literal('')),
    zipCode: z.string().optional().or(z.literal('')),
    notes: z.string().optional().or(z.literal('')),
    active: z.boolean().default(true),
  });

  type FormValues = z.infer<typeof editClientSchema>;

  // Edit client form
  const form = useForm<FormValues>({
    resolver: zodResolver(editClientSchema),
    defaultValues: {
      name: client?.name || "",
      email: client?.email || "",
      phone: client?.phone || "",
      document: client?.document || "",
      address: client?.address || "",
      city: client?.city || "",
      state: client?.state || "",
      zipCode: client?.zipCode || "",
      notes: client?.notes || "",
      active: client?.active ?? true,
    },
  });

  // Update form values when client data loads
  useEffect(() => {
    if (client) {
      form.reset({
        name: client.name,
        email: client.email || "",
        phone: client.phone || "",
        document: client.document || "",
        address: client.address || "",
        city: client.city || "",
        state: client.state || "",
        zipCode: client.zipCode || "",
        notes: client.notes || "",
        active: client.active,
      });
    }
  }, [client, form]);

  // Mutation for updating client
  const updateClientMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      return apiRequest("PATCH", `/api/clients/${clientId}`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/clients/${clientId}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/clients'] });
      toast({
        title: "Cliente atualizado",
        description: "Os detalhes do cliente foram atualizados com sucesso",
      });
      setShowEditDialog(false);
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar cliente",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });

  // Mutation for deleting client
  const deleteClientMutation = useMutation({
    mutationFn: async () => {
      return apiRequest("DELETE", `/api/clients/${clientId}`, undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/clients'] });
      toast({
        title: "Cliente excluído",
        description: "O cliente foi excluído com sucesso",
      });
      navigate("/clients");
    },
    onError: (error) => {
      toast({
        title: "Erro ao excluir cliente",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });

  const onSubmitEdit = (values: FormValues) => {
    updateClientMutation.mutate(values);
  };

  const handleDelete = () => {
    deleteClientMutation.mutate();
  };

  if (isLoadingClient) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes do Cliente" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes do Cliente" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-red-500">Erro</CardTitle>
                <CardDescription>
                  Falha ao carregar os detalhes do cliente
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-red-500">{error?.message || "Ocorreu um erro desconhecido"}</p>
                <Button 
                  onClick={() => navigate("/clients")} 
                  className="mt-4"
                >
                  Voltar para Clientes
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes do Cliente" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <Card>
              <CardHeader>
                <CardTitle>Não Encontrado</CardTitle>
                <CardDescription>
                  Cliente não encontrado
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>O cliente que você está procurando não existe ou foi excluído.</p>
                <Button 
                  onClick={() => navigate("/clients")} 
                  className="mt-4"
                >
                  Voltar para Clientes
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title={client.name} onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="mb-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-800 mr-3">
                {client.name}
              </h1>
              <Badge
                variant={client.active ? "default" : "secondary"}
                className={
                  client.active
                    ? "bg-green-100 text-green-800"
                    : "bg-gray-100 text-gray-800"
                }
              >
                {client.active ? "Ativo" : "Inativo"}
              </Badge>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowEditDialog(true)}
              >
                <Edit className="h-4 w-4 mr-1" />
                Editar Cliente
              </Button>
              
              <Button 
                variant="outline" 
                size="sm"
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={() => setShowDeleteDialog(true)}
              >
                <Trash className="h-4 w-4 mr-1" />
                Excluir
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <div className="lg:col-span-2">
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">Visão Geral</TabsTrigger>
                  <TabsTrigger value="equipment">Equipamentos ({equipment.length})</TabsTrigger>
                  <TabsTrigger value="service-orders">Ordens de Serviço ({clientServiceOrders.length})</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview">
                  <Card>
                    <CardHeader>
                      <CardTitle>Detalhes do Cliente</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Informações de Contato</h3>
                              <div className="mt-2 space-y-2">
                                {client.email && (
                                  <div className="flex items-center">
                                    <Mail className="h-4 w-4 text-gray-400 mr-2" />
                                    <span>{client.email}</span>
                                  </div>
                                )}
                                {client.phone && (
                                  <div className="flex items-center">
                                    <Phone className="h-4 w-4 text-gray-400 mr-2" />
                                    <span>{client.phone}</span>
                                  </div>
                                )}
                                {client.document && (
                                  <div className="flex items-center">
                                    <Clipboard className="h-4 w-4 text-gray-400 mr-2" />
                                    <span>Documento: {client.document}</span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {(client.address || client.city || client.state) && (
                              <div>
                                <h3 className="text-sm font-medium text-gray-500">Endereço</h3>
                                <div className="mt-2 space-y-1">
                                  <div className="flex items-start">
                                    <MapPin className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                                    <div>
                                      {client.address && <p>{client.address}</p>}
                                      {(client.city || client.state) && (
                                        <p>
                                          {client.city && client.city}
                                          {client.city && client.state && ", "}
                                          {client.state && client.state}
                                          {client.zipCode && ` ${client.zipCode}`}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                          
                          <div className="space-y-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Cliente Desde</h3>
                              <p className="mt-1">{formatDateTime(client.createdAt, { 
                                year: 'numeric', 
                                month: 'long', 
                                day: 'numeric' 
                              })}</p>
                            </div>
                            
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Resumo Rápido</h3>
                              <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
                                <div className="rounded-md bg-gray-50 p-2">
                                  <p className="text-gray-500">Total de Equipamentos</p>
                                  <p className="text-lg font-semibold">{equipment.length}</p>
                                </div>
                                <div className="rounded-md bg-gray-50 p-2">
                                  <p className="text-gray-500">Ordens de Serviço</p>
                                  <p className="text-lg font-semibold">{clientServiceOrders.length}</p>
                                </div>
                                <div className="rounded-md bg-gray-50 p-2">
                                  <p className="text-gray-500">Ordens Ativas</p>
                                  <p className="text-lg font-semibold">{
                                    clientServiceOrders.filter(order => 
                                      order.status !== 'completed' && order.status !== 'delivered'
                                    ).length
                                  }</p>
                                </div>
                                <div className="rounded-md bg-gray-50 p-2">
                                  <p className="text-gray-500">Ordens Concluídas</p>
                                  <p className="text-lg font-semibold">{
                                    clientServiceOrders.filter(order => 
                                      order.status === 'completed' || order.status === 'delivered'
                                    ).length
                                  }</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-sm font-medium">Valor Total em Serviços</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-2xl font-bold">
                                {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
                                  .format(metrics.totalRevenue / 100)}
                              </div>
                            </CardContent>
                          </Card>
                          
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-sm font-medium">Ticket Médio</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-2xl font-bold">
                                {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' })
                                  .format(metrics.avgTicket / 100)}
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader>
                              <CardTitle className="text-sm font-medium">Tempo como Cliente</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-2xl font-bold">
                                {metrics.lifetime} dias
                              </div>
                            </CardContent>
                          </Card>
                        </div>

                        {client.notes && (
                          <div>
                            <Separator className="my-4" />
                            <h3 className="text-sm font-medium text-gray-500 mb-2">Observações</h3>
                            <p className="text-gray-800 whitespace-pre-line">{client.notes}</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between border-t pt-5">
                      <Button 
                        variant="outline" 
                        onClick={() => navigate("/clients")}
                      >
                        Voltar para Clientes
                      </Button>
                      <Link href={`/service-orders/new?client=${clientId}`}>
                        <Button className="bg-primary hover:bg-primary-dark">
                          <Plus className="h-4 w-4 mr-1" />
                          Nova Ordem de Serviço
                        </Button>
                      </Link>
                    </CardFooter>
                  </Card>
                </TabsContent>
                
                <TabsContent value="equipment">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <div>
                        <CardTitle>Equipamentos do Cliente</CardTitle>
                        <CardDescription>
                          Todos os equipamentos registrados para este cliente
                        </CardDescription>
                      </div>
                      <Link href={`/equipment/new?client=${clientId}`}>
                        <Button className="bg-primary hover:bg-primary-dark">
                          <Plus className="h-4 w-4 mr-1" />
                          Adicionar Equipamento
                        </Button>
                      </Link>
                    </CardHeader>
                    <CardContent>
                      {isLoadingEquipment ? (
                        <div className="flex justify-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                        </div>
                      ) : equipment.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          Nenhum equipamento registrado para este cliente ainda
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Marca</TableHead>
                              <TableHead>Modelo</TableHead>
                              <TableHead>Número de Série</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {equipment.map((item) => (
                              <TableRow key={item.id}>
                                <TableCell className="font-medium">{item.brand || "-"}</TableCell>
                                <TableCell>{item.model || "-"}</TableCell>
                                <TableCell>{item.serialNumber || "-"}</TableCell>
                                <TableCell>
                                  <Badge
                                    variant={item.status === "active" ? "default" : "secondary"}
                                    className={
                                      item.status === "active"
                                        ? "bg-green-100 text-green-800"
                                        : "bg-gray-100 text-gray-800"
                                    }
                                  >
                                    {item.status === "active" ? "Ativo" : "Inativo"}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                  <Link href={`/equipment/${item.id}`}>
                                    <Button variant="ghost" size="sm">
                                      <Eye className="h-4 w-4 mr-1" />
                                      Visualizar
                                    </Button>
                                  </Link>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="service-orders">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <div>
                        <CardTitle>Ordens de Serviço</CardTitle>
                        <CardDescription>
                          Todas as ordens de serviço para este cliente
                        </CardDescription>
                      </div>
                      <Link href={`/service-orders/new?client=${clientId}`}>
                        <Button className="bg-primary hover:bg-primary-dark">
                          <Plus className="h-4 w-4 mr-1" />
                          Nova Ordem de Serviço
                        </Button>
                      </Link>
                    </CardHeader>
                    <CardContent>
                      {clientServiceOrders.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          Nenhuma ordem de serviço para este cliente ainda
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>OS #</TableHead>
                              <TableHead>Equipamento</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Criada em</TableHead>
                              <TableHead className="text-right">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {clientServiceOrders.map((order) => {
                              const statusColors = getStatusColor(order.status);
                              const orderEquipment = equipment.find(e => e.id === order.equipmentId);
                              const equipmentName = orderEquipment 
                                ? `${orderEquipment.brand || ''} ${orderEquipment.model || ''}`.trim() 
                                : 'N/A';
                              
                              return (
                                <TableRow key={order.id}>
                                  <TableCell className="font-medium">#{order.orderNumber}</TableCell>
                                  <TableCell>{equipmentName}</TableCell>
                                  <TableCell>
                                    <Badge className={`${statusColors.bg} ${statusColors.text}`}>
                                      {getStatusLabel(order.status)}
                                    </Badge>
                                  </TableCell>
                                  <TableCell>{formatDateTime(order.createdAt)}</TableCell>
                                  <TableCell className="text-right">
                                    <Link href={`/service-orders/${order.id}`}>
                                      <Button variant="ghost" size="sm">
                                        <Eye className="h-4 w-4 mr-1" />
                                        Visualizar
                                      </Button>
                                    </Link>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
            
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Ações Rápidas</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Link href={`/service-orders/new?client=${clientId}`}>
                    <Button className="w-full justify-start bg-primary hover:bg-primary-dark">
                      <FileText className="h-4 w-4 mr-2" />
                      Nova Ordem de Serviço
                    </Button>
                  </Link>
                  <Link href={`/equipment/new?client=${clientId}`}>
                    <Button className="w-full justify-start" variant="outline">
                      <Building className="h-4 w-4 mr-2" />
                      Adicionar Equipamento
                    </Button>
                  </Link>
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => setShowEditDialog(true)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Editar Cliente
                  </Button>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Ordens de Serviço Ativas</CardTitle>
                </CardHeader>
                <CardContent>
                  {clientServiceOrders.filter(
                    order => order.status !== 'completed' && order.status !== 'delivered'
                  ).length === 0 ? (
                    <p className="text-center text-gray-500 py-2">Nenhuma ordem de serviço ativa</p>
                  ) : (
                    <div className="space-y-3">
                      {clientServiceOrders
                        .filter(order => order.status !== 'completed' && order.status !== 'delivered')
                        .slice(0, 5)
                        .map(order => {
                          const statusColors = getStatusColor(order.status);
                          return (
                            <Link key={order.id} href={`/service-orders/${order.id}`}>
                              <div className="border rounded-md p-3 hover:bg-gray-50 cursor-pointer">
                                <div className="flex justify-between items-start">
                                  <p className="font-medium">#{order.orderNumber}</p>
                                  <Badge className={`${statusColors.bg} ${statusColors.text}`}>
                                    {getStatusLabel(order.status)}
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-500 mt-1">
                                  {formatDateTime(order.updatedAt)}
                                </p>
                              </div>
                            </Link>
                          );
                        })
                      }
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
      
      {/* Edit Client Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Cliente</DialogTitle>
            <DialogDescription>
              Atualizar informações do cliente
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitEdit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome*</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefone</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="document"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Documento</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Endereço</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cidade</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estado</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="zipCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CEP</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observações</FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Ativo</FormLabel>
                      <FormDescription>
                        Marcar este cliente como ativo
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
              
              <div className="sticky bottom-0 bg-white pb-2 pt-4 border-t mt-6">
                <div className="flex flex-row justify-end gap-2">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setShowEditDialog(false)}
                  >
                    Cancelar
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={updateClientMutation.isPending}
                    className="bg-primary hover:bg-primary-dark"
                  >
                    {updateClientMutation.isPending ? "Salvando..." : "Salvar Alterações"}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Cliente</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir {client.name}? Esta ação não pode ser desfeita.
              Todas as ordens de serviço e equipamentos associados permanecerão no sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-500 hover:bg-red-600"
            >
              {deleteClientMutation.isPending ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Excluindo...
                </div>
              ) : (
                "Excluir"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
