import { useState } from "react";
import { useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Save, UserPlus } from "lucide-react";
import { Link } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { User } from "@/lib/types";
import { TECHNICIAN_STATUSES } from "@/lib/constants";

const createTechnicianSchema = z.object({
  userId: z.number().min(1, "Usuário é obrigatório"),
  specialties: z.string().min(1, "Especialidades são obrigatórias"),
  status: z.enum(['available', 'on_service', 'off_duty']),
  notes: z.string().optional(),
});

type CreateTechnicianFormValues = z.infer<typeof createTechnicianSchema>;

export default function NewTechnicianPage() {
  const [, navigate] = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all users to select from
  const { data: users = [], isLoading: usersLoading } = useQuery<User[]>({
    queryKey: ['/api/users']
  });

  // Fetch existing technicians to filter out users who are already technicians
  const { data: existingTechnicians = [] } = useQuery({
    queryKey: ['/api/technicians']
  });

  const form = useForm<CreateTechnicianFormValues>({
    resolver: zodResolver(createTechnicianSchema),
    defaultValues: {
      specialties: "",
      status: "available",
      notes: "",
    },
  });

  const createTechnicianMutation = useMutation({
    mutationFn: async (data: CreateTechnicianFormValues) => {
      return await apiRequest("/api/technicians", {
        method: "POST",
        body: JSON.stringify(data),
      });
    },
    onSuccess: (newTechnician) => {
      toast({
        title: "Sucesso",
        description: "Técnico criado com sucesso!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/technicians'] });
      navigate(`/technicians/${newTechnician.id}`);
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Falha ao criar técnico. Tente novamente.",
        variant: "destructive",
      });
      console.error("Error creating technician:", error);
    },
  });

  const onSubmit = (values: CreateTechnicianFormValues) => {
    createTechnicianMutation.mutate(values);
  };

  // Filter users to only show those who aren't already technicians
  const existingTechnicianUserIds = Array.isArray(existingTechnicians) 
    ? existingTechnicians.map((tech: any) => tech.userId)
    : [];
  const availableUsers = users.filter(user => !existingTechnicianUserIds.includes(user.id));

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Novo Técnico" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="max-w-2xl mx-auto space-y-6">
            {/* Header */}
            <div className="flex items-center gap-3">
              <Link href="/technicians">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Voltar
                </Button>
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">
                Adicionar Novo Técnico
              </h1>
            </div>

            {/* Create Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserPlus className="h-5 w-5" />
                  Informações do Técnico
                </CardTitle>
              </CardHeader>
              <CardContent>
                {usersLoading ? (
                  <div className="p-8 flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <FormField
                        control={form.control}
                        name="userId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Usuário *</FormLabel>
                            <Select onValueChange={(value) => field.onChange(parseInt(value))}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecionar usuário" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {availableUsers.length === 0 ? (
                                  <SelectItem value="no-users" disabled>
                                    Nenhum usuário disponível
                                  </SelectItem>
                                ) : (
                                  availableUsers.map((user) => (
                                    <SelectItem key={user.id} value={user.id.toString()}>
                                      {user.name} ({user.email})
                                    </SelectItem>
                                  ))
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                            {availableUsers.length === 0 && (
                              <p className="text-sm text-gray-500">
                                Todos os usuários já são técnicos ou não há usuários cadastrados.
                              </p>
                            )}
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="specialties"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Especialidades *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Ex: Computadores, Celulares, Impressoras"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecionar status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {TECHNICIAN_STATUSES.map((status) => (
                                  <SelectItem key={status.value} value={status.value}>
                                    {status.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="notes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Observações</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Observações adicionais sobre o técnico..."
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Submit Button */}
                      <div className="flex justify-end space-x-3 pt-6">
                        <Link href="/technicians">
                          <Button variant="outline" type="button">
                            Cancelar
                          </Button>
                        </Link>
                        <Button 
                          type="submit" 
                          className="bg-primary hover:bg-primary-dark"
                          disabled={createTechnicianMutation.isPending || availableUsers.length === 0}
                        >
                          <Save className="h-4 w-4 mr-1" />
                          {createTechnicianMutation.isPending ? "Criando..." : "Criar Técnico"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}