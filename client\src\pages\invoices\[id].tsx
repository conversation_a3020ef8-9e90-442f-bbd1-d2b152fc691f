import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { insertPaymentSchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";

import type { Invoice, InvoiceItem, PaymentMethod, Client } from "@shared/schema";

const invoiceStatusMap = {
  draft: { label: "Rascunho", variant: "outline" },
  issued: { label: "Emitida", variant: "secondary" },
  sent: { label: "Enviada", variant: "default" },
  paid: { label: "Paga", variant: "success" },
  overdue: { label: "Vencida", variant: "destructive" },
  cancelled: { label: "Cancelada", variant: "destructive" },
};

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value / 100);
};

const formatDate = (date: string | Date | null) => {
  if (!date) return "-";
  return format(new Date(date), "dd/MM/yyyy", { locale: ptBR });
};

// Esquema para o formulário de pagamento
const paymentFormSchema = insertPaymentSchema.extend({
  amount: z.string().transform(val => parseInt(val) * 100), // Converter para centavos
  paymentMethodId: z.string().transform(val => parseInt(val)),
});

export default function InvoiceDetailPage() {
  const params = useParams<{ id: string }>();
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  const invoiceId = parseInt(params.id);

  // Consulta da fatura
  const { data: invoice, isLoading: isLoadingInvoice } = useQuery<Invoice>({
    queryKey: [`/api/invoices/${invoiceId}`],
  });

  // Consulta dos itens da fatura
  const { data: invoiceItems, isLoading: isLoadingItems } = useQuery<InvoiceItem[]>({
    queryKey: [`/api/invoices/${invoiceId}/items`],
    enabled: !!invoice,
  });

  // Consulta do cliente
  const { data: client } = useQuery<Client>({
    queryKey: [`/api/clients/${invoice?.clientId}`],
    enabled: !!invoice?.clientId,
  });

  // Consulta dos métodos de pagamento
  const { data: paymentMethods } = useQuery<PaymentMethod[]>({
    queryKey: ['/api/payment-methods'],
  });

  // Consulta dos pagamentos da fatura
  const { data: payments, isLoading: isLoadingPayments } = useQuery({
    queryKey: [`/api/payments/invoice/${invoiceId}`],
    enabled: !!invoice,
  });

  // Formulário de pagamento
  const paymentForm = useForm<z.infer<typeof paymentFormSchema>>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      invoiceId,
      clientId: invoice?.clientId,
      amount: invoice?.totalAmount ? ((invoice.totalAmount - (invoice.paidAmount || 0)) / 100).toString() : "",
      status: "pending",
      notes: "",
    },
  });

  // Atualiza o formulário quando a fatura for carregada
  if (invoice && !paymentForm.getValues().clientId) {
    paymentForm.setValue("clientId", invoice.clientId);
    const remainingAmount = invoice.totalAmount - (invoice.paidAmount || 0);
    paymentForm.setValue("amount", (remainingAmount / 100).toString());
  }

  // Mutação para criar um pagamento
  const createPaymentMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/payments", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [`/api/payments/invoice/${invoiceId}`]
      });
      queryClient.invalidateQueries({
        queryKey: [`/api/invoices/${invoiceId}`]
      });
      queryClient.invalidateQueries({
        queryKey: ['/api/invoices']
      });
      toast({
        title: "Pagamento registrado com sucesso",
        description: "O pagamento foi registrado e a fatura foi atualizada.",
      });
      setShowPaymentDialog(false);
      paymentForm.reset();
    },
    onError: (error) => {
      toast({
        title: "Erro ao registrar pagamento",
        description: "Ocorreu um erro ao registrar o pagamento. Tente novamente.",
        variant: "destructive",
      });
      console.error(error);
    },
  });

  // Mutação para atualizar o status da fatura
  const updateInvoiceStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: number; status: string }) => {
      const response = await apiRequest("PATCH", `/api/invoices/${id}`, { status });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [`/api/invoices/${invoiceId}`]
      });
      queryClient.invalidateQueries({
        queryKey: ['/api/invoices']
      });
      toast({
        title: "Status da fatura atualizado",
        description: "O status da fatura foi atualizado com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar status",
        description: "Ocorreu um erro ao atualizar o status da fatura.",
        variant: "destructive",
      });
      console.error(error);
    },
  });

  // Envia o formulário de pagamento
  const onSubmitPayment = (values: z.infer<typeof paymentFormSchema>) => {
    createPaymentMutation.mutate(values);
  };

  // Atualiza o status da fatura
  const updateInvoiceStatus = (status: string) => {
    if (invoice) {
      updateInvoiceStatusMutation.mutate({ id: invoice.id, status });
    }
  };

  if (isLoadingInvoice) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <p>Carregando detalhes da fatura...</p>
        </div>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <p>Fatura não encontrada.</p>
        </div>
      </div>
    );
  }

  // Calcula o valor restante a pagar
  const remainingAmount = invoice.totalAmount - (invoice.paidAmount || 0);

  // Determina se a fatura está paga
  const isPaid = invoice.status === "paid" || remainingAmount <= 0;

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Fatura #{invoice.invoiceNumber}</h1>
        <div className="flex gap-2">
          {invoice.status === "draft" && (
            <Button onClick={() => updateInvoiceStatus("issued")}>
              Emitir Fatura
            </Button>
          )}
          {invoice.status === "issued" && (
            <Button onClick={() => updateInvoiceStatus("sent")}>
              Marcar como Enviada
            </Button>
          )}
          {(invoice.status === "issued" || invoice.status === "sent" || invoice.status === "overdue") && !isPaid && (
            <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
              <DialogTrigger asChild>
                <Button>Registrar Pagamento</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Registrar Pagamento</DialogTitle>
                  <DialogDescription>
                    Registre um pagamento para esta fatura.
                  </DialogDescription>
                </DialogHeader>
                <Form {...paymentForm}>
                  <form onSubmit={paymentForm.handleSubmit(onSubmitPayment)} className="space-y-4">
                    <FormField
                      control={paymentForm.control}
                      name="paymentMethodId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Método de Pagamento</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o método de pagamento" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {paymentMethods?.map((method) => (
                                <SelectItem key={method.id} value={method.id.toString()}>
                                  {method.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={paymentForm.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor (R$)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              placeholder="0,00"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={paymentForm.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Observações</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Observações sobre o pagamento"
                              className="resize-none"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <DialogFooter>
                      <Button
                        type="submit"
                        disabled={createPaymentMutation.isPending}
                      >
                        {createPaymentMutation.isPending
                          ? "Registrando..."
                          : "Registrar Pagamento"}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          )}
          {invoice.status !== "cancelled" && (
            <Button
              variant="destructive"
              onClick={() => updateInvoiceStatus("cancelled")}
            >
              Cancelar Fatura
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Fatura #{invoice.invoiceNumber}</CardTitle>
                  <CardDescription>
                    Emitida em {formatDate(invoice.createdAt)}
                  </CardDescription>
                </div>
                <Badge variant={
                  invoiceStatusMap[invoice.status as keyof typeof invoiceStatusMap]?.variant as any || "outline"
                }>
                  {invoiceStatusMap[invoice.status as keyof typeof invoiceStatusMap]?.label || invoice.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 sm:grid-cols-2">
                <div>
                  <h3 className="font-medium mb-1">Cliente</h3>
                  <p>{client?.name}</p>
                  <p className="text-sm text-muted-foreground">{client?.email}</p>
                  <p className="text-sm text-muted-foreground">{client?.phone}</p>
                </div>
                <div className="text-right">
                  <h3 className="font-medium mb-1">Datas</h3>
                  <p>Emissão: {formatDate(invoice.createdAt)}</p>
                  <p>Vencimento: {formatDate(invoice.dueDate)}</p>
                  {invoice.paidAt && (
                    <p>Pagamento: {formatDate(invoice.paidAt)}</p>
                  )}
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Itens</h3>
                {isLoadingItems ? (
                  <p>Carregando itens...</p>
                ) : (
                  <div className="border rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-muted">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Descrição
                          </th>
                          <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Qtd
                          </th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Valor Unit.
                          </th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-card divide-y divide-gray-200">
                        {invoiceItems?.map((item) => (
                          <tr key={item.id}>
                            <td className="px-4 py-3 text-sm text-left">
                              {item.description}
                            </td>
                            <td className="px-4 py-3 text-sm text-center">
                              {item.quantity}
                            </td>
                            <td className="px-4 py-3 text-sm text-right">
                              {formatCurrency(item.unitPrice)}
                            </td>
                            <td className="px-4 py-3 text-sm text-right font-medium">
                              {formatCurrency(item.quantity * item.unitPrice)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot className="bg-muted">
                        <tr>
                          <td colSpan={3} className="px-4 py-3 text-sm text-right font-bold">
                            Total
                          </td>
                          <td className="px-4 py-3 text-sm text-right font-bold">
                            {formatCurrency(invoice.totalAmount)}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                )}
              </div>
              
              {invoice.notes && (
                <div>
                  <h3 className="font-medium mb-1">Observações</h3>
                  <p className="text-sm text-muted-foreground">{invoice.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Pagamentos</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingPayments ? (
                <p>Carregando pagamentos...</p>
              ) : payments?.length > 0 ? (
                <div className="border rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-muted">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Data
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Método
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Valor
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-card divide-y divide-gray-200">
                      {payments.map((payment) => (
                        <tr key={payment.id}>
                          <td className="px-4 py-3 text-sm">
                            {formatDate(payment.createdAt)}
                          </td>
                          <td className="px-4 py-3 text-sm">
                            {paymentMethods?.find(m => m.id === payment.paymentMethodId)?.name || 'Não especificado'}
                          </td>
                          <td className="px-4 py-3 text-sm text-right">
                            {formatCurrency(payment.amount)}
                          </td>
                          <td className="px-4 py-3 text-sm text-center">
                            <Badge variant={payment.status === "paid" ? "success" : "outline"}>
                              {payment.status === "paid" ? "Confirmado" : "Pendente"}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-center py-4 text-muted-foreground">
                  Nenhum pagamento registrado para esta fatura.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Resumo Financeiro</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between py-1">
              <span className="text-muted-foreground">Valor Total</span>
              <span className="font-medium">{formatCurrency(invoice.totalAmount)}</span>
            </div>
            <div className="flex justify-between py-1">
              <span className="text-muted-foreground">Valor Pago</span>
              <span className="font-medium">{formatCurrency(invoice.paidAmount || 0)}</span>
            </div>
            
            <Separator />
            
            <div className="flex justify-between py-1">
              <span className="font-bold">Saldo Restante</span>
              <span className="font-bold">
                {formatCurrency(remainingAmount)}
              </span>
            </div>
            
            {!isPaid && (
              <Button 
                className="w-full mt-4" 
                disabled={invoice.status === "cancelled"}
                onClick={() => setShowPaymentDialog(true)}
              >
                Registrar Pagamento
              </Button>
            )}
            
            {isPaid && (
              <div className="rounded-md border p-4 bg-green-50 dark:bg-green-950 text-center mt-4">
                <p className="text-sm font-medium text-green-500 dark:text-green-400">
                  Esta fatura está completamente paga.
                </p>
              </div>
            )}
            
            {invoice.status === "cancelled" && (
              <div className="rounded-md border p-4 bg-red-50 dark:bg-red-950 text-center mt-4">
                <p className="text-sm font-medium text-red-500 dark:text-red-400">
                  Esta fatura foi cancelada.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}