import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { ServiceOrder, Client, Equipment, Technician, User } from "@/lib/types";
import { formatDateTime, getStatusColor, getStatusLabel } from "@/lib/utils";
import { Link } from "wouter";
import { Eye } from "lucide-react";

export default function RecentServiceOrders() {
  const { data: serviceOrders = [], isLoading: isLoadingOrders } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
  });

  const { data: clients = [], isLoading: isLoadingClients } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  const { data: equipment = [], isLoading: isLoadingEquipment } = useQuery<Equipment[]>({
    queryKey: ['/api/equipment'],
  });

  const { data: technicians = [], isLoading: isLoadingTechnicians } = useQuery<Technician[]>({
    queryKey: ['/api/technicians'],
  });

  const { data: users = [], isLoading: isLoadingUsers } = useQuery<User[]>({
    queryKey: ['/api/users'],
  });

  const isLoading = isLoadingOrders || isLoadingClients || isLoadingEquipment || isLoadingTechnicians || isLoadingUsers;

  const getClientName = (clientId: number) => {
    const client = clients.find(c => c.id === clientId);
    return client?.name || 'Unknown Client';
  };

  const getEquipmentName = (equipmentId: number | undefined) => {
    if (!equipmentId) return 'N/A';
    const item = equipment.find(e => e.id === equipmentId);
    return item ? `${item.brand || ''} ${item.model || ''}`.trim() || item.description || 'N/A' : 'N/A';
  };

  const getTechnicianName = (technicianId: number | undefined) => {
    if (!technicianId) return 'Unassigned';
    const technician = technicians.find(t => t.id === technicianId);
    if (!technician) return 'Unassigned';
    
    const user = users.find(u => u.id === technician.userId);
    return user?.name || 'Unknown Technician';
  };

  // Sort by created date, newest first
  const sortedOrders = [...(serviceOrders || [])].sort((a, b) => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  }).slice(0, 5); // Take only the 5 most recent

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-800">Ordens de Serviço Recentes</h3>
        <Link href="/service-orders">
          <Button variant="link" className="text-primary hover:text-primary-dark">Ver Todas</Button>
        </Link>
      </div>
      
      <div className="overflow-x-auto">
        {isLoading ? (
          <div className="p-8 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : sortedOrders.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            Nenhuma ordem de serviço encontrada. Crie sua primeira!
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Ver</span>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nº OS</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipamento</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Técnico</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Última Atualização</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedOrders.map((order) => {
                const statusColors = getStatusColor(order.status);
                return (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <Link href={`/service-orders/${order.id}`}>
                        <Button variant="ghost" size="icon" className="text-primary hover:text-primary-dark">
                          <Eye className="h-5 w-5" />
                        </Button>
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary">#{order.orderNumber}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{getClientName(order.clientId)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getEquipmentName(order.equipmentId)}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors.bg} ${statusColors.text}`}>
                        {getStatusLabel(order.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getTechnicianName(order.technicianId)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDateTime(order.updatedAt)}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        )}
      </div>
      
      <div className="px-6 py-3 border-t border-gray-200 bg-gray-50 text-sm text-gray-500">
        Exibindo {sortedOrders.length} de {serviceOrders.length} ordens de serviço no total
      </div>
    </div>
  );
}
