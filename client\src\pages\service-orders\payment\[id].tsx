import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  CreditCard, 
  DollarSign, 
  Smartphone,
  Banknote,
  CheckCircle
} from "lucide-react";
import { cn, formatCurrency } from "@/lib/utils";
import {
  ServiceOrder,
  Client,
  ServiceOrderItem,
} from "@/lib/types";

// Schema de validação para o pagamento
const paymentSchema = z.object({
  paymentMethod: z.enum(["credit_card", "debit_card", "cash", "pix"]),
  installments: z.coerce.number().min(1).max(12).optional(),
  discount: z.coerce.number().min(0).max(100).default(0),
  tax: z.coerce.number().min(0).default(0),
  notes: z.string().optional(),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

const PAYMENT_METHODS = [
  {
    value: "credit_card",
    label: "Cartão de Crédito",
    icon: CreditCard,
    allowInstallments: true,
  },
  {
    value: "debit_card", 
    label: "Cartão de Débito",
    icon: CreditCard,
    allowInstallments: false,
  },
  {
    value: "pix",
    label: "PIX",
    icon: Smartphone,
    allowInstallments: false,
  },
  {
    value: "cash",
    label: "Dinheiro",
    icon: Banknote,
    allowInstallments: false,
  },
];

const INSTALLMENT_OPTIONS = [
  { value: 1, label: "À vista" },
  { value: 2, label: "2x sem juros" },
  { value: 3, label: "3x sem juros" },
  { value: 4, label: "4x sem juros" },
  { value: 5, label: "5x sem juros" },
  { value: 6, label: "6x sem juros" },
  { value: 7, label: "7x sem juros" },
  { value: 8, label: "8x sem juros" },
  { value: 9, label: "9x sem juros" },
  { value: 10, label: "10x sem juros" },
  { value: 11, label: "11x sem juros" },
  { value: 12, label: "12x sem juros" },
];

export default function PaymentPage() {
  const { id } = useParams();
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Consulta da ordem de serviço
  const { data: serviceOrder, isLoading: isLoadingServiceOrder } = useQuery<ServiceOrder>({
    queryKey: [`/api/service-orders/${id}`],
    enabled: !!id,
  });

  // Consulta dos itens da ordem de serviço
  const { data: orderItems = [] } = useQuery<ServiceOrderItem[]>({
    queryKey: [`/api/service-orders/${id}/items`],
    enabled: !!id,
  });

  // Consulta do cliente
  const { data: client } = useQuery<Client>({
    queryKey: [`/api/clients/${serviceOrder?.clientId}`],
    enabled: !!serviceOrder?.clientId,
  });

  // Formulário
  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      paymentMethod: "credit_card",
      installments: 1,
      discount: 0,
      tax: 0,
      notes: "",
    },
  });

  const paymentMethod = form.watch("paymentMethod");
  const discount = form.watch("discount");
  const tax = form.watch("tax");
  const installments = form.watch("installments") || 1;

  // Cálculos
  const subtotal = orderItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const discountAmount = (subtotal * discount) / 100;
  const taxAmount = tax;
  const total = subtotal - discountAmount + taxAmount;
  const installmentValue = installments > 1 ? total / installments : total;

  // Mutação para processar pagamento
  const processPaymentMutation = useMutation({
    mutationFn: async (data: PaymentFormData) => {
      const paymentData = {
        serviceOrderId: Number(id),
        amount: Math.round(total * 100), // Converter para centavos
        paymentMethodId: 1, // ID do método de pagamento (seria dinâmico em prod)
        clientId: serviceOrder?.clientId,
        notes: data.notes,
        status: "paid",
        additionalInfo: {
          paymentMethod: data.paymentMethod,
          installments: data.installments,
          discount: discountAmount,
          tax: taxAmount,
          subtotal: subtotal,
          total: total,
        },
      };

      const response = await apiRequest("POST", "/api/payments", paymentData);
      return response.json();
    },
    onSuccess: () => {
      // Atualizar status da ordem de serviço para "completed"
      return apiRequest("PUT", `/api/service-orders/${id}`, {
        status: "completed",
        completedAt: new Date().toISOString(),
      });
    },
    onError: (error) => {
      console.error('Erro ao processar pagamento:', error);
      toast({
        title: "Erro ao processar pagamento",
        description: "Não foi possível processar o pagamento. Tente novamente.",
        variant: "destructive",
      });
      setIsProcessing(false);
    },
  });

  const onSubmit = async (data: PaymentFormData) => {
    setIsProcessing(true);
    
    try {
      await processPaymentMutation.mutateAsync(data);
      
      toast({
        title: "Pagamento processado com sucesso!",
        description: `Pagamento de ${formatCurrency(total)} recebido.`,
      });

      // Invalidar queries relacionadas
      queryClient.invalidateQueries({
        queryKey: [`/api/service-orders/${id}`]
      });
      queryClient.invalidateQueries({
        queryKey: ['/api/service-orders']
      });
      queryClient.invalidateQueries({
        queryKey: ['/api/payments']
      });

      // Navegar de volta para os detalhes da ordem
      navigate(`/service-orders/${id}`);
    } catch (error) {
      setIsProcessing(false);
    }
  };

  if (isLoadingServiceOrder) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header
            title="Carregando..."
            onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-lg text-gray-600">Carregando ordem de serviço...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!serviceOrder) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header
            title="Ordem não encontrada"
            onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-lg text-gray-600">Ordem de serviço não encontrada</p>
                <Button 
                  onClick={() => navigate("/service-orders")} 
                  className="mt-4"
                >
                  Voltar para Ordens de Serviço
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header
          title={`Pagamento - OS ${serviceOrder.orderNumber}`}
          onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="container mx-auto py-6">
            <div className="mb-6">
              <Button
                variant="outline"
                onClick={() => navigate(`/service-orders/${id}`)}
                className="mb-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar aos Detalhes
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Formulário de Pagamento */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Processar Pagamento</CardTitle>
                    <CardDescription>
                      Configure os detalhes do pagamento para a ordem #{serviceOrder.orderNumber}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        {/* Método de Pagamento */}
                        <FormField
                          control={form.control}
                          name="paymentMethod"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Método de Pagamento</FormLabel>
                              <FormControl>
                                <RadioGroup
                                  value={field.value}
                                  onValueChange={field.onChange}
                                  className="grid grid-cols-2 gap-4"
                                >
                                  {PAYMENT_METHODS.map((method) => {
                                    const Icon = method.icon;
                                    return (
                                      <div key={method.value}>
                                        <RadioGroupItem
                                          value={method.value}
                                          id={method.value}
                                          className="peer sr-only"
                                        />
                                        <Label
                                          htmlFor={method.value}
                                          className={cn(
                                            "flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                                          )}
                                        >
                                          <Icon className="mb-3 h-6 w-6" />
                                          <span className="text-sm font-medium">{method.label}</span>
                                        </Label>
                                      </div>
                                    );
                                  })}
                                </RadioGroup>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Parcelamento (apenas para cartão de crédito) */}
                        {paymentMethod === "credit_card" && (
                          <FormField
                            control={form.control}
                            name="installments"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Parcelamento</FormLabel>
                                <Select
                                  onValueChange={(value) => field.onChange(Number(value))}
                                  value={field.value?.toString()}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o parcelamento" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {INSTALLMENT_OPTIONS.map((option) => (
                                      <SelectItem key={option.value} value={option.value.toString()}>
                                        {option.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}

                        {/* Desconto */}
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="discount"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Desconto (%)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    max="100"
                                    step="0.1"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="tax"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Taxa Adicional (R$)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* Observações */}
                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Observações</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Observações sobre o pagamento..."
                                  className="resize-none"
                                  rows={3}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="flex gap-4 pt-6">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => navigate(`/service-orders/${id}`)}
                            className="flex-1"
                          >
                            Cancelar
                          </Button>
                          <Button
                            type="submit"
                            disabled={isProcessing}
                            className="flex-1 bg-green-600 hover:bg-green-700"
                          >
                            {isProcessing ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Processando...
                              </>
                            ) : (
                              <>
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Processar Pagamento
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </div>

              {/* Resumo do Pagamento */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Resumo do Pagamento</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Cliente */}
                    <div>
                      <p className="text-sm font-medium text-gray-600">Cliente</p>
                      <p className="text-lg font-semibold">{client?.name || 'Carregando...'}</p>
                    </div>

                    <Separator />

                    {/* Valores */}
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Subtotal:</span>
                        <span>{formatCurrency(subtotal)}</span>
                      </div>
                      
                      {discount > 0 && (
                        <div className="flex justify-between text-green-600">
                          <span>Desconto ({discount}%):</span>
                          <span>-{formatCurrency(discountAmount)}</span>
                        </div>
                      )}
                      
                      {tax > 0 && (
                        <div className="flex justify-between text-orange-600">
                          <span>Taxa adicional:</span>
                          <span>+{formatCurrency(taxAmount)}</span>
                        </div>
                      )}
                      
                      <Separator />
                      
                      <div className="flex justify-between text-lg font-bold">
                        <span>Total:</span>
                        <span>{formatCurrency(total)}</span>
                      </div>
                    </div>

                    {/* Informações do parcelamento */}
                    {paymentMethod === "credit_card" && installments > 1 && (
                      <>
                        <Separator />
                        <div className="bg-blue-50 p-3 rounded-md">
                          <p className="text-sm font-medium text-blue-800">Parcelamento</p>
                          <p className="text-sm text-blue-600">
                            {installments}x de {formatCurrency(installmentValue)}
                          </p>
                        </div>
                      </>
                    )}

                    {/* Método de pagamento selecionado */}
                    <Separator />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Método de Pagamento</p>
                      <p className="text-sm">
                        {PAYMENT_METHODS.find(m => m.value === paymentMethod)?.label}
                      </p>
                    </div>

                    {/* Itens da ordem */}
                    {orderItems.length > 0 && (
                      <>
                        <Separator />
                        <div>
                          <p className="text-sm font-medium text-gray-600 mb-2">Itens da Ordem</p>
                          <div className="space-y-1">
                            {orderItems.map((item, index) => (
                              <div key={index} className="flex justify-between text-sm">
                                <span className="truncate">{item.description}</span>
                                <span>{formatCurrency(item.quantity * item.unitPrice)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}