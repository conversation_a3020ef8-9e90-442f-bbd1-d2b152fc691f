import { useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { InventoryItem, InventoryCategory } from "@/lib/types";
import { Eye, Plus, Search, ArrowUpDown, AlertCircle } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { apiRequest } from "@/lib/queryClient";

export default function InventoryPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [stockFilter, setStockFilter] = useState<string>("all");
  const [showAdjustStockDialog, setShowAdjustStockDialog] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: inventoryItems = [], isLoading: isLoadingItems } = useQuery<InventoryItem[]>({
    queryKey: ['/api/inventory'],
  });

  const { data: categories = [], isLoading: isLoadingCategories } = useQuery<InventoryCategory[]>({
    queryKey: ['/api/inventory-categories'],
  });

  const isLoading = isLoadingItems || isLoadingCategories;

  // Schema for stock adjustment
  const adjustStockSchema = z.object({
    quantity: z.coerce.number().int("Must be a whole number").min(-9999).max(9999),
  });

  type AdjustStockFormValues = z.infer<typeof adjustStockSchema>;

  const form = useForm<AdjustStockFormValues>({
    resolver: zodResolver(adjustStockSchema),
    defaultValues: {
      quantity: 0,
    },
  });

  const getCategoryName = (categoryId?: number) => {
    if (!categoryId) return 'Uncategorized';
    const category = categories.find(c => c.id === categoryId);
    return category?.name || 'Unknown Category';
  };

  // Filter inventory items
  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.sku || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.supplier || '').toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory =
      categoryFilter === "all" ||
      (categoryFilter === "uncategorized" && !item.categoryId) ||
      (item.categoryId?.toString() === categoryFilter);

    const matchesStock = 
      stockFilter === "all" ||
      (stockFilter === "lowStock" && item.quantity <= item.minQuantity) ||
      (stockFilter === "outOfStock" && item.quantity === 0) ||
      (stockFilter === "inStock" && item.quantity > item.minQuantity);
    
    return matchesSearch && matchesCategory && matchesStock;
  });

  // Sort inventory items
  const sortedItems = [...filteredItems].sort((a, b) => {
    let result = 0;
    
    switch (sortBy) {
      case "name":
        result = a.name.localeCompare(b.name);
        break;
      case "sku":
        result = (a.sku || '').localeCompare(b.sku || '');
        break;
      case "category":
        result = getCategoryName(a.categoryId).localeCompare(getCategoryName(b.categoryId));
        break;
      case "quantity":
        result = a.quantity - b.quantity;
        break;
      case "price":
        result = (a.price || 0) - (b.price || 0);
        break;
      default:
        result = a.name.localeCompare(b.name);
    }
    
    return sortOrder === "asc" ? result : -result;
  });

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  // Handle stock adjustment
  const openAdjustStockDialog = (itemId: number) => {
    setSelectedItemId(itemId);
    setShowAdjustStockDialog(true);
    form.reset({ quantity: 0 });
  };

  const adjustStockMutation = useMutation({
    mutationFn: async (data: AdjustStockFormValues) => {
      if (selectedItemId === null) return null;
      return apiRequest("PATCH", `/api/inventory/${selectedItemId}/adjust`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/inventory'] });
      queryClient.invalidateQueries({ queryKey: ['/api/inventory/low-stock'] });
      toast({
        title: "Estoque atualizado",
        description: "A quantidade em estoque foi atualizada com sucesso",
      });
      setShowAdjustStockDialog(false);
      setSelectedItemId(null);
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar estoque",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });

  const onSubmitAdjustStock = (values: AdjustStockFormValues) => {
    adjustStockMutation.mutate(values);
  };

  const getStockStatus = (item: InventoryItem) => {
    if (item.quantity === 0) {
      return { label: "Sem Estoque", color: "bg-red-100 text-red-800" };
    } else if (item.quantity <= item.minQuantity) {
      return { label: "Estoque Baixo", color: "bg-yellow-100 text-yellow-800" };
    } else {
      return { label: "Em Estoque", color: "bg-green-100 text-green-800" };
    }
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Estoque" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-2 md:mb-0">Gerenciamento de Estoque</h3>
              <div className="flex flex-col md:flex-row gap-2">
                <Link href="/inventory/new">
                  <Button className="bg-primary hover:bg-primary-dark">
                    <Plus className="h-4 w-4 mr-1" />
                    Adicionar Item
                  </Button>
                </Link>
              </div>
            </div>
            
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Pesquisar estoque..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filtrar por categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as Categorias</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                  <SelectItem value="uncategorized">Sem Categoria</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={stockFilter} onValueChange={setStockFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filtrar por estoque" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Níveis</SelectItem>
                  <SelectItem value="inStock">Em Estoque</SelectItem>
                  <SelectItem value="lowStock">Estoque Baixo</SelectItem>
                  <SelectItem value="outOfStock">Sem Estoque</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Nome</SelectItem>
                  <SelectItem value="sku">SKU</SelectItem>
                  <SelectItem value="category">Categoria</SelectItem>
                  <SelectItem value="quantity">Quantidade</SelectItem>
                  <SelectItem value="price">Preço</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="overflow-x-auto">
              {isLoading ? (
                <div className="p-8 flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : filteredItems.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  Nenhum item de estoque encontrado. Adicione seu primeiro item!
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[250px]">
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("name");
                            toggleSortOrder();
                          }}
                        >
                          Nome
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("sku");
                            toggleSortOrder();
                          }}
                        >
                          SKU
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("category");
                            toggleSortOrder();
                          }}
                        >
                          Categoria
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("quantity");
                            toggleSortOrder();
                          }}
                        >
                          Quantidade
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("price");
                            toggleSortOrder();
                          }}
                        >
                          Preço
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedItems.map((item) => {
                      const stockStatus = getStockStatus(item);
                      return (
                        <TableRow key={item.id} className="hover:bg-gray-50">
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              {item.quantity <= item.minQuantity && (
                                <AlertCircle className="h-4 w-4 text-yellow-500 mr-2" />
                              )}
                              <span>{item.name}</span>
                            </div>
                          </TableCell>
                          <TableCell>{item.sku || '-'}</TableCell>
                          <TableCell>{getCategoryName(item.categoryId)}</TableCell>
                          <TableCell>
                            {item.quantity} / {item.minQuantity} min
                          </TableCell>
                          <TableCell>{item.price ? formatCurrency(item.price) : '-'}</TableCell>
                          <TableCell>
                            <Badge className={stockStatus.color}>
                              {stockStatus.label}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                onClick={() => openAdjustStockDialog(item.id)}
                              >
                                Ajustar
                              </Button>
                              <Link href={`/inventory/${item.id}`}>
                                <Button variant="ghost" size="sm" className="text-primary hover:text-primary-dark">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </Link>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </div>
            
            <div className="px-6 py-3 border-t border-gray-200 bg-gray-50 text-sm text-gray-500">
              Exibindo {filteredItems.length} de {inventoryItems.length} itens de estoque
            </div>
          </div>
        </div>
      </div>

      {/* Adjust Stock Dialog */}
      <Dialog open={showAdjustStockDialog} onOpenChange={setShowAdjustStockDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Ajustar Estoque</DialogTitle>
            <DialogDescription>
              {selectedItemId && (
                <>
                  Ajuste a quantidade de {inventoryItems.find(item => item.id === selectedItemId)?.name}.
                  Use valores positivos para adicionar e negativos para remover do estoque.
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitAdjustStock)} className="space-y-4">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ajuste de Quantidade</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="Digite a quantidade" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowAdjustStockDialog(false)}
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                  disabled={adjustStockMutation.isPending}
                >
                  {adjustStockMutation.isPending ? "Ajustando..." : "Atualizar Estoque"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
