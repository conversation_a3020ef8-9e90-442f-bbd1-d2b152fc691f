// Test appointment creation
const http = require('http');

const appointmentData = {
  title: 'Teste API',
  description: null,
  type: 'technical_visit',
  status: 'scheduled',
  appointmentDate: '2025-07-08T00:00:00.000Z',
  startTime: '14:00',
  endTime: '15:00',
  location: null,
  clientId: 1,
  technicianId: 1,
  serviceOrderId: null,
  contactPerson: null,
  contactPhone: null,
  contactEmail: null,
  notes: null,
  createdBy: 1,
  isRecurring: false,
  recurringPattern: null
};

const postData = JSON.stringify(appointmentData);

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/appointments',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', data);
    process.exit(0);
  });
});

req.on('error', (err) => {
  console.log('Error:', err.message);
  process.exit(1);
});

req.write(postData);
req.end();

console.log('Testing appointment creation...');