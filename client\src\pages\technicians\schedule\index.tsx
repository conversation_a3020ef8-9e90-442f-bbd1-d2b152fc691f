import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
// import TechnicianScheduleCalendar from "@/components/calendar/TechnicianScheduleCalendar";
import { 
  Card, 
  CardContent,
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Badge 
} from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Clock, Search, Plus, MoreVertical, Calendar as CalendarIcon, CalendarDays, List } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { TechnicianScheduleWithRelations, Technician, User } from "@/lib/types";
import { SCHEDULE_STATUSES } from "@/lib/constants";

export default function TechnicianScheduleList() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [technicianFilter, setTechnicianFilter] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState<string | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [currentTab, setCurrentTab] = useState("list");

  // Buscar todos os agendamentos
  const { data: schedules = [], isLoading: isLoadingSchedules } = useQuery<TechnicianScheduleWithRelations[]>({
    queryKey: ['/api/technician-schedules'],
  });

  // Buscar todos os técnicos para o filtro
  const { data: technicians = [], isLoading: isLoadingTechnicians } = useQuery<Technician[]>({
    queryKey: ['/api/technicians'],
  });
  
  // Buscar dados de usuários para exibir nomes dos técnicos
  const { data: users = [] } = useQuery<User[]>({
    queryKey: ['/api/users'],
  });

  // Filtrar os agendamentos
  const filteredSchedules = schedules.filter((schedule) => {
    // Filtro por termo de busca
    const searchMatch = searchTerm === "" || 
      schedule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schedule.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schedule.technician?.user?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schedule.client?.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Filtro por status
    const statusMatch = !statusFilter || schedule.status === statusFilter;
    
    // Filtro por técnico
    const technicianMatch = !technicianFilter || schedule.technicianId === technicianFilter;

    // Filtro por data
    const dateMatch = !dateFilter || schedule.scheduleDate === dateFilter;

    return searchMatch && statusMatch && technicianMatch && dateMatch;
  });

  function getStatusBadgeColor(status: string) {
    switch (status) {
      case 'scheduled':
        return "bg-blue-100 text-blue-800";
      case 'in_progress':
        return "bg-yellow-100 text-yellow-800";
      case 'completed':
        return "bg-green-100 text-green-800";
      case 'cancelled':
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  function getStatusLabel(status: string) {
    const statusObj = SCHEDULE_STATUSES.find(s => s.value === status);
    return statusObj ? statusObj.label : status;
  }

  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return format(date, "dd/MM/yyyy", { locale: ptBR });
  }

  function formatTime(timeString: string) {
    const [hours, minutes] = timeString.split(':');
    return `${hours}:${minutes}`;
  }

  const handleSelectDate = (date: string | null) => {
    setDateFilter(date);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Agendamentos de Técnicos" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-2 md:mb-0">Agendamentos</h3>
              <div className="flex flex-col md:flex-row gap-2">
                <Link href="/technicians/schedule/new">
                  <Button className="bg-primary hover:bg-primary-dark">
                    <Plus className="h-4 w-4 mr-1" />
                    Novo Agendamento
                  </Button>
                </Link>
              </div>
            </div>

            {/* View Tabs */}
            <div className="px-6 py-4 border-b border-gray-200">
              <Tabs value={currentTab} onValueChange={setCurrentTab}>
                <TabsList className="grid w-full grid-cols-2 max-w-md">
                  <TabsTrigger value="list" className="flex items-center gap-2">
                    <List className="w-4 h-4" />
                    Lista
                  </TabsTrigger>
                  <TabsTrigger value="calendar" className="flex items-center gap-2">
                    <CalendarDays className="w-4 h-4" />
                    Calendário
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            
            {/* Filters - Only show for list view */}
            {currentTab === "list" && (
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar agendamentos..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                
                <Select onValueChange={(value) => setTechnicianFilter(value ? parseInt(value) : null)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filtrar por técnico" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os técnicos</SelectItem>
                    {technicians.map((technician) => {
                      // Buscar o usuário correspondente para este técnico
                      const user = users.find(u => u.id === technician.userId);
                      return (
                        <SelectItem key={technician.id} value={technician.id.toString()}>
                          {user?.name || `Técnico #${technician.id}`}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                
                <Select onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filtrar por status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os status</SelectItem>
                    {SCHEDULE_STATUSES.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <div className="relative">
                  <Input
                    type="date"
                    className="pl-8"
                    value={dateFilter || ""}
                    onChange={(e) => handleSelectDate(e.target.value || null)}
                  />
                  <CalendarIcon className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                </div>
              </div>
            )}
            
            <div className="px-6 py-4">
              {isLoadingSchedules || isLoadingTechnicians ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : (
                <>
                  {/* List View */}
                  {currentTab === "list" && (
                    <>
                      {filteredSchedules.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          Nenhum agendamento encontrado.
                        </div>
                      ) : (
                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Título</TableHead>
                                <TableHead>Técnico</TableHead>
                                <TableHead>Cliente</TableHead>
                                <TableHead>Data</TableHead>
                                <TableHead>Horário</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Ações</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {filteredSchedules.map((schedule) => {
                                // Buscar o técnico e o usuário correspondente
                                const technician = technicians.find(t => t.id === schedule.technicianId);
                                const user = technician ? users.find(u => u.id === technician.userId) : null;
                                
                                return (
                                  <TableRow key={schedule.id} className="cursor-pointer hover:bg-gray-50" onClick={() => navigate(`/technicians/schedule/${schedule.id}`)}>
                                    <TableCell className="font-medium">{schedule.title}</TableCell>
                                    <TableCell>{user?.name || "-"}</TableCell>
                                    <TableCell>{schedule.client?.name || "Não especificado"}</TableCell>
                                    <TableCell className="whitespace-nowrap">
                                      <div className="flex items-center">
                                        <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                                        {formatDate(schedule.scheduleDate)}
                                      </div>
                                    </TableCell>
                                    <TableCell className="whitespace-nowrap">
                                      <div className="flex items-center">
                                        <Clock className="h-4 w-4 mr-2 text-gray-500" />
                                        {formatTime(schedule.startTime)} - {formatTime(schedule.endTime)}
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <Badge className={getStatusBadgeColor(schedule.status)}>
                                        {getStatusLabel(schedule.status)}
                                      </Badge>
                                    </TableCell>
                                    <TableCell className="text-right">
                                      <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                                            <MoreVertical className="h-4 w-4" />
                                          </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                          <DropdownMenuItem onClick={(e) => {
                                            e.stopPropagation();
                                            navigate(`/technicians/schedule/${schedule.id}`);
                                          }}>
                                            Ver detalhes
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={(e) => {
                                            e.stopPropagation();
                                            navigate(`/technicians/schedule/${schedule.id}/edit`);
                                          }}>
                                            Editar
                                          </DropdownMenuItem>
                                        </DropdownMenuContent>
                                      </DropdownMenu>
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        </div>
                      )}
                    </>
                  )}

                  {/* Calendar View */}
                  {currentTab === "calendar" && (
                    <div className="mt-4 text-center py-8 text-gray-500">
                      <CalendarIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Calendário em desenvolvimento</h3>
                      <p>A visualização em calendário para técnicos estará disponível em breve.</p>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}