import { useEffect, useRef, useState } from "react";
import { formatDateTime, formatCurrency } from "@/lib/utils";
import { ServiceOrder, Client, Equipment, Technician, User, ServiceOrderItem } from "@/lib/types";

interface PrintServiceOrderProps {
  serviceOrder: ServiceOrder;
  client?: Client;
  equipment?: Equipment;
  technician?: Technician;
  technicianUser?: User;
  orderItems: ServiceOrderItem[];
  totalAmount: number;
  onPrintComplete: () => void;
}

export function PrintServiceOrder({
  serviceOrder,
  client,
  equipment,
  technician,
  technicianUser,
  orderItems,
  totalAmount: inputTotalAmount,
  onPrintComplete
}: PrintServiceOrderProps) {
  const printRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    // Aguardar mais tempo em dispositivos móveis para garantir renderização completa
    const isMobile = window.innerWidth <= 768;
    const delay = isMobile ? 2000 : 500; // 2 segundos no mobile, 500ms no desktop

    const timeoutId = setTimeout(() => {
      if (printRef.current) {
        // Em dispositivos móveis, não chamar automaticamente o print
        if (isMobile) {
          // Apenas manter a visualização aberta para o usuário decidir
          console.log('Visualização de impressão carregada - pronta para imprimir');
        } else {
          window.print();
          onPrintComplete();
        }
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [onPrintComplete]);

  // Formatar a data atual para exibição
  const currentDate = new Date().toLocaleDateString('pt-BR');

  // Calcular o total com base nos itens existentes
  const calculatedTotal = orderItems.reduce(
    (total, item) => total + (item.unitPrice * item.quantity), 
    0
  );

  // Usar o total calculado a partir dos itens ou o valor passado por props
  const totalAmount = calculatedTotal || inputTotalAmount;

  const handleManualPrint = () => {
    try {
      // Aguardar um momento para garantir que o conteúdo está renderizado
      setTimeout(() => {
        window.print();
      }, 100);
    } catch (error) {
      console.error('Erro ao tentar imprimir:', error);
      alert('Não foi possível imprimir. Tente usar o menu do navegador > Imprimir');
    }
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Controles para dispositivos móveis */}
      {isMobile && (
        <div className="bg-gray-100 p-4 border-b no-print sticky top-0 z-10">
          <div className="flex justify-between items-center max-w-4xl mx-auto">
            <h2 className="text-lg font-semibold">Visualizar Impressão</h2>
            <div className="flex gap-2">
              <button
                onClick={handleManualPrint}
                className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
              >
                📄 Imprimir
              </button>
              <button
                onClick={onPrintComplete}
                className="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700"
              >
                ✕ Fechar
              </button>
            </div>
          </div>
        </div>
      )}

      <div
        id="print-content"
        ref={printRef}
        className="bg-white p-4"
        style={{ 
          fontFamily: 'Arial, sans-serif',
          maxWidth: '210mm',  // Largura A4
          margin: '0 auto'
        }}
      >
      <style type="text/css">
        {`
          @page {
            size: A4 portrait;
            margin: 0.5cm;
          }

          @media print {
            body * {
              visibility: hidden;
            }

            #print-content, #print-content * {
              visibility: visible;
            }

            #print-content {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
            }

            .no-print {
              display: none !important;
            }

            * {
              box-sizing: border-box;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }

            table {
              width: 100% !important;
            }

            .bg-gray-200,
            .bg-gray-300,
            .bg-gray-100 {
              background-color: #f3f4f6 !important;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
          }
        `}
      </style>

      {/* Cabeçalho da empresa */}
      <div className="flex items-center p-3 bg-gray-200 rounded-sm mb-3">
        <div className="flex-shrink-0 mr-3">
          <div className="bg-blue-800 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold">
            SM
          </div>
        </div>
        <div className="flex-grow">
          <h1 className="text-base font-bold">SimplesmedMed Produtos e Serviços em Equipamento Hospitalar</h1>
          <p className="text-xs">Avenida Carneiro da Cunha, 212</p>
          <p className="text-xs">Torre, João Pessoa-PB</p>
          <p className="text-xs">CEP 58040-240</p>
        </div>
        <div className="text-right text-xs">
          <p className="flex items-center justify-end mb-1">
            <span className="mr-1">📧</span> <EMAIL>
          </p>
          <p className="flex items-center justify-end mb-1">
            <span className="mr-1">📱</span> (83) 98845-2049
          </p>
          <p className="flex items-center justify-end">
            <span className="mr-1">☎️</span> (83) 3021-6746
          </p>
          <p className="mt-1">{currentDate}</p>
        </div>
      </div>

      {/* Número da ordem de serviço */}
      <div className="bg-gray-300 py-1 px-3 my-3 rounded-sm">
        <h2 className="font-bold text-sm">Ordem de serviço {serviceOrder.orderNumber}</h2>
      </div>

      {/* Informações do cliente */}
      <div className="mb-3">
        <p className="font-bold text-sm">Cliente: {client?.name}</p>
        <p className="flex items-center text-xs">
          <span className="mr-1">📱</span> {client?.phone}
        </p>
      </div>

      {/* Informações básicas */}
      <div className="mb-3">
        <h3 className="font-bold text-sm mb-1">Informações básicas</h3>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <p className="font-bold">Marca</p>
            <p>{equipment?.brand}</p>
          </div>
          <div>
            <p className="font-bold">Equipamento</p>
            <p>{equipment?.model}</p>
          </div>
          <div>
            <p className="font-bold">Número de série</p>
            <p>{equipment?.serialNumber}</p>
          </div>
          <div>
            <p className="font-bold">Defeito</p>
            <p>{serviceOrder.description}</p>
          </div>
        </div>
      </div>

      {/* Serviços */}
      <div className="mb-3">
        <h3 className="font-bold text-sm mb-1">Serviços</h3>
        <table className="w-full border-collapse text-xs">
          <thead>
            <tr className="bg-gray-100">
              <th className="py-1 px-1 text-left" style={{ width: '40%' }}>Descrição</th>
              <th className="py-1 px-1 text-left" style={{ width: '20%' }}>Unidade</th>
              <th className="py-1 px-1 text-right" style={{ width: '15%' }}>Preço unitário</th>
              <th className="py-1 px-1 text-center" style={{ width: '10%' }}>Qtd.</th>
              <th className="py-1 px-1 text-right" style={{ width: '15%' }}>Preço</th>
            </tr>
          </thead>
          <tbody>
            {orderItems.length === 0 ? (
              <tr>
                <td colSpan={5} className="py-1 px-1 text-center text-gray-500">Nenhum serviço adicionado</td>
              </tr>
            ) : (
              orderItems.map((item, idx) => (
                <tr key={idx} className="border-b border-gray-200">
                  <td className="py-1 px-1">{item.description}</td>
                  <td className="py-1 px-1">Serviço</td>
                  <td className="py-1 px-1 text-right">{formatCurrency(item.unitPrice)}</td>
                  <td className="py-1 px-1 text-center">{item.quantity}</td>
                  <td className="py-1 px-1 text-right">{formatCurrency(item.unitPrice * item.quantity)}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Peças - Mantemos a seção mesmo que vazia para manter o formato padrão */}
      <div className="mb-3">
        <h3 className="font-bold text-sm mb-1">Peças</h3>
        <table className="w-full border-collapse text-xs">
          <thead>
            <tr className="bg-gray-100">
              <th className="py-1 px-1 text-left" style={{ width: '40%' }}>Descrição</th>
              <th className="py-1 px-1 text-left" style={{ width: '20%' }}>Unidade</th>
              <th className="py-1 px-1 text-right" style={{ width: '15%' }}>Preço unitário</th>
              <th className="py-1 px-1 text-center" style={{ width: '10%' }}>Qtd.</th>
              <th className="py-1 px-1 text-right" style={{ width: '15%' }}>Preço</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={5} className="py-1 px-1 text-center text-gray-500">Nenhuma peça adicionada</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Resumo financeiro */}
      <div className="mb-3">
        <div className="text-right">
          <table className="ml-auto text-xs">
            <tbody>
              <tr>
                <td className="pr-4 py-1 font-bold">Serviços:</td>
                <td className="text-right py-1">{formatCurrency(totalAmount)}</td>
              </tr>
              <tr>
                <td className="pr-4 py-1 font-bold">Peças:</td>
                <td className="text-right py-1">{formatCurrency(0)}</td>
              </tr>
              <tr className="border-t border-gray-300">
                <td className="pr-4 py-1 font-bold">Total:</td>
                <td className="text-right font-bold py-1">{formatCurrency(totalAmount)}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagamento */}
      <div className="mb-3">
        <h3 className="font-bold text-sm mb-1">Pagamento</h3>
        <p className="text-xs">Meios de pagamento</p>
        <p className="text-xs">Transferência bancária, dinheiro, cartão de crédito, cartão de débito ou pix.</p>
      </div>

      {/* Assinaturas */}
      <div className="mt-8 pt-4">
        <div className="flex justify-between">
          <div className="w-5/12">
            <div className="border-t border-black pt-1">
              <p className="text-center text-xs">Simplesmed Produtos e Serviços em Equipamento Hospitalar</p>
            </div>
          </div>
          <div className="w-5/12">
            <div className="border-t border-black pt-1">
              <p className="text-center text-xs">{client?.name}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Rodapé */}
      <div className="mt-4 text-center text-xs text-gray-500">
        <p>João Pessoa, {currentDate}</p>
        <p className="mt-1">Página 1/1</p>
      </div>
      </div>
    </div>
  );
}

// Função auxiliar para status
function getStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    'received': 'Recebido',
    'analysis': 'Em Análise',
    'waiting_approval': 'Aguardando Aprovação',
    'approved': 'Aprovado',
    'in_progress': 'Em Progresso',
    'completed': 'Concluído',
    'delivered': 'Entregue',
    'cancelled': 'Cancelado'
  };

  return statusMap[status] || status.charAt(0).toUpperCase() + status.slice(1);
}