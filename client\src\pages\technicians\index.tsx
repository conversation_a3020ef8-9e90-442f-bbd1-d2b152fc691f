import { useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Technician, User } from "@/lib/types";
import { Eye, Plus, Search, ArrowUpDown } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { TECHNICIAN_STATUSES } from "@/lib/constants";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

export default function TechniciansPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [selectedTechnicianId, setSelectedTechnicianId] = useState<number | null>(null);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: technicians = [], isLoading: isLoadingTechnicians } = useQuery<Technician[]>({
    queryKey: ['/api/technicians'],
  });

  const { data: users = [], isLoading: isLoadingUsers } = useQuery<User[]>({
    queryKey: ['/api/users'],
  });

  const isLoading = isLoadingTechnicians || isLoadingUsers;

  // Schema for status update
  const updateStatusSchema = z.object({
    status: z.enum(['available', 'on_service', 'off_duty']),
  });

  type UpdateStatusFormValues = z.infer<typeof updateStatusSchema>;

  const form = useForm<UpdateStatusFormValues>({
    resolver: zodResolver(updateStatusSchema),
    defaultValues: {
      status: 'available',
    },
  });

  // Get technician with user information
  const getTechnicianWithUser = (technician: Technician) => {
    const user = users.find(u => u.id === technician.userId);
    return {
      ...technician,
      user
    };
  };

  // Filter technicians
  const filteredTechnicians = technicians.filter(technician => {
    const user = users.find(u => u.id === technician.userId);
    if (!user) return false;

    const matchesSearch = 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.phone || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (technician.specialties || '').toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = 
      statusFilter === "all" || 
      technician.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Sort technicians
  const sortedTechnicians = [...filteredTechnicians].sort((a, b) => {
    const userA = users.find(u => u.id === a.userId);
    const userB = users.find(u => u.id === b.userId);
    
    if (!userA || !userB) return 0;
    
    let result = 0;
    
    switch (sortBy) {
      case "name":
        result = userA.name.localeCompare(userB.name);
        break;
      case "email":
        result = userA.email.localeCompare(userB.email);
        break;
      case "status":
        result = a.status.localeCompare(b.status);
        break;
      case "specialties":
        result = (a.specialties || '').localeCompare(b.specialties || '');
        break;
      default:
        result = userA.name.localeCompare(userB.name);
    }
    
    return sortOrder === "asc" ? result : -result;
  });

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  // Update technician status
  const openStatusDialog = (technicianId: number) => {
    const technician = technicians.find(t => t.id === technicianId);
    if (technician) {
      form.reset({ status: technician.status as 'available' | 'on_service' | 'off_duty' });
      setSelectedTechnicianId(technicianId);
      setShowStatusDialog(true);
    }
  };

  const updateStatusMutation = useMutation({
    mutationFn: async (data: UpdateStatusFormValues) => {
      if (selectedTechnicianId === null) return null;
      return apiRequest("PATCH", `/api/technicians/${selectedTechnicianId}`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/technicians'] });
      toast({
        title: "Status updated",
        description: "Technician status has been updated successfully",
      });
      setShowStatusDialog(false);
      setSelectedTechnicianId(null);
    },
    onError: (error) => {
      toast({
        title: "Error updating status",
        description: error.message || "Something went wrong",
        variant: "destructive",
      });
    },
  });

  const onSubmitUpdateStatus = (values: UpdateStatusFormValues) => {
    updateStatusMutation.mutate(values);
  };

  const getStatusLabel = (status: string) => {
    return TECHNICIAN_STATUSES.find(s => s.value === status)?.label || status;
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'available':
        return "bg-green-100 text-green-800";
      case 'on_service':
        return "bg-yellow-100 text-yellow-800";
      case 'off_duty':
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Técnicos" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-2 md:mb-0">Gestão de Técnicos</h3>
              <div className="flex flex-col md:flex-row gap-2">
                <Link href="/technicians/new">
                  <Button className="bg-primary hover:bg-primary-dark">
                    <Plus className="h-4 w-4 mr-1" />
                    Adicionar Técnico
                  </Button>
                </Link>
              </div>
            </div>
            
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar técnicos..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filtrar por status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Status</SelectItem>
                  {TECHNICIAN_STATUSES.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Nome</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                  <SelectItem value="specialties">Especialidades</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="overflow-x-auto">
              {isLoading ? (
                <div className="p-8 flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : filteredTechnicians.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  Nenhum técnico encontrado. Adicione seu primeiro técnico!
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[200px]">
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("name");
                            toggleSortOrder();
                          }}
                        >
                          Nome
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("email");
                            toggleSortOrder();
                          }}
                        >
                          Email
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>Telefone</TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("specialties");
                            toggleSortOrder();
                          }}
                        >
                          Especialidades
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("status");
                            toggleSortOrder();
                          }}
                        >
                          Status
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedTechnicians.map((technician) => {
                      const technicianWithUser = getTechnicianWithUser(technician);
                      if (!technicianWithUser.user) return null;
                      
                      return (
                        <TableRow key={technician.id} className="hover:bg-gray-50">
                          <TableCell className="font-medium">{technicianWithUser.user.name}</TableCell>
                          <TableCell>{technicianWithUser.user.email}</TableCell>
                          <TableCell>{technicianWithUser.user.phone || '-'}</TableCell>
                          <TableCell>{technician.specialties || '-'}</TableCell>
                          <TableCell>
                            <Badge className={getStatusBadgeColor(technician.status)}>
                              {getStatusLabel(technician.status)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                onClick={() => openStatusDialog(technician.id)}
                              >
                                Atualizar Status
                              </Button>
                              <Link href={`/technicians/${technician.id}`}>
                                <Button variant="ghost" size="sm" className="text-primary hover:text-primary-dark">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </Link>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </div>
            
            <div className="px-6 py-3 border-t border-gray-200 bg-gray-50 text-sm text-gray-500">
              Mostrando {filteredTechnicians.length} de {technicians.length} técnicos
            </div>
          </div>
        </div>
      </div>

      {/* Update Status Dialog */}
      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Atualizar Status do Técnico</DialogTitle>
            <DialogDescription>
              {selectedTechnicianId && (
                <>
                  Atualizar o status de {
                    technicians.find(t => t.id === selectedTechnicianId)?.userId &&
                    users.find(u => u.id === technicians.find(t => t.id === selectedTechnicianId)?.userId)?.name
                  }
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitUpdateStatus)} className="space-y-4">
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecionar status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TECHNICIAN_STATUSES.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowStatusDialog(false)}
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                  disabled={updateStatusMutation.isPending}
                >
                  {updateStatusMutation.isPending ? "Atualizando..." : "Atualizar Status"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
