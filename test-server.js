// Test script to check if server starts correctly
const http = require('http');

// Test if server is running
function testServer() {
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/appointments/test',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Response:', data);
      process.exit(0);
    });
  });

  req.on('error', (err) => {
    console.log('Server not running or error:', err.message);
    process.exit(1);
  });

  req.end();
}

// Wait 2 seconds then test
setTimeout(testServer, 2000);
console.log('Testing server in 2 seconds...');