import { useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Quote, Client, ServiceOrder } from "@/lib/types";
import { Plus, Search, ArrowUpDown, Eye, FileCheck, FileX } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

export default function Quotes() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<string>("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: quotes = [], isLoading: isLoadingQuotes } = useQuery<Quote[]>({
    queryKey: ['/api/quotes'],
  });

  const { data: clients = [], isLoading: isLoadingClients } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  const { data: serviceOrders = [], isLoading: isLoadingServiceOrders } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
  });

  const isLoading = isLoadingQuotes || isLoadingClients || isLoadingServiceOrders;

  // Get client name by ID
  const getClientName = (clientId: number) => {
    const client = clients.find(c => c.id === clientId);
    return client ? client.name : 'Cliente não encontrado';
  };

  // Get service order number by ID
  const getServiceOrderNumber = (serviceOrderId?: number) => {
    if (!serviceOrderId) return 'N/A';
    const serviceOrder = serviceOrders.find(so => so.id === serviceOrderId);
    return serviceOrder ? serviceOrder.orderNumber : 'OS não encontrada';
  };

  // Approve quote mutation
  const approveQuoteMutation = useMutation({
    mutationFn: async (quoteId: number) => {
      return apiRequest("PATCH", `/api/quotes/${quoteId}/approve`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/quotes'] });
      toast({
        title: "Orçamento aprovado",
        description: "O orçamento foi aprovado com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao aprovar orçamento",
        description: error.message || "Ocorreu um erro ao aprovar o orçamento",
        variant: "destructive",
      });
    },
  });

  // Reject quote mutation
  const rejectQuoteMutation = useMutation({
    mutationFn: async (quoteId: number) => {
      return apiRequest("PATCH", `/api/quotes/${quoteId}/reject`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/quotes'] });
      toast({
        title: "Orçamento rejeitado",
        description: "O orçamento foi rejeitado com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao rejeitar orçamento",
        description: error.message || "Ocorreu um erro ao rejeitar o orçamento",
        variant: "destructive",
      });
    },
  });

  // Filter quotes
  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch =
      quote.quoteNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      getClientName(quote.clientId).toLowerCase().includes(searchTerm.toLowerCase()) ||
      (quote.notes || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || 
      quote.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Sort quotes
  const sortedQuotes = [...filteredQuotes].sort((a, b) => {
    let result = 0;

    switch (sortBy) {
      case "quoteNumber":
        result = a.quoteNumber.localeCompare(b.quoteNumber);
        break;
      case "clientName":
        result = getClientName(a.clientId).localeCompare(getClientName(b.clientId));
        break;
      case "total":
        result = (a.totalAmount || 0) - (b.totalAmount || 0);
        break;
      case "status":
        result = a.status.localeCompare(b.status);
        break;
      case "createdAt":
        result = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      case "validUntil":
        const dateA = a.validUntil ? new Date(a.validUntil).getTime() : 0;
        const dateB = b.validUntil ? new Date(b.validUntil).getTime() : 0;
        result = dateA - dateB;
        break;
      default:
        result = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    }

    return sortOrder === "asc" ? result : -result;
  });

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return "bg-yellow-100 text-yellow-800";
      case 'approved':
        return "bg-green-100 text-green-800";
      case 'rejected':
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return "Pendente";
      case 'approved':
        return "Aprovado";
      case 'rejected':
        return "Rejeitado";
      default:
        return status;
    }
  };

  const isExpired = (validUntil?: string) => {
    if (!validUntil) return false;
    return new Date(validUntil) < new Date();
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Orçamentos" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-2 md:mb-0">Gestão de Orçamentos</h3>
              <div className="flex flex-col md:flex-row gap-2">
                <Link href="/quotes/new">
                  <Button className="bg-primary hover:bg-primary-dark">
                    <Plus className="h-4 w-4 mr-1" />
                    Novo Orçamento
                  </Button>
                </Link>
              </div>
            </div>
            
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar orçamentos..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filtrar por status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Status</SelectItem>
                  <SelectItem value="pending">Pendentes</SelectItem>
                  <SelectItem value="approved">Aprovados</SelectItem>
                  <SelectItem value="rejected">Rejeitados</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt">Data de Criação</SelectItem>
                  <SelectItem value="quoteNumber">Número</SelectItem>
                  <SelectItem value="clientName">Cliente</SelectItem>
                  <SelectItem value="total">Valor Total</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                  <SelectItem value="validUntil">Validade</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="overflow-x-auto">
              {isLoading ? (
                <div className="p-8 flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : filteredQuotes.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  Nenhum orçamento encontrado. Crie seu primeiro orçamento!
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[150px]">
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("quoteNumber");
                            toggleSortOrder();
                          }}
                        >
                          Número
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("clientName");
                            toggleSortOrder();
                          }}
                        >
                          Cliente
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>Ordem de Serviço</TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("createdAt");
                            toggleSortOrder();
                          }}
                        >
                          Data
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("validUntil");
                            toggleSortOrder();
                          }}
                        >
                          Validade
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("total");
                            toggleSortOrder();
                          }}
                        >
                          Total
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("status");
                            toggleSortOrder();
                          }}
                        >
                          Status
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedQuotes.map((quote) => (
                      <TableRow key={quote.id} className="hover:bg-gray-50">
                        <TableCell className="font-medium">{quote.quoteNumber}</TableCell>
                        <TableCell>{getClientName(quote.clientId)}</TableCell>
                        <TableCell>{getServiceOrderNumber(quote.serviceOrderId)}</TableCell>
                        <TableCell>{formatDate(quote.createdAt)}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {isExpired(quote.validUntil) && quote.status === 'pending' && (
                              <Badge className="bg-red-100 text-red-800 mr-2">
                                Expirado
                              </Badge>
                            )}
                            {formatDate(quote.validUntil)}
                          </div>
                        </TableCell>
                        <TableCell>{formatCurrency(quote.total || 0)}</TableCell>
                        <TableCell>
                          <Badge className={getStatusBadgeColor(quote.status)}>
                            {getStatusLabel(quote.status)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {quote.status === 'pending' && (
                              <>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="text-green-600 hover:text-green-800"
                                  onClick={() => approveQuoteMutation.mutate(quote.id)}
                                  disabled={approveQuoteMutation.isPending}
                                >
                                  <FileCheck className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="text-red-600 hover:text-red-800"
                                  onClick={() => rejectQuoteMutation.mutate(quote.id)}
                                  disabled={rejectQuoteMutation.isPending}
                                >
                                  <FileX className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                            <Link href={`/quotes/${quote.id}`}>
                              <Button variant="ghost" size="sm" className="text-primary hover:text-primary-dark">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
            
            <div className="px-6 py-3 border-t border-gray-200 bg-gray-50 text-sm text-gray-500">
              Exibindo {filteredQuotes.length} de {quotes.length} orçamentos
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}