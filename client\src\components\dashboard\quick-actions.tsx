import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Link } from "wouter";
import { 
  Plus, 
  ClipboardList, 
  Users, 
  Laptop, 
  FileText,
  Clock,
  AlertTriangle,
  TrendingUp,
  Calculator,
  Calendar
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { ServiceOrder } from "@/lib/types";

export default function QuickActions() {
  const { data: serviceOrders = [] } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
  });

  // Estatísticas rápidas
  const urgentOrders = serviceOrders.filter(order => 
    order.status === 'received' || order.status === 'in_progress'
  ).length;

  const pendingDiagnosis = serviceOrders.filter(order => 
    order.status === 'received' && !order.diagnostics
  ).length;

  const readyForDelivery = serviceOrders.filter(order => 
    order.status === 'completed'
  ).length;

  const actions = [
    {
      title: "Nova Ordem de Serviço",
      description: "Criar uma nova ordem",
      href: "/service-orders/new",
      icon: Plus,
      color: "bg-blue-500 hover:bg-blue-600",
      urgent: false
    },
    {
      title: "Novo Cliente",
      description: "Cadastrar cliente",
      href: "/clients/new",
      icon: Users,
      color: "bg-green-500 hover:bg-green-600",
      urgent: false
    },
    {
      title: "Novo Equipamento",
      description: "Registrar equipamento",
      href: "/equipment/new",
      icon: Laptop,
      color: "bg-purple-500 hover:bg-purple-600",
      urgent: false
    },
    {
      title: "Gerar Relatório",
      description: "Relatórios do sistema",
      href: "/reports",
      icon: FileText,
      color: "bg-orange-500 hover:bg-orange-600",
      urgent: false
    },
    {
      title: "Gerar Orçamento",
      description: "Criar novo orçamento",
      href: "/quotes/new",
      icon: Calculator,
      color: "bg-indigo-500 hover:bg-indigo-600",
      urgent: false
    },
    {
      title: "Agendamento",
      description: "Agendar serviço",
      href: "/appointments/new",
      icon: Calendar,
      color: "bg-teal-500 hover:bg-teal-600",
      urgent: false
    }
  ];

  const urgentActions = [
    {
      title: "Ordens Urgentes",
      description: `${urgentOrders} ordens precisam de atenção`,
      href: "/service-orders?filter=urgent",
      icon: AlertTriangle,
      color: "bg-red-500 hover:bg-red-600",
      count: urgentOrders,
      urgent: true
    },
    {
      title: "Aguardando Diagnóstico",
      description: `${pendingDiagnosis} ordens sem diagnóstico`,
      href: "/service-orders?filter=pending-diagnosis",
      icon: Clock,
      color: "bg-yellow-500 hover:bg-yellow-600",
      count: pendingDiagnosis,
      urgent: true
    },
    {
      title: "Prontas para Entrega",
      description: `${readyForDelivery} ordens concluídas`,
      href: "/service-orders?filter=ready-delivery",
      icon: TrendingUp,
      color: "bg-emerald-500 hover:bg-emerald-600",
      count: readyForDelivery,
      urgent: true
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <ClipboardList className="mr-2 h-5 w-5" />
          Ações Rápidas
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Ações Urgentes */}
        {urgentActions.some(action => action.count > 0) && (
          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-3">
              Requer Atenção
            </h4>
            <div className="grid grid-cols-1 gap-2">
              {urgentActions.map((action, index) => (
                action.count > 0 && (
                  <Link key={index} href={action.href}>
                    <Button
                      variant="outline"
                      className={`w-full justify-start h-auto p-3 ${action.color} text-white border-0`}
                    >
                      <div className="flex items-center w-full">
                        <action.icon className="h-4 w-4 mr-3" />
                        <div className="flex-1 text-left">
                          <div className="font-medium">{action.title}</div>
                          <div className="text-xs opacity-90">{action.description}</div>
                        </div>
                        <div className="bg-white/20 px-2 py-1 rounded text-xs font-bold">
                          {action.count}
                        </div>
                      </div>
                    </Button>
                  </Link>
                )
              ))}
            </div>
          </div>
        )}

        {/* Ações Gerais */}
        <div>
          <h4 className="text-sm font-medium text-muted-foreground mb-3">
            Criar Novo
          </h4>
          <div className="grid grid-cols-2 gap-2">
            {actions.map((action, index) => (
              <Link key={index} href={action.href}>
                <Button
                  variant="outline"
                  className={`w-full justify-start h-auto p-3 ${action.color} text-white border-0`}
                >
                  <div className="text-center w-full">
                    <action.icon className="h-5 w-5 mx-auto mb-1" />
                    <div className="text-xs font-medium">{action.title}</div>
                  </div>
                </Button>
              </Link>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}