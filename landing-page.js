document.addEventListener('DOMContentLoaded', function() {
    // Referências aos elementos DOM
    const header = document.getElementById('header');
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');
    const dots = document.querySelectorAll('.dot');
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    
    // ===== Menu Móvel Toggle =====
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
        
        // Animar as barras do hamburger para formar um X
        const bars = hamburger.querySelectorAll('.bar');
        if (hamburger.classList.contains('active')) {
            bars[0].style.transform = 'translateY(9px) rotate(45deg)';
            bars[1].style.opacity = '0';
            bars[2].style.transform = 'translateY(-9px) rotate(-45deg)';
        } else {
            bars[0].style.transform = 'none';
            bars[1].style.opacity = '1';
            bars[2].style.transform = 'none';
        }
    });
    
    // Fechar o menu ao clicar em um link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
            
            const bars = hamburger.querySelectorAll('.bar');
            bars[0].style.transform = 'none';
            bars[1].style.opacity = '1';
            bars[2].style.transform = 'none';
        });
    });
    
    // ===== Rolagem Suave para Âncoras =====
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return; // Ignorar links "#" sem destino
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                const headerHeight = header.offsetHeight;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                
                window.scrollTo({
                    top: targetPosition - headerHeight,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // ===== Efeito de Header ao Rolar =====
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
    
    // ===== Carrossel de Depoimentos =====
    let currentSlide = 0;
    
    // Função para mudar o slide
    function changeSlide(index) {
        // Resetar todos os slides
        testimonialCards.forEach(card => {
            card.style.opacity = '0.4';
            card.style.transform = 'scale(0.8)';
        });
        
        // Resetar todos os dots
        dots.forEach(dot => {
            dot.classList.remove('active');
        });
        
        // Ativar o slide atual
        testimonialCards[index].style.opacity = '1';
        testimonialCards[index].style.transform = 'scale(1)';
        dots[index].classList.add('active');
        
        currentSlide = index;
    }
    
    // Configurar os dots para o carrossel
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            changeSlide(index);
        });
    });
    
    // Inicializar o primeiro slide
    changeSlide(0);
    
    // Mudança automática de slides a cada 5 segundos
    setInterval(() => {
        currentSlide = (currentSlide + 1) % testimonialCards.length;
        changeSlide(currentSlide);
    }, 5000);
    
    // ===== Animações com Intersection Observer =====
    // Função para animar elementos quando entrarem no viewport
    const animateOnScroll = function() {
        const sections = document.querySelectorAll('section');
        const featureCards = document.querySelectorAll('.feature-card');
        const benefitItems = document.querySelectorAll('.benefit-item');
        const pricingCards = document.querySelectorAll('.pricing-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1
        });
        
        sections.forEach(section => {
            section.classList.add('section-hidden');
            observer.observe(section);
        });
        
        featureCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = `opacity 0.5s ease, transform 0.5s ease ${index * 0.1}s`;
            
            observer.observe(card);
        });
        
        benefitItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            item.style.transition = `opacity 0.5s ease, transform 0.5s ease ${index * 0.1}s`;
            
            observer.observe(item);
        });
        
        pricingCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = `opacity 0.5s ease, transform 0.5s ease ${index * 0.1}s`;
            
            observer.observe(card);
        });
    };
    
    // Adicionar estilo CSS para animações
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .section-hidden {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }
        section.animate {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-card.animate, .benefit-item.animate, .pricing-card.animate {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
    `;
    document.head.appendChild(styleElement);
    
    // Iniciar animações
    animateOnScroll();
    
    // ===== Funcionalidade para o formulário de contato =====
    // Se houver um formulário de contato, adicionar funcionalidade
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Aqui você adicionaria a lógica de envio do formulário
            // Como é apenas uma demonstração, vamos simular um sucesso
            
            const formElements = contactForm.elements;
            let allFilled = true;
            
            // Verificar se todos os campos obrigatórios estão preenchidos
            for (let i = 0; i < formElements.length; i++) {
                if (formElements[i].required && !formElements[i].value) {
                    allFilled = false;
                    formElements[i].classList.add('error');
                } else {
                    formElements[i].classList.remove('error');
                }
            }
            
            if (allFilled) {
                // Simular envio com sucesso
                contactForm.innerHTML = '<div class="success-message"><i class="fas fa-check-circle"></i><h3>Mensagem Enviada!</h3><p>Agradecemos seu contato. Retornaremos em breve.</p></div>';
            }
        });
    }
});