import { useState } from "react";
import { use<PERSON>ara<PERSON>, useLocation } from "wouter";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { PrintServiceOrder } from "@/components/print/print-service-order";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  ServiceOrder,
  Client,
  Equipment,
  Technician,
  User,
  ServiceOrderItem,
  InventoryItem,
} from "@/lib/types";
import { SERVICE_ORDER_STATUSES } from "@/lib/constants";
import {
  formatDateTime,
  getStatusColor,
  getStatusLabel,
  formatCurrency,
  calculateTotal,
} from "@/lib/utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronDown,
  Clock,
  Download,
  Edit,
  FileText,
  MoreHorizontal,
  Plus,
  Printer,
  Share,
  Trash,
  X,
  MessageCircle,
  Mail,
  Copy,
} from "lucide-react";

// Define the schema for status update
const statusUpdateSchema = z.object({
  status: z.enum(SERVICE_ORDER_STATUSES.map(s => s.value) as [string, ...string[]]),
});

// Define the schema for adding an item to the service order
const addItemSchema = z.object({
  inventoryItemId: z.coerce.number().optional(),
  description: z.string().min(3, "Description is required"),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
  unitPrice: z.coerce.number().min(0, "Price must be at least 0"),
});

export default function ServiceOrderDetails() {
  const params = useParams<{ id: string }>();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const serviceOrderId = parseInt(params.id);

  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAddItemDialog, setShowAddItemDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [shareEmail, setShareEmail] = useState("");

  // Load service order details
  const {
    data: serviceOrder,
    isLoading: isLoadingOrder,
    isError,
    error
  } = useQuery<ServiceOrder>({
    queryKey: [`/api/service-orders/${serviceOrderId}`],
    enabled: !isNaN(serviceOrderId),
  });

  // Load client details
  const { data: clients = [] } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  // Load equipment details
  const { data: equipmentList = [] } = useQuery<Equipment[]>({
    queryKey: ['/api/equipment'],
  });

  // Load technicians
  const { data: technicians = [] } = useQuery<Technician[]>({
    queryKey: ['/api/technicians'],
  });

  // Load users for technician names
  const { data: users = [] } = useQuery<User[]>({
    queryKey: ['/api/users'],
  });

  // Load service order items
  const { data: orderItems = [] } = useQuery<ServiceOrderItem[]>({
    queryKey: [`/api/service-orders/${serviceOrderId}/items`],
    enabled: !isNaN(serviceOrderId),
  });

  // Load inventory items for adding to the order
  const { data: inventoryItems = [] } = useQuery<InventoryItem[]>({
    queryKey: ['/api/inventory'],
  });

  // Form for status update
  const statusForm = useForm<z.infer<typeof statusUpdateSchema>>({
    resolver: zodResolver(statusUpdateSchema),
    defaultValues: {
      status: serviceOrder?.status || "received",
    },
  });

  // Form for adding items
  const addItemForm = useForm<z.infer<typeof addItemSchema>>({
    resolver: zodResolver(addItemSchema),
    defaultValues: {
      description: "",
      quantity: 1,
      unitPrice: 0,
    },
  });

  // Watch inventory item selection to auto-fill details
  const watchedInventoryItemId = addItemForm.watch("inventoryItemId");

  // When inventory item is selected, auto-fill details
  useState(() => {
    if (watchedInventoryItemId) {
      const item = inventoryItems.find(i => i.id === Number(watchedInventoryItemId));
      if (item) {
        addItemForm.setValue("description", item.name);
        if (item.price) {
          addItemForm.setValue("unitPrice", item.price);
        }
      }
    }
  });

  // Mutation for updating service order status
  const updateStatusMutation = useMutation({
    mutationFn: async (data: z.infer<typeof statusUpdateSchema>) => {
      return apiRequest("PATCH", `/api/service-orders/${serviceOrderId}/status`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/service-orders/${serviceOrderId}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/service-orders'] });
      toast({
        title: "Status updated",
        description: "The service order status has been updated successfully",
      });
      setShowStatusDialog(false);
    },
    onError: (error) => {
      toast({
        title: "Error updating status",
        description: error.message || "Something went wrong",
        variant: "destructive",
      });
    },
  });

  // Mutation for adding an item to the service order
  const addItemMutation = useMutation({
    mutationFn: async (data: z.infer<typeof addItemSchema>) => {
      return apiRequest("POST", `/api/service-orders/${serviceOrderId}/items`, {
        ...data,
        serviceOrderId,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/service-orders/${serviceOrderId}/items`] });
      toast({
        title: "Item added",
        description: "The item has been added to the service order",
      });
      setShowAddItemDialog(false);
      addItemForm.reset({
        description: "",
        quantity: 1,
        unitPrice: 0,
      });
    },
    onError: (error) => {
      toast({
        title: "Error adding item",
        description: error.message || "Something went wrong",
        variant: "destructive",
      });
    },
  });

  const onSubmitStatusUpdate = (values: z.infer<typeof statusUpdateSchema>) => {
    updateStatusMutation.mutate(values);
  };

  const onSubmitAddItem = (values: z.infer<typeof addItemSchema>) => {
    addItemMutation.mutate(values);
  };

  // Estados para controlar a exibição dos diálogos
  const [showPrintView, setShowPrintView] = useState(false);
  const [showQuoteDialog, setShowQuoteDialog] = useState(false);

  // Schema para formulário de orçamento
  const quoteSchema = z.object({
    description: z.string().min(3, "A descrição é obrigatória"),
    validUntil: z.coerce.date()
      .refine(date => date > new Date(), {
        message: "A data deve ser futura"
      }),
    notes: z.string().optional(),
  });

  // Form para geração de orçamento
  const quoteForm = useForm<z.infer<typeof quoteSchema>>({
    resolver: zodResolver(quoteSchema),
    defaultValues: {
      description: `Orçamento para ${serviceOrder?.orderNumber || ''}`,
      validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 dias a partir de hoje
      notes: "",
    },
  });

  // Manipulador para impressão da ordem de serviço
  const handlePrintServiceOrder = () => {
    const isMobile = window.innerWidth <= 768;

    toast({
      title: "Preparando impressão",
      description: "Aguarde enquanto preparamos a visualização para impressão...",
    });

    setShowPrintView(true);
  };

  // Callback para quando a impressão for concluída
  const handlePrintComplete = () => {
    setShowPrintView(false);

    toast({
      title: "Visualização fechada",
      description: "A visualização de impressão foi fechada.",
    });
  };

  // Manipulador para abrir o formulário de geração de orçamento
  const handleGenerateQuote = () => {
    setShowQuoteDialog(true);
  };

  // Manipulador para enviar formulário de orçamento
  const handleSubmitQuote = (values: z.infer<typeof quoteSchema>) => {
    toast({
      title: "Gerando orçamento",
      description: "Criando orçamento para a ordem de serviço...",
    });

    // Criar dados para o orçamento com base nos dados da OS
    if (!serviceOrder || !client) {
      toast({
        title: "Erro",
        description: "Dados da ordem de serviço ou cliente não encontrados",
        variant: "destructive"
      });
      return;
    }

    // Formatar os dados do orçamento
    const quoteData = {
      clientId: client.id,
      serviceOrderId: serviceOrder.id,
      description: values.description,
      validUntil: values.validUntil,
      notes: values.notes || "",
      // Total em centavos
      total: totalAmount * 100
      // O servidor vai gerar o número do orçamento automaticamente
    };

    // Enviar dados para a API
    fetch("/api/quotes", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(quoteData),
    })
    .then(response => {
      if (!response.ok) {
        return response.json().then(data => {
          throw new Error(JSON.stringify(data));
        });
      }
      return response.json();
    })
    .then(() => {
      toast({
        title: "Orçamento gerado",
        description: "O orçamento foi criado com sucesso",
      });
      setShowQuoteDialog(false);
    })
    .catch(error => {
      console.error("Erro ao criar orçamento:", error);
      toast({
        title: "Erro ao criar orçamento",
        description: error.message || "Ocorreu um erro ao criar o orçamento",
        variant: "destructive",
      });
    });
  };

  // Manipulador para exportação da ordem como PDF
  const handleExportToPDF = () => {
    const isMobile = window.innerWidth <= 768;

    try {
      toast({
        title: "Exportando PDF",
        description: isMobile ? 
          "Preparando arquivo PDF... Em dispositivos móveis, o PDF será aberto em uma nova aba." : 
          "Preparando arquivo PDF da ordem de serviço...",
      });

      // Importações dinâmicas para não afetar o carregamento inicial da página
      import('jspdf').then(({ default: jsPDF }) => {
        import('jspdf-autotable').then(({ default: autoTable }) => {
          // Criar novo documento PDF
          const doc = new jsPDF();

          // Configurações iniciais
          doc.setDrawColor(0);
          doc.setFillColor(245, 245, 245);

          // Cabeçalho da empresa com caixa sombreada
          doc.setFillColor(41, 128, 185); // Cor azul similar ao tema da aplicação
          doc.rect(0, 0, doc.internal.pageSize.width, 30, 'F');
          doc.setTextColor(255, 255, 255);
          doc.setFontSize(22);
          doc.setFont('helvetica', 'bold');
          doc.text('Simple Tech Solutions', 14, 15);
          doc.setFontSize(11);
          doc.text('Sistema de Gerenciamento de Assistência Técnica', 14, 22);

          // Reset para cor padrão
          doc.setTextColor(0, 0, 0);
          doc.setFont('helvetica', 'normal');

          // Título da Ordem de Serviço
          doc.setFontSize(20);
          doc.setFont('helvetica', 'bold');
          doc.text(`Ordem de Serviço #${serviceOrder.orderNumber}`, doc.internal.pageSize.width / 2, 40, { align: 'center' });
          doc.setFont('helvetica', 'normal');

          // Informações da ordem - Lado esquerdo
          doc.setFontSize(11);
          doc.setFont('helvetica', 'bold');
          doc.text('Informações do Cliente:', 14, 55);
          doc.setFont('helvetica', 'normal');
          doc.text(`Nome: ${client?.name || 'N/A'}`, 14, 62);
          doc.text(`Documento: ${client?.document || 'N/A'}`, 14, 69);
          doc.text(`Telefone: ${client?.phone || 'N/A'}`, 14, 76);
          doc.text(`Email: ${client?.email || 'N/A'}`, 14, 83);

          // Informações da ordem - Lado direito
          doc.setFont('helvetica', 'bold');
          const rightColumnX = 120;
          doc.text('Detalhes da Ordem:', rightColumnX, 55);
          doc.setFont('helvetica', 'normal');
          doc.text(`Data: ${formatDateTime(serviceOrder.createdAt)}`, rightColumnX, 62);
          doc.text(`Status: ${getStatusLabel(serviceOrder.status)}`, rightColumnX, 69);
          doc.text(`Técnico: ${technicianUser?.name || 'Não atribuído'}`, rightColumnX, 76);

          if (serviceOrder.completedAt) {
            doc.text(`Concluída em: ${formatDateTime(serviceOrder.completedAt)}`, rightColumnX, 83);
          }

          // Informações do equipamento
          doc.setFont('helvetica', 'bold');
          doc.text('Informações do Equipamento:', 14, 95);
          doc.setFont('helvetica', 'normal');

          if (equipment) {
            doc.text(`Equipamento: ${equipment.brand} ${equipment.model}`, 14, 102);
            if (equipment.serialNumber) {
              doc.text(`Número de Série: ${equipment.serialNumber}`, 14, 109);
            }
            if (equipment.purchaseDate) {
              doc.text(`Data de Compra: ${formatDateTime(equipment.purchaseDate)}`, 14, 116);
            }
          } else {
            doc.text('Nenhum equipamento associado', 14, 102);
          }

          // Linha separadora
          doc.setDrawColor(200, 200, 200);
          doc.line(14, 125, doc.internal.pageSize.width - 14, 125);

          // Descrição do problema
          doc.setFont('helvetica', 'bold');
          doc.text('Descrição do Problema:', 14, 135);
          doc.setFont('helvetica', 'normal');

          // Quebrar o texto em linhas para não ultrapassar a largura da página
          const textLines = doc.splitTextToSize(serviceOrder.description || 'Sem descrição', 180);
          doc.text(textLines, 14, 142);

          // Diagnóstico (se houver)
          let currentY = 142 + (textLines.length * 7);

          if (serviceOrder.diagnostics) {
            doc.setFont('helvetica', 'bold');
            doc.text('Diagnóstico Técnico:', 14, currentY);
            doc.setFont('helvetica', 'normal');

            const diagnosticLines = doc.splitTextToSize(serviceOrder.diagnostics, 180);
            doc.text(diagnosticLines, 14, currentY + 7);

            currentY += 7 + (diagnosticLines.length * 7);
          }

          // Solução (se houver)
          if (serviceOrder.solution) {
            doc.setFont('helvetica', 'bold');
            doc.text('Solução Aplicada:', 14, currentY);
            doc.setFont('helvetica', 'normal');

            const solutionLines = doc.splitTextToSize(serviceOrder.solution, 180);
            doc.text(solutionLines, 14, currentY + 7);

            currentY += 7 + (solutionLines.length * 7);
          }

          // Linha separadora antes dos itens
          doc.setDrawColor(200, 200, 200);
          doc.line(14, currentY + 5, doc.internal.pageSize.width - 14, currentY + 5);
          currentY += 15;

          // Título da seção de itens
          doc.setFont('helvetica', 'bold');
          doc.text('Itens e Serviços', 14, currentY);
          doc.setFont('helvetica', 'normal');

          // Tabela de itens
          if (orderItems.length > 0) {
            autoTable(doc, {
              startY: currentY + 5,
              head: [['Item', 'Quantidade', 'Valor Unitário', 'Subtotal']],
              body: orderItems.map(item => [
                item.description,
                item.quantity.toString(),
                formatCurrency(item.unitPrice),
                formatCurrency(item.quantity * item.unitPrice)
              ]),
              foot: [
                ['Total', '', '', formatCurrency(totalAmount)]
              ],
              theme: 'grid',
              headStyles: { 
                fillColor: [41, 128, 185],
                textColor: [255, 255, 255],
                fontSize: 10,
                fontStyle: 'bold',
                halign: 'center'
              },
              bodyStyles: {
                fontSize: 9
              },
              footStyles: {
                fillColor: [240, 240, 240],
                fontSize: 10,
                fontStyle: 'bold'
              },
              columnStyles: {
                0: { cellWidth: 'auto' },
                1: { halign: 'center', cellWidth: 30 },
                2: { halign: 'right', cellWidth: 40 },
                3: { halign: 'right', cellWidth: 40 }
              }
            });
          } else {
            currentY += 10;
            doc.setFontSize(10);
            doc.text('Nenhum item adicionado a esta ordem de serviço', 14, currentY);

            // Adicionar linha para total zerado
            currentY += 15;
            doc.setFillColor(240, 240, 240);
            doc.rect(14, currentY - 5, doc.internal.pageSize.width - 28, 10, 'F');
            doc.setFont('helvetica', 'bold');
            doc.text('Total:', 14, currentY);
            doc.text('R$ 0,00', doc.internal.pageSize.width - 14, currentY, { align: 'right' });
          }

          // Área para assinaturas
          const pageHeight = doc.internal.pageSize.height;
          doc.text('Assinaturas:', 14, pageHeight - 50);

          // Linha para assinatura do cliente
          doc.line(14, pageHeight - 30, 100, pageHeight - 30);
          doc.text('Cliente', 57, pageHeight - 25, { align: 'center' });

          // Linha para assinatura do técnico
          doc.line(110, pageHeight - 30, 196, pageHeight - 30);
          doc.text('Técnico Responsável', 153, pageHeight - 25, { align: 'center' });

          // Rodapé com informações da empresa
          doc.setDrawColor(41, 128, 185);
          doc.setFillColor(41, 128, 185);
          doc.rect(0, pageHeight - 20, doc.internal.pageSize.width, 20, 'F');

          doc.setTextColor(255, 255, 255);
          doc.setFontSize(8);
          doc.text('Simple Tech Solutions - Assistência Técnica Especializada', 14, pageHeight - 14);
          doc.text('Tel: (11) 4321-1234 | Email: <EMAIL>', 14, pageHeight - 8);
          doc.text(`OS-${serviceOrder.orderNumber} gerada em ${new Date().toLocaleDateString()}`, doc.internal.pageSize.width - 14, pageHeight - 8, { align: 'right' });

          // Aplicar os mesmos elementos em todas as páginas
          const pageCount = doc.internal.getNumberOfPages();
          for (let i = 2; i <= pageCount; i++) { // Começando da página 2
            doc.setPage(i);

            // Cabeçalho simplificado
            doc.setFillColor(41, 128, 185);
            doc.rect(0, 0, doc.internal.pageSize.width, 20, 'F');
            doc.setTextColor(255, 255, 255);
            doc.setFontSize(10);
            doc.text(`Ordem de Serviço #${serviceOrder.orderNumber} - Continuação`, 14, 13);

            // Rodapé
            doc.setFillColor(41, 128, 185);
            doc.rect(0, pageHeight - 20, doc.internal.pageSize.width, 20, 'F');
            doc.setTextColor(255, 255, 255);
            doc.setFontSize(8);
            doc.text('Simple Tech Solutions - Assistência Técnica Especializada', 14, pageHeight - 14);
            doc.text(`Página ${i} de ${pageCount}`, doc.internal.pageSize.width - 14, pageHeight - 14, { align: 'right' });
          }

          // Comportamento diferente para mobile e desktop
          if (isMobile) {
            // Em dispositivos móveis, abrir o PDF em uma nova aba
            const pdfBlob = doc.output('blob');
            const pdfUrl = URL.createObjectURL(pdfBlob);

            // Tentar abrir em nova aba
            const newTab = window.open();
            if (newTab) {
              newTab.location.href = pdfUrl;

              toast({
                title: "PDF Aberto",
                description: "O PDF foi aberto em uma nova aba. Use o menu do navegador para salvá-lo.",
                duration: 5000,
              });
            } else {
              // Se não conseguir abrir nova aba, fazer download direto
              doc.save(`OS-${serviceOrder.orderNumber}.pdf`);

              toast({
                title: "PDF Baixado",
                description: "O PDF foi baixado. Verifique a pasta Downloads do seu navegador.",
                duration: 5000,
              });
            }

            // Limpar a URL após um tempo
            setTimeout(() => {
              URL.revokeObjectURL(pdfUrl);
            }, 10000);

          } else {
            // No desktop, fazer download direto
            doc.save(`OS-${serviceOrder.orderNumber}.pdf`);

            toast({
              title: "PDF Gerado",
              description: "O PDF da ordem de serviço foi gerado e baixado com sucesso!",
            });
          }

        });
      }).catch(error => {
        console.error("Erro ao carregar bibliotecas de PDF:", error);
        toast({
          title: "Erro",
          description: "Não foi possível gerar o PDF. Tente novamente.",
          variant: "destructive",
        });
      });
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o PDF.",
        variant: "destructive",
      });
    }
  };

  // Função de compartilhar com cliente via e-mail
  const handleShareWithClient = (method: string) => {
    if (!serviceOrder || !client) return;

    const orderUrl = `${window.location.origin}/service-orders/${serviceOrder.id}`;
    const message = `Olá ${client.name}!\n\nSua ordem de serviço #${serviceOrder.orderNumber} está com status: ${getStatusLabel(serviceOrder.status)}\n\nVocê pode acompanhar o andamento através do link: ${orderUrl}\n\nEquipe de Suporte Técnico`;

    if (method === "whatsapp" && client.phone) {
      const whatsappUrl = `https://wa.me/${client.phone.replace(/\D/g, '')}?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, '_blank');
    } else if (method === "email" && client.email) {
      const emailSubject = `Ordem de Serviço #${serviceOrder.orderNumber} - Atualização`;
      const emailUrl = `mailto:${client.email}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(message)}`;
      window.open(emailUrl, '_blank');
    } else {
      // Copiar para área de transferência
      navigator.clipboard.writeText(message).then(() => {
        toast({
          title: "Mensagem copiada",
          description: "A mensagem foi copiada para a área de transferência.",
        });
      }).catch(() => {
        toast({
          title: "Erro ao copiar",
          description: "Não foi possível copiar a mensagem.",
          variant: "destructive",
        });
      });
    }
  };

  // Manipulador para excluir ordem de serviço
  const deleteServiceOrderMutation = useMutation({
    mutationFn: async () => {
      await apiRequest('DELETE', `/api/service-orders/${serviceOrderId}`);
    },
    onSuccess: () => {
      toast({
        title: "Ordem de Serviço Excluída",
        description: "A ordem de serviço foi excluída com sucesso",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/service-orders'] });
      navigate("/service-orders");
    },
    onError: (error) => {
      console.error('Erro ao excluir ordem de serviço:', error);
      toast({
        title: "Erro ao excluir",
        description: "Ocorreu um erro ao excluir a ordem de serviço.",
        variant: "destructive",
      });
    }
  });

  const handleDeleteServiceOrder = () => {
    toast({
      title: "Excluindo",
      description: "Excluindo a ordem de serviço...",
    });

    // Executar a mutação de exclusão
    deleteServiceOrderMutation.mutate();
  };

  // Get client details
  const client = serviceOrder ? clients.find(c => c.id === serviceOrder.clientId) : undefined;

  // Get equipment details
  const equipment = serviceOrder?.equipmentId ? 
    equipmentList.find(e => e.id === serviceOrder.equipmentId) : undefined;

  // Get technician details
  const technician = serviceOrder?.technicianId ? 
    technicians.find(t => t.id === serviceOrder.technicianId) : undefined;

  // Get technician name
  const technicianUser = technician?.userId ? 
    users.find(u => u.id === technician.userId) : undefined;

  // Calculate total amount
  const totalAmount = calculateTotal(orderItems);

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  if (isLoadingOrder) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes da Ordem de Serviço" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Erro" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-red-500">Erro</CardTitle>
                <CardDescription>
                  Falha ao carregar detalhes da ordem de serviço
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-red-500">{error?.message || "Ocorreu um erro desconhecido"}</p>
                <Button 
                  onClick={() => navigate("/service-orders")} 
                  className="mt-4"
                >
                  Voltar para Ordens de Serviço
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!serviceOrder) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Não Encontrada" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <Card>
              <CardHeader>
                <CardTitle>Não Encontrada</CardTitle>
                <CardDescription>
                  Ordem de serviço não encontrada
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>A ordem de serviço que você está procurando não existe ou foi excluída.</p>
                <Button 
                  onClick={() => navigate("/service-orders")} 
                  className="mt-4"
                >
                  Voltar para Ordens de Serviço
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  const statusColors = getStatusColor(serviceOrder.status);

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />

      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header 
          title={`Ordem de Serviço #${serviceOrder.orderNumber}`} 
          onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} 
        />

        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="mb-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-800 mr-3">
                Ordem de Serviço #{serviceOrder.orderNumber}
              </h1>
              <Badge className={`${statusColors.bg} ${statusColors.text}`}>
                {getStatusLabel(serviceOrder.status)}
              </Badge>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button                 variant="outline" 
                size="sm"
                onClick={() => setShowStatusDialog(true)}
              >
                <Clock className="h-4 w-4 mr-1" />
                Atualizar Status
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4 mr-1" />
                    Ações
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handlePrintServiceOrder}>
                    <Printer className="h-4 w-4 mr-2" />
                    Imprimir Ordem de Serviço
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleExportToPDF}>
                    <Download className="h-4 w-4 mr-2" />
                    Exportar como PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleShareWithClient("")}>
                    <Share className="h-4 w-4 mr-2" />
                    Compartilhar com Cliente
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleGenerateQuote}>
                    <FileText className="h-4 w-4 mr-2" />
                    Criar Orçamento
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
            <div className="lg:col-span-2">
              <Tabs defaultValue="details" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">Detalhes</TabsTrigger>
                  <TabsTrigger value="items">Itens & Serviços</TabsTrigger>
                  <TabsTrigger value="history">Histórico</TabsTrigger>
                </TabsList>

                <TabsContent value="details">
                  <Card>
                    <CardHeader>
                      <CardTitle>Detalhes da Ordem de Serviço</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Cliente</h3>
                          <p className="text-lg font-medium">{client?.name || "Cliente Desconhecido"}</p>
                          {client?.phone && <p className="text-sm text-gray-600">{client.phone}</p>}
                          {client?.email && <p className="text-sm text-gray-600">{client.email}</p>}
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Equipamento</h3>
                          {equipment ? (
                            <>
                              <p className="text-lg font-medium">
                                {equipment.brand} {equipment.model}
                              </p>
                              {equipment.serialNumber && (
                                <p className="text-sm text-gray-600">S/N: {equipment.serialNumber}</p>
                              )}
                              {equipment.description && (
                                <p className="text-sm text-gray-600">{equipment.description}</p>
                              )}
                            </>
                          ) : (
                            <p className="text-gray-600">Nenhum equipamento especificado</p>
                          )}
                        </div>
                      </div>

                      <Separator />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Técnico Designado</h3>
                          {technicianUser ? (
                            <>
                              <p className="text-lg font-medium">{technicianUser.name}</p>
                              {technician?.specialties && (
                                <p className="text-sm text-gray-600">Especialidades: {technician.specialties}</p>
                              )}
                            </>
                          ) : (
                            <p className="text-gray-600">Nenhum técnico designado</p>
                          )}
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Datas</h3>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <p className="text-gray-600">Criada:</p>
                            <p>{formatDateTime(serviceOrder.createdAt)}</p>

                            <p className="text-gray-600">Última Atualização:</p>
                            <p>{formatDateTime(serviceOrder.updatedAt)}</p>

                            {serviceOrder.estimatedCompletionDate && (
                              <>
                                <p className="text-gray-600">Conclusão Estimada:</p>
                                <p>{formatDateTime(serviceOrder.estimatedCompletionDate)}</p>
                              </>
                            )}

                            {serviceOrder.completedAt && (
                              <>
                                <p className="text-gray-600">Concluída:</p>
                                <p>{formatDateTime(serviceOrder.completedAt)}</p>
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-2">Descrição</h3>
                        <p className="text-gray-800 whitespace-pre-line">{serviceOrder.description}</p>
                      </div>

                      {serviceOrder.diagnostics && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-2">Diagnóstico</h3>
                          <p className="text-gray-800 whitespace-pre-line">{serviceOrder.diagnostics}</p>
                        </div>
                      )}

                      {serviceOrder.solution && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-2">Solução</h3>
                          <p className="text-gray-800 whitespace-pre-line">{serviceOrder.solution}</p>
                        </div>
                      )}

                      {serviceOrder.internalNotes && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-2">Anotações Internas</h3>
                          <p className="text-gray-800 whitespace-pre-line">{serviceOrder.internalNotes}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="items">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <div>
                        <CardTitle>Itens & Serviços</CardTitle>
                        <CardDescription>
                          Peças e serviços utilizados nesta ordem
                        </CardDescription>
                      </div>
                      <Button 
                        size="sm" 
                        onClick={() => setShowAddItemDialog(true)}
                        className="bg-primary hover:bg-primary-dark"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Adicionar Item
                      </Button>
                    </CardHeader>
                    <CardContent>
                      {orderItems.length === 0 ? (
                        <div className="text-center py-6 text-gray-500">
                          Nenhum item adicionado a esta ordem de serviço ainda
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="w-[400px]">Descrição</TableHead>
                              <TableHead className="text-right">Quantidade</TableHead>
                              <TableHead className="text-right">Preço Unitário</TableHead>
                              <TableHead className="text-right">Total</TableHead>
                              <TableHead className="w-[50px]"></TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {orderItems.map((item) => (
                              <TableRow key={item.id}>
                                <TableCell className="font-medium">{item.description}</TableCell>
                                <TableCell className="text-right">{item.quantity}</TableCell>
                                <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                                <TableCell className="text-right">{formatCurrency(item.quantity * item.unitPrice)}</TableCell>
                                <TableCell>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <span className="sr-only">Remover item</span>
                                    <X className="h-4 w-4" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}

                      <div className="mt-6 flex justify-end">
                        <div className="w-[300px]">
                          <div className="flex justify-between py-2 text-sm">
                            <span className="font-medium">Subtotal</span>
                            <span>{formatCurrency(totalAmount)}</span>
                          </div>
                          <Separator className="my-2" />
                          <div className="flex justify-between py-2 text-base font-bold">
                            <span>Total</span>
                            <span>{formatCurrency(totalAmount)}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="history">
                  <Card>
                    <CardHeader>
                      <CardTitle>Histórico da Ordem de Serviço</CardTitle>
                      <CardDescription>
                        Linha do tempo de todas as atividades para esta ordem de serviço
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex">
                          <div className="mr-4 flex flex-col items-center">
                            <div className="h-4 w-4 rounded-full bg-primary"></div>
                            <div className="h-full w-px bg-gray-200"></div>
                          </div>
                          <div>
                            <p className="font-medium">Ordem de serviço criada</p>
                            <p className="text-sm text-gray-500">
                              {formatDateTime(serviceOrder.createdAt)}
                            </p>
                          </div>
                        </div>

                        <div className="flex">
                          <div className="mr-4 flex flex-col items-center">
                            <div className="h-4 w-4 rounded-full bg-primary"></div>
                            <div className="h-full w-px bg-gray-200"></div>
                          </div>
                          <div>
                            <p className="font-medium">Status alterado para {getStatusLabel(serviceOrder.status)}</p>
                            <p className="text-sm text-gray-500">
                              {formatDateTime(serviceOrder.updatedAt)}
                            </p>
                          </div>
                        </div>

                        {/* Aqui seria mostrada a linha do tempo completa se tivéssemos esses dados */}

                        {serviceOrder.completedAt && (
                          <div className="flex">
                            <div className="mr-4 flex flex-col items-center">
                              <div className="h-4 w-4 rounded-full bg-green-500"></div>
                            </div>
                            <div>
                              <p className="font-medium">Ordem de serviço concluída</p>
                              <p className="text-sm text-gray-500">
                                {formatDateTime(serviceOrder.completedAt)}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Informações do Cliente</CardTitle>
                </CardHeader>
                <CardContent>
                  {client ? (
                    <div className="space-y-2">
                      <p className="font-medium text-lg">{client.name}</p>
                      {client.email && <p className="text-sm">{client.email}</p>}
                      {client.phone && <p className="text-sm">{client.phone}</p>}
                      {client.address && (
                        <div className="text-sm pt-2">
                          <p>{client.address}</p>
                          {client.city && client.state && (
                            <p>
                              {client.city}, {client.state} {client.zipCode}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500">Informações do cliente não disponíveis</p>
                  )}
                </CardContent>
                <CardFooter className="border-t pt-4">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full" 
                    onClick={() => client && navigate(`/clients/${client.id}`)}
                    disabled={!client}
                  >
                    Ver Detalhes do Cliente
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Resumo Financeiro</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal:</span>
                      <span className="font-medium">{formatCurrency(totalAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Imposto:</span>
                      <span className="font-medium">{formatCurrency(0)}</span>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total:</span>
                      <span>{formatCurrency(totalAmount)}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="border-t pt-4 flex-col space-y-2">
                  <Button 
                    className="w-full bg-green-600 hover:bg-green-700" 
                    size="sm"
                    onClick={() => navigate(`/service-orders/${serviceOrder.id}/payment`)}
                  >
                    <FileText className="h-4 w-4 mr-1" />
                    Realizar Pagamento
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Ações</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full justify-start" 
                    size="sm"
                    onClick={() => navigate(`/service-orders/${serviceOrder.id}/edit`)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Editar Ordem de Serviço
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start" 
                    size="sm"
                    onClick={handlePrintServiceOrder}
                  >
                    <Printer className="h-4 w-4 mr-2" />
                    Imprimir Ordem de Serviço
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start" 
                    size="sm"
                    onClick={handleExportToPDF}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Exportar como PDF
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start" 
                    size="sm"
                    onClick={() => handleShareWithClient("")}
                  >
                    <Share className="h-4 w-4 mr-2" />
                    Compartilhar com Cliente
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start text-red-500 hover:text-red-700 hover:bg-red-50" 
                    size="sm"
                    onClick={() => setShowDeleteDialog(true)}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Excluir Ordem de Serviço
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Status Update Dialog */}
      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Atualizar Status</DialogTitle>
            <DialogDescription>
              Alterar o status atual da ordem de serviço #{serviceOrder.orderNumber}
            </DialogDescription>
          </DialogHeader>

          <Form {...statusForm}>
            <form onSubmit={statusForm.handleSubmit(onSubmitStatusUpdate)} className="space-y-4">
              <FormField
                control={statusForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecionar status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {SERVICE_ORDER_STATUSES.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowStatusDialog(false)}
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  disabled={updateStatusMutation.isPending}
                  className="bg-primary hover:bg-primary-dark"
                >
                  {updateStatusMutation.isPending ? "Atualizando..." : "Atualizar Status"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Add Item Dialog */}
      <Dialog open={showAddItemDialog} onOpenChange={setShowAddItemDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Item</DialogTitle>
            <DialogDescription>
              Adicionar uma peça ou serviço a esta ordem
            </DialogDescription>
          </DialogHeader>

          <Form {...addItemForm}>
            <form onSubmit={addItemForm.handleSubmit(onSubmitAddItem)} className="space-y-4">
              <FormField
                control={addItemForm.control}
                name="inventoryItemId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Selecionar do Inventário (Opcional)</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecionar um item" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {inventoryItems.map((item) => (
                          <SelectItem key={item.id} value={item.id.toString()}>
                            {item.name} {item.quantity > 0 ? `(${item.quantity} em estoque)` : "(Sem estoque)"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={addItemForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição*</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Descrição da peça ou serviço" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-4">
                <FormField
                  control={addItemForm.control}
                  name="quantity"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Quantidade*</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min={1} 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addItemForm.control}
                  name="unitPrice"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Preço Unitário*</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min={0} 
                          step={0.01} 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowAddItemDialog(false)}
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  disabled={addItemMutation.isPending}
                  className="bg-primary hover:bg-primary-dark"
                >
                  {addItemMutation.isPending ? "Adicionando..." : "Adicionar Item"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Ordem de Serviço</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a ordem de serviço #{serviceOrder.orderNumber}?
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              className="bg-red-500 hover:bg-red-600"
              onClick={handleDeleteServiceOrder}
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Share Dialog */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Compartilhar com Cliente</DialogTitle>
            <DialogDescription>
              Selecione como deseja compartilhar esta ordem de serviço com o cliente
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">
                E-mail do Cliente
              </label>
              <Input
                id="email"
                placeholder="<EMAIL>"
                value={shareEmail}
                onChange={(e) => setShareEmail(e.target.value)}
                type="email"
              />
              {client?.email && (
                <p className="text-sm text-gray-500">
                  E-mail registrado do cliente: {client.email}
                </p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setShowShareDialog(false)}
            >
              Cancelar
            </Button>
            <Button 
              type="button" 
              className="bg-primary hover:bg-primary-dark"
              onClick={() => handleShareWithClient(shareEmail)}
              disabled={!shareEmail}
            >
              Enviar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Orçamento Dialog */}
      <Dialog open={showQuoteDialog} onOpenChange={setShowQuoteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Criar Orçamento</DialogTitle>
            <DialogDescription>
              Crie um orçamento baseado nesta ordem de serviço
            </DialogDescription>
          </DialogHeader>

          <Form {...quoteForm}>
            <form onSubmit={quoteForm.handleSubmit(handleSubmitQuote)} className="space-y-4">
              <FormField
                control={quoteForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Descrição do orçamento" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={quoteForm.control}
                name="validUntil"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Válido até</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value instanceof Date ? field.value.toISOString().substring(0, 10) : ''}
                        onChange={(e) => {
                          const date = e.target.value ? new Date(e.target.value) : null;
                          field.onChange(date);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={quoteForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observações (opcional)</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Observações adicionais para o orçamento"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="pt-4 space-y-3">
                <h3 className="text-sm font-medium">Resumo do Orçamento</h3>

                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="flex justify-between py-1 text-sm">
                    <span>Itens da Ordem:</span>
                    <span>{orderItems.length}</span>
                  </div>
                  <div className="flex justify-between py-1 text-sm">
                    <span>Total:</span>
                    <span className="font-medium">{formatCurrency(totalAmount)}</span>
                  </div>
                </div>
              </div>

              <DialogFooter className="pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowQuoteDialog(false)}
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                >
                  Gerar Orçamento
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Confirmação de exclusão */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Ordem de Serviço</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta ordem de serviço? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteServiceOrder}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Visualização de impressão */}
      {showPrintView && serviceOrder && (
        <div className="fixed inset-0 bg-white z-50 overflow-y-auto">
          <div className="p-4 flex justify-end no-print">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPrintView(false)}
              className="no-print"
            >
              <X className="h-4 w-4 mr-1" />
              Fechar
            </Button>
          </div>
          <div className="pb-8">
            <PrintServiceOrder
              serviceOrder={serviceOrder}
              client={client}
              equipment={equipment}
              technician={technician}
              technicianUser={technicianUser}
              orderItems={orderItems}
              totalAmount={totalAmount}
              onPrintComplete={handlePrintComplete}
            />
          </div>
        </div>
      )}
    </div>
  );
}