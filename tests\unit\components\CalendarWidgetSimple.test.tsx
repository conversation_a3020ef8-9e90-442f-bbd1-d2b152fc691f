import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import CalendarWidgetSimple from '@/components/calendar/CalendarWidgetSimple'
import { apiRequest } from '@/lib/queryClient'

vi.mock('@/lib/queryClient', () => ({
  apiRequest: vi.fn()
}))

const mockAppointments = [
  {
    id: 1,
    title: 'Visita Técnica Hoje',
    appointmentDate: new Date().toISOString(),
    startTime: '09:00',
    status: 'scheduled',
    type: 'technical_visit'
  },
  {
    id: 2,
    title: 'Entrega Amanhã',
    appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    startTime: '14:00',
    status: 'confirmed',
    type: 'equipment_delivery'
  }
]

const mockSchedules = [
  {
    id: 1,
    title: '<PERSON><PERSON><PERSON><PERSON>',
    scheduleDate: new Date().toISOString(),
    startTime: '08:00',
    endTime: '17:00',
    technicianId: 1
  }
]

describe('CalendarWidgetSimple', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    })
    vi.clearAllMocks()
  })

  const renderComponent = (props = {}) => {
    const defaultProps = {
      daysToShow: 7,
      ...props
    }

    return render(
      <QueryClientProvider client={queryClient}>
        <CalendarWidgetSimple {...defaultProps} />
      </QueryClientProvider>
    )
  }

  it('should render widget title correctly', () => {
    vi.mocked(apiRequest).mockImplementation(() => Promise.resolve([]))

    renderComponent()

    expect(screen.getByText('Próximos 7 dias')).toBeInTheDocument()
  })

  it('should render custom days count', () => {
    vi.mocked(apiRequest).mockImplementation(() => Promise.resolve([]))

    renderComponent({ daysToShow: 14 })

    expect(screen.getByText('Próximos 14 dias')).toBeInTheDocument()
  })

  it('should display legend correctly', () => {
    vi.mocked(apiRequest).mockImplementation(() => Promise.resolve([]))

    renderComponent()

    expect(screen.getByText('Agendamentos')).toBeInTheDocument()
    expect(screen.getByText('Técnicos')).toBeInTheDocument()
  })

  it('should render appointments for upcoming days', async () => {
    vi.mocked(apiRequest).mockImplementation((method, url) => {
      if (url === '/api/appointments') return Promise.resolve(mockAppointments)
      if (url === '/api/technician-schedules') return Promise.resolve(mockSchedules)
      return Promise.resolve([])
    })

    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Visita Técnica Hoje')).toBeInTheDocument()
      expect(screen.getByText('Entrega Amanhã')).toBeInTheDocument()
    })
  })

  it('should render technician schedules', async () => {
    vi.mocked(apiRequest).mockImplementation((method, url) => {
      if (url === '/api/appointments') return Promise.resolve([])
      if (url === '/api/technician-schedules') return Promise.resolve(mockSchedules)
      return Promise.resolve([])
    })

    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Horário Técnico (Técnico)')).toBeInTheDocument()
    })
  })

  it('should show "today" indicator for current date', async () => {
    vi.mocked(apiRequest).mockImplementation((method, url) => {
      if (url === '/api/appointments') return Promise.resolve(mockAppointments)
      if (url === '/api/technician-schedules') return Promise.resolve([])
      return Promise.resolve([])
    })

    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Hoje')).toBeInTheDocument()
    })
  })

  it('should display "no events" message for empty days', async () => {
    vi.mocked(apiRequest).mockImplementation(() => Promise.resolve([]))

    renderComponent()

    await waitFor(() => {
      expect(screen.getAllByText('Nenhum evento')).toHaveLength(7) // 7 days with no events
    })
  })

  it('should handle different appointment types with correct styling', async () => {
    vi.mocked(apiRequest).mockImplementation((method, url) => {
      if (url === '/api/appointments') return Promise.resolve(mockAppointments)
      if (url === '/api/technician-schedules') return Promise.resolve(mockSchedules)
      return Promise.resolve([])
    })

    renderComponent()

    await waitFor(() => {
      // Check for appointment styling (blue background)
      const appointmentElement = screen.getByText('Visita Técnica Hoje').closest('div')
      expect(appointmentElement).toHaveClass('bg-blue-50')
      
      // Check for schedule styling (green background)
      const scheduleElement = screen.getByText('Horário Técnico (Técnico)').closest('div')
      expect(scheduleElement).toHaveClass('bg-green-50')
    })
  })

  it('should display times correctly', async () => {
    vi.mocked(apiRequest).mockImplementation((method, url) => {
      if (url === '/api/appointments') return Promise.resolve(mockAppointments)
      if (url === '/api/technician-schedules') return Promise.resolve([])
      return Promise.resolve([])
    })

    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('09:00')).toBeInTheDocument()
      expect(screen.getByText('14:00')).toBeInTheDocument()
    })
  })

  it('should filter events by date range correctly', async () => {
    const futureAppointment = {
      id: 3,
      title: 'Agendamento Futuro',
      appointmentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      startTime: '10:00',
      status: 'scheduled',
      type: 'technical_visit'
    }

    vi.mocked(apiRequest).mockImplementation((method, url) => {
      if (url === '/api/appointments') return Promise.resolve([...mockAppointments, futureAppointment])
      if (url === '/api/technician-schedules') return Promise.resolve([])
      return Promise.resolve([])
    })

    renderComponent({ daysToShow: 7 })

    await waitFor(() => {
      // Should show appointments within 7 days
      expect(screen.getByText('Visita Técnica Hoje')).toBeInTheDocument()
      expect(screen.getByText('Entrega Amanhã')).toBeInTheDocument()
      
      // Should NOT show appointments beyond 7 days
      expect(screen.queryByText('Agendamento Futuro')).not.toBeInTheDocument()
    })
  })

  it('should handle API errors gracefully', async () => {
    vi.mocked(apiRequest).mockRejectedValue(new Error('Network error'))

    renderComponent()

    // Widget should still render with title
    expect(screen.getByText('Próximos 7 dias')).toBeInTheDocument()
    
    await waitFor(() => {
      // Should show message when no events due to error
      expect(screen.getByText('Nenhum evento nos próximos 7 dias')).toBeInTheDocument()
    })
  })
})