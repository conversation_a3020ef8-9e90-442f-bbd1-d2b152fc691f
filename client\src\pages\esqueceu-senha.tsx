import { useState } from "react";
import { useLocation } from "wouter";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ArrowLeft } from "lucide-react";

const forgotPasswordSchema = z.object({
  email: z.string().email("Por favor, insira um email válido"),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export default function EsqueceuSenha() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  // Simulação da recuperação de senha (sem envio real de email)
  const forgotPasswordMutation = useMutation({
    mutationFn: async (data: ForgotPasswordFormValues) => {
      // Simula um atraso para parecer que está processando
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Em vez de fazer uma chamada real à API, apenas simula sucesso
      return new Response();
    },
    onSuccess: () => {
      toast({
        title: "Simulação de recuperação de senha",
        description: "Em um ambiente de produção, um email seria enviado com instruções. Funcionalidade completa será implementada posteriormente.",
      });
      setEmailSent(true);
    },
    onError: (error) => {
      toast({
        title: "Falha ao enviar email",
        description: error.message || "Não foi possível processar a solicitação",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: ForgotPasswordFormValues) => {
    setIsLoading(true);
    forgotPasswordMutation.mutate(values, {
      onSettled: () => {
        setIsLoading(false);
      },
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold tracking-tight text-primary">
            Recuperar Senha
          </CardTitle>
          <p className="text-sm text-gray-500">
            Informe seu email para receber instruções de recuperação
          </p>
        </CardHeader>
        <CardContent>
          {!emailSent ? (
            <>
              <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-md text-amber-800 text-sm">
                <p>⚠️ <strong>Funcionalidade em desenvolvimento</strong>: No momento, esta é apenas uma demonstração da interface. O envio de emails será implementado posteriormente.</p>
              </div>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Digite seu email cadastrado"
                            autoComplete="email"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    className="w-full bg-primary hover:bg-primary-dark"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        Enviando...
                      </div>
                    ) : (
                      "Enviar instruções"
                    )}
                  </Button>
                </form>
              </Form>
            </>
          ) : (
            <div className="text-center space-y-4">
              <div className="p-3 bg-green-50 border border-green-200 rounded-md text-green-800 mb-4">
                <p>✓ <strong>Simulação completada</strong></p>
                <p className="text-sm mt-1">
                  Esta é uma demonstração da interface. Em um ambiente de produção real, 
                  um email seria enviado para o endereço informado.
                </p>
              </div>
              <p className="text-sm text-gray-500">
                (A funcionalidade completa de envio de email será implementada futuramente)
              </p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => setEmailSent(false)}
              >
                Tentar novamente
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button variant="link" className="text-sm text-primary" onClick={() => navigate("/login")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar para o login
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}