# Testes do Sistema de Agendamentos

Este diretório contém uma suíte completa de testes para o sistema de agendamentos do TechSupport Manager.

## Estrutura dos Testes

```
tests/
├── unit/                       # Testes unitários
│   ├── components/            # Testes de componentes React
│   │   ├── AppointmentCalendarSimple.test.tsx
│   │   └── CalendarWidgetSimple.test.tsx
│   └── pages/                 # Testes de páginas
│       ├── appointments.test.tsx
│       └── appointment-form.test.tsx
├── integration/               # Testes de integração
│   └── appointments-api.test.ts
├── e2e/                      # Testes end-to-end
│   └── appointments.test.ts
├── setup.ts                  # Configuração global dos testes
└── README.md                 # Este arquivo
```

## Tecnologias Utilizadas

- **Vitest**: Framework de testes rápido e compatível com Vite
- **Testing Library**: Biblioteca para testes de componentes React
- **jsdom**: Ambiente DOM para testes
- **Supertest**: Testes de API HTTP
- **User Event**: Simulação de interações do usuário

## Scripts Disponíveis

```bash
# Executar todos os testes
pnpm test

# Executar testes com interface gráfica
pnpm test:ui

# Executar testes uma vez (CI/CD)
pnpm test:run

# Executar testes com cobertura
pnpm test:coverage

# Executar testes em modo watch
pnpm test:watch
```

## Tipos de Testes

### 1. Testes Unitários

#### Componentes (`tests/unit/components/`)

- **AppointmentCalendarSimple.test.tsx**
  - Renderização do calendário
  - Navegação entre meses
  - Exibição de agendamentos
  - Interações com eventos e slots
  - Estados de loading e erro

- **CalendarWidgetSimple.test.tsx**
  - Exibição de próximos eventos
  - Filtragem por período
  - Diferentes tipos de eventos
  - Estados vazios e de erro

#### Páginas (`tests/unit/pages/`)

- **appointments.test.tsx**
  - Listagem de agendamentos
  - Filtros e busca
  - Alternância entre visualizações
  - Exclusão de agendamentos
  - Estados de loading/erro/vazio

- **appointment-form.test.tsx**
  - Formulário de criação
  - Formulário de edição
  - Validações de campo
  - Submissão de dados
  - Tratamento de erros

### 2. Testes de Integração (`tests/integration/`)

#### API de Agendamentos (`appointments-api.test.ts`)

- **Endpoints testados:**
  - `GET /api/appointments` - Listar agendamentos
  - `GET /api/appointments/:id` - Buscar agendamento específico
  - `POST /api/appointments` - Criar agendamento
  - `PUT /api/appointments/:id` - Atualizar agendamento
  - `DELETE /api/appointments/:id` - Excluir agendamento
  - `PATCH /api/appointments/:id/status` - Atualizar status
  - `PATCH /api/appointments/:id/complete` - Marcar como concluído
  - `PATCH /api/appointments/:id/cancel` - Cancelar agendamento

- **Cenários de filtros:**
  - Por técnico
  - Por cliente
  - Por data
  - Por tipo
  - Por status
  - Por período (data range)

- **Tratamento de erros:**
  - Dados inválidos
  - Registros não encontrados
  - Erros de banco de dados

### 3. Testes End-to-End (`tests/e2e/`)

#### Fluxos Completos (`appointments.test.ts`)

- **Gerenciamento de Lista:**
  - Visualização de agendamentos
  - Aplicação de filtros
  - Alternância entre visualizações
  - Atualização manual de dados

- **Criação de Agendamentos:**
  - Preenchimento completo do formulário
  - Validação de campos obrigatórios
  - Seleção de relacionamentos (cliente, técnico, OS)
  - Submissão e feedback

- **Edição de Agendamentos:**
  - Carregamento de dados existentes
  - Modificação de campos
  - Atualização e confirmação

- **Exclusão de Agendamentos:**
  - Confirmação de exclusão
  - Cancelamento de exclusão
  - Feedback de sucesso

- **Tratamento de Erros:**
  - Estados de erro da API
  - Estados vazios
  - Recuperação de erros

## Configuração

### Pré-requisitos

```bash
# Instalar dependências de teste
pnpm install
```

### Configuração do Ambiente

O arquivo `tests/setup.ts` configura:
- Mocks globais (fetch, window.location)
- Extensões do jest-dom
- Configurações de console

### Variáveis de Ambiente

Para testes de integração, certifique-se de ter:
```env
NODE_ENV=test
DATABASE_URL=postgresql://...
SESSION_SECRET=test-secret
```

## Mocks e Utilitários

### Mocks Principais

1. **API Requests**: Mock do `apiRequest` para simular respostas da API
2. **Authentication**: Mock de sessão de usuário autenticado
3. **Navigation**: Mock do roteador wouter
4. **UI Components**: Mock de componentes de layout

### Dados de Teste

Os testes utilizam dados mockados consistentes:
- Agendamentos de exemplo
- Clientes e técnicos
- Usuários e ordens de serviço

## Cobertura de Testes

### Componentes Cobertos

- ✅ AppointmentCalendarSimple
- ✅ CalendarWidgetSimple
- ✅ Página de listagem de agendamentos
- ✅ Formulários de criação/edição

### APIs Cobertas

- ✅ CRUD completo de agendamentos
- ✅ Filtros e consultas
- ✅ Operações de status
- ✅ Tratamento de erros

### Fluxos Cobertos

- ✅ Visualização e navegação
- ✅ Criação completa
- ✅ Edição e atualização
- ✅ Exclusão com confirmação
- ✅ Estados de erro e recuperação

## Boas Práticas

### 1. Nomenclatura

- Nomes descritivos para testes
- Agrupamento lógico com `describe`
- Cenários específicos com `it`

### 2. Estrutura dos Testes

```typescript
describe('Component/Feature', () => {
  beforeEach(() => {
    // Setup comum
  })

  it('should do something specific', async () => {
    // Arrange
    // Act
    // Assert
  })
})
```

### 3. Mocking

- Mock apenas o necessário
- Use dados realistas
- Mantenha mocks consistentes

### 4. Assertions

- Use matchers específicos
- Teste comportamentos, não implementação
- Aguarde estados assíncronos com `waitFor`

## Execução em CI/CD

Para integração contínua, use:

```bash
# Executar todos os testes sem watch
pnpm test:run

# Com cobertura para relatórios
pnpm test:coverage
```

## Troubleshooting

### Problemas Comuns

1. **Timeouts**: Aumente timeout para operações assíncronas
2. **Mocks não funcionando**: Verifique se o mock está no lugar correto
3. **DOM não encontrado**: Certifique-se de usar jsdom
4. **Erros de tipagem**: Verifique imports e tipos

### Debug

```bash
# Executar teste específico
pnpm test AppointmentCalendarSimple

# Com output detalhado
pnpm test --reporter=verbose

# Interface gráfica para debug
pnpm test:ui
```

## Contribuindo

Ao adicionar novos recursos:

1. Escreva testes para novos componentes
2. Mantenha cobertura acima de 80%
3. Teste cenários de erro
4. Documente comportamentos complexos
5. Execute toda a suíte antes do commit

## Recursos Adicionais

- [Vitest Documentation](https://vitest.dev/)
- [Testing Library Docs](https://testing-library.com/)
- [Jest-DOM Matchers](https://github.com/testing-library/jest-dom)