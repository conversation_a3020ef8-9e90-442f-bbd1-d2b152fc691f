import { useState } from "react";
import { useLocation } from "wouter";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { insertEquipmentSchema } from "@shared/schema";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Client, EquipmentCategory } from "@/lib/types";

// Adiciona regras de validação ao schema de equipamento
// Como estamos usando transform em insertEquipmentSchema, precisamos criar nosso próprio schema
const createEquipmentSchema = z.object({
  clientId: z.coerce.number().min(1, "Cliente é obrigatório"),
  categoryId: z.coerce.number().optional(),
  brand: z.string().optional().or(z.literal('')),
  model: z.string().optional().or(z.literal('')),
  serialNumber: z.string().optional().or(z.literal('')),
  description: z.string().min(3, "Descrição é obrigatória"),
  status: z.string().default("active"),
  purchaseDate: z.union([z.date(), z.string(), z.null()]).optional(),
  notes: z.string().optional().or(z.literal('')),
});

type FormValues = z.infer<typeof createEquipmentSchema>;

export default function NewEquipment() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Use the current URL to check if there's a client ID and return path in the query params
  const searchParams = typeof window !== 'undefined' 
    ? new URLSearchParams(window.location.search) 
    : new URLSearchParams();
  const preselectedClientId = searchParams.get('client');
  const returnTo = searchParams.get('returnTo');

  // Load clients for dropdown
  const { data: clients = [], isLoading: isLoadingClients } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  // Load categories for dropdown
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery<EquipmentCategory[]>({
    queryKey: ['/api/equipment-categories'],
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(createEquipmentSchema),
    defaultValues: {
      clientId: preselectedClientId ? parseInt(preselectedClientId) : undefined,
      brand: "",
      model: "",
      serialNumber: "",
      description: "",
      status: "active",
      purchaseDate: "",
      notes: "",
    },
  });

  const createEquipmentMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      return apiRequest("POST", "/api/equipment", data);
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['/api/equipment'] });
      if (preselectedClientId) {
        queryClient.invalidateQueries({ queryKey: [`/api/equipment/client/${preselectedClientId}`] });
      }
      toast({
        title: "Equipamento criado",
        description: "O equipamento foi adicionado com sucesso",
      });
      
      // Se temos um formulário salvo de ordem de serviço, mantemos os dados
      if (returnTo === '/service-orders/new') {
        // Não precisamos fazer nada especial, apenas redirecionar de volta
      }
      
      // Se tiver returnTo, redirecione para lá, caso contrário, use a lógica padrão
      if (returnTo) {
        navigate(returnTo);
      } else {
        navigate(preselectedClientId ? `/clients/${preselectedClientId}` : "/equipment");
      }
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar equipamento",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: FormValues) => {
    // Criar uma cópia dos valores para manipulação
    const formattedValues = { ...values };
    
    // Converter a data para o formato esperado pelo servidor
    if (values.purchaseDate && typeof values.purchaseDate === 'string' && values.purchaseDate.trim() !== '') {
      // Manter como string, a transformação no schema vai lidar com isso
      formattedValues.purchaseDate = values.purchaseDate;
    } else {
      // Se não temos data, enviamos null
      formattedValues.purchaseDate = null;
    }
    
    // Depuração
    console.log("Enviando dados:", formattedValues);
    
    // Enviar para o servidor
    createEquipmentMutation.mutate(formattedValues as any);
  };

  const handleCancel = () => {
    setShowCancelDialog(true);
  };

  const confirmCancel = () => {
    setShowCancelDialog(false);
    if (returnTo) {
      navigate(returnTo);
    } else {
      navigate(preselectedClientId ? `/clients/${preselectedClientId}` : "/equipment");
    }
  };

  const dismissCancel = () => {
    setShowCancelDialog(false);
  };

  const isLoading = isLoadingClients || isLoadingCategories;

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Novo Equipamento" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Cadastrar Novo Equipamento</CardTitle>
                <CardDescription>
                  Adicione um novo equipamento ao seu sistema para rastreamento e ordens de serviço.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Informações Básicas</h3>
                      
                      <FormField
                        control={form.control}
                        name="clientId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cliente*</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione o cliente" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {clients.map((client) => (
                                  <SelectItem key={client.id} value={client.id.toString()}>
                                    {client.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              O proprietário deste equipamento
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="categoryId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Categoria</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione a categoria (opcional)" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {categories.map((category) => (
                                  <SelectItem key={category.id} value={category.id.toString()}>
                                    {category.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="brand"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Marca</FormLabel>
                              <FormControl>
                                <Input placeholder="ex. Apple, Samsung, Dell" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="model"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Modelo</FormLabel>
                              <FormControl>
                                <Input placeholder="ex. iPhone 13, Galaxy S21" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="serialNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Número de Série</FormLabel>
                            <FormControl>
                              <Input placeholder="Número de Série ou ID" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Descrição*</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Descreva o equipamento" 
                                className="min-h-[80px]"
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Detalhes Adicionais</h3>
                      
                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione o status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="active">Ativo</SelectItem>
                                <SelectItem value="inactive">Inativo</SelectItem>
                                <SelectItem value="maintenance">Em Manutenção</SelectItem>
                                <SelectItem value="retired">Descontinuado</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="purchaseDate"
                        render={({ field }) => {
                          // Função para garantir o formato string do valor para exibição
                          let valueFormatted = "";
                          
                          if (field.value) {
                            if (typeof field.value === "string") {
                              valueFormatted = field.value;
                            } else if (field.value instanceof Date) {
                              valueFormatted = field.value.toISOString().split('T')[0];
                            }
                          }
                            
                          return (
                            <FormItem>
                              <FormLabel>Data de Compra</FormLabel>
                              <FormControl>
                                <Input
                                  type="date"
                                  {...field}
                                  value={valueFormatted}
                                  onChange={e => {
                                    // Apenas armazenamos o valor como string
                                    field.onChange(e.target.value || "");
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          );
                        }}
                      />
                      
                      <FormField
                        control={form.control}
                        name="notes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Observações</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Observações adicionais sobre o equipamento" 
                                className="min-h-[80px]"
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <div className="flex justify-end space-x-2">
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={handleCancel}
                      >
                        Cancelar
                      </Button>
                      <Button 
                        type="submit" 
                        disabled={createEquipmentMutation.isPending}
                        className="bg-primary hover:bg-primary-dark"
                      >
                        {createEquipmentMutation.isPending ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                            Salvando...
                          </div>
                        ) : (
                          "Cadastrar Equipamento"
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancelar Cadastro de Equipamento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja cancelar? Todas as informações inseridas serão perdidas.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={dismissCancel}>Não, continuar editando</AlertDialogCancel>
            <AlertDialogAction onClick={confirmCancel}>Sim, cancelar cadastro</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
