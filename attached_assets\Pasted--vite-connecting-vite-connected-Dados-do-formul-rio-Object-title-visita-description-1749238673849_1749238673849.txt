[vite] connecting...
[vite] connected.
Dados do formulário: 
Object {title: "visita", description: null, type: "technical_visit", status: "scheduled", appointmentDate: Date, …}
Dados processados para envio: 
Object {title: "visita", description: null, type: "technical_visit", status: "scheduled", appointmentDate: Date, …}
Enviando dados para API: 
Object {title: "visita", description: null, type: "technical_visit", status: "scheduled", appointmentDate: Date, …}
Erro ao fazer parse da resposta de erro: 
TypeError {}

at t.value (https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400
Erro na requisição: 
Error {}

at t.value (https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400
Erro ao criar agendamento: 
Error {}
message: "Erro HTTP scheduled: undefined"
stack: "Error: Erro HTTP scheduled: undefined↵ at Object.mutationFn (https://d57dc8dd-c308-4260-895b-2415c8b19caa-00-7xmjisro9v16.kirk.replit.dev/src/pages/appointments/new.tsx:108:17)"
get stack: ƒ ()
set stack: ƒ ()
[[Prototype]]: Object