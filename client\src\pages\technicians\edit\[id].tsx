import { useState, useEffect } from "react";
import { useRoute, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Save } from "lucide-react";
import { Link } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Technician, User } from "@/lib/types";
import { TECHNICIAN_STATUSES } from "@/lib/constants";

const editTechnicianSchema = z.object({
  specialties: z.string().min(1, "Especialidades são obrigatórias"),
  status: z.enum(['available', 'on_service', 'off_duty']),
  notes: z.string().optional(),
});

type EditTechnicianFormValues = z.infer<typeof editTechnicianSchema>;

export default function EditTechnicianPage() {
  const [, params] = useRoute("/technicians/edit/:id");
  const [, navigate] = useLocation();
  const technicianId = params?.id ? parseInt(params.id) : null;
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch technician data
  const { data: technician, isLoading: technicianLoading } = useQuery<Technician>({
    queryKey: ['/api/technicians', technicianId],
    enabled: !!technicianId
  });

  // Fetch user data for this technician
  const { data: user, isLoading: userLoading } = useQuery<User>({
    queryKey: ['/api/users', technician?.userId],
    enabled: !!technician?.userId
  });

  const form = useForm<EditTechnicianFormValues>({
    resolver: zodResolver(editTechnicianSchema),
    defaultValues: {
      specialties: "",
      status: "available",
      notes: "",
    },
  });

  // Update form when technician data loads
  useEffect(() => {
    if (technician) {
      form.reset({
        specialties: technician.specialties || "",
        status: technician.status as 'available' | 'on_service' | 'off_duty',
        notes: technician.notes || "",
      });
    }
  }, [technician, form]);

  const updateTechnicianMutation = useMutation({
    mutationFn: async (data: EditTechnicianFormValues) => {
      const response = await apiRequest(`/api/technicians/${technicianId}`, {
        method: "PATCH",
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        throw new Error("Falha ao atualizar técnico");
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Sucesso",
        description: "Técnico atualizado com sucesso!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/technicians'] });
      queryClient.invalidateQueries({ queryKey: ['/api/technicians', technicianId] });
      navigate(`/technicians/${technicianId}`);
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Falha ao atualizar técnico. Tente novamente.",
        variant: "destructive",
      });
      console.error("Error updating technician:", error);
    },
  });

  const onSubmit = (values: EditTechnicianFormValues) => {
    updateTechnicianMutation.mutate(values);
  };

  if (!technicianId) {
    return <div className="p-8 text-center">Técnico não encontrado</div>;
  }

  const isLoading = technicianLoading || userLoading;

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Editar Técnico" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          {isLoading ? (
            <div className="p-8 flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : !technician ? (
            <div className="p-8 text-center text-gray-500">
              Técnico não encontrado
            </div>
          ) : (
            <div className="max-w-2xl mx-auto space-y-6">
              {/* Header */}
              <div className="flex items-center gap-3">
                <Link href={`/technicians/${technician.id}`}>
                  <Button variant="outline" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    Voltar
                  </Button>
                </Link>
                <h1 className="text-2xl font-bold text-gray-900">
                  Editar Técnico: {user?.name}
                </h1>
              </div>

              {/* Edit Form */}
              <Card>
                <CardHeader>
                  <CardTitle>Informações do Técnico</CardTitle>
                </CardHeader>
                <CardContent>
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      {/* User Information (Read-only) */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                        <div>
                          <label className="text-sm font-medium text-gray-500">Nome</label>
                          <p className="text-sm text-gray-900">{user?.name || '-'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Email</label>
                          <p className="text-sm text-gray-900">{user?.email || '-'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Telefone</label>
                          <p className="text-sm text-gray-900">{user?.phone || '-'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Função</label>
                          <p className="text-sm text-gray-900">{user?.role === 'technician' ? 'Técnico' : user?.role || '-'}</p>
                        </div>
                      </div>

                      {/* Editable Fields */}
                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name="specialties"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Especialidades *</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Ex: Computadores, Celulares, Impressoras"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="status"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Status *</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Selecionar status" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {TECHNICIAN_STATUSES.map((status) => (
                                    <SelectItem key={status.value} value={status.value}>
                                      {status.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Observações</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Observações adicionais sobre o técnico..."
                                  rows={3}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Submit Button */}
                      <div className="flex justify-end space-x-3 pt-6">
                        <Link href={`/technicians/${technician.id}`}>
                          <Button variant="outline" type="button">
                            Cancelar
                          </Button>
                        </Link>
                        <Button 
                          type="submit" 
                          className="bg-primary hover:bg-primary-dark"
                          disabled={updateTechnicianMutation.isPending}
                        >
                          <Save className="h-4 w-4 mr-1" />
                          {updateTechnicianMutation.isPending ? "Salvando..." : "Salvar Alterações"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}