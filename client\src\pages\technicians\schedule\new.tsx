import { useState } from "react";
import { useLocation } from "wouter";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { SCHEDULE_STATUSES } from "@/lib/constants";
import { Technician, Client, ServiceOrder, User } from "@/lib/types";
import { format } from "date-fns";

// Esquema para validação do formulário
const createScheduleSchema = z.object({
  technicianId: z.coerce.number().min(1, "Técnico é obrigatório"),
  title: z.string().min(3, "Título deve ter pelo menos 3 caracteres"),
  description: z.string().optional().or(z.literal('')),
  scheduleDate: z.string().min(1, "Data é obrigatória"),
  startTime: z.string().min(1, "Hora de início é obrigatória"),
  endTime: z.string().min(1, "Hora de fim é obrigatória"),
  status: z.enum(['scheduled', 'in_progress', 'completed', 'cancelled']).default('scheduled'),
  location: z.string().optional().or(z.literal('')),
  clientId: z.coerce.number().optional(),
  serviceOrderId: z.coerce.number().optional(),
  isAllDay: z.boolean().default(false),
});

type FormValues = z.infer<typeof createScheduleSchema>;

export default function NewTechnicianSchedule() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Carregar técnicos para o dropdown
  const { data: technicians = [], isLoading: isLoadingTechnicians } = useQuery<Technician[]>({
    queryKey: ['/api/technicians'],
  });
  
  // Carregar usuários para obter os nomes dos técnicos
  const { data: users = [], isLoading: isLoadingUsers } = useQuery<User[]>({
    queryKey: ['/api/users'],
  });

  // Carregar clientes para o dropdown
  const { data: clients = [], isLoading: isLoadingClients } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  // Carregar ordens de serviço para o dropdown
  const { data: serviceOrders = [], isLoading: isLoadingServiceOrders } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
  });

  // Definir valores padrão para o formulário
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const defaultStartTime = "09:00";
  const defaultEndTime = "10:00";

  const form = useForm<FormValues>({
    resolver: zodResolver(createScheduleSchema),
    defaultValues: {
      title: "",
      description: "",
      scheduleDate: format(tomorrow, "yyyy-MM-dd"),
      startTime: defaultStartTime,
      endTime: defaultEndTime,
      status: "scheduled",
      location: "",
      isAllDay: false,
    },
  });

  const createScheduleMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      return apiRequest("POST", "/api/technician-schedules", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/technician-schedules'] });
      toast({
        title: "Agendamento criado",
        description: "O agendamento foi adicionado com sucesso",
      });
      navigate("/technicians/schedule");
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar agendamento",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: FormValues) => {
    createScheduleMutation.mutate(values);
  };

  const handleCancel = () => {
    setShowCancelDialog(true);
  };

  const confirmCancel = () => {
    setShowCancelDialog(false);
    navigate("/technicians/schedule");
  };

  const dismissCancel = () => {
    setShowCancelDialog(false);
  };

  const isLoading = isLoadingTechnicians || isLoadingClients || isLoadingServiceOrders || isLoadingUsers;

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Novo Agendamento" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Agendar Novo Compromisso</CardTitle>
                <CardDescription>
                  Agende uma visita técnica ou compromisso para um técnico.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Informações do Agendamento</h3>
                      
                      <FormField
                        control={form.control}
                        name="technicianId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Técnico*</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um técnico" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {technicians.map((technician) => {
                                  // Buscar o usuário correspondente
                                  const user = users.find(u => u.id === technician.userId);
                                  return (
                                    <SelectItem key={technician.id} value={technician.id.toString()}>
                                      {user?.name || `Técnico #${technician.id}`}
                                    </SelectItem>
                                  );
                                })}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Técnico responsável por este agendamento
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Título*</FormLabel>
                            <FormControl>
                              <Input placeholder="Ex: Visita técnica" {...field} />
                            </FormControl>
                            <FormDescription>
                              Um título curto para o agendamento
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Descrição</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Descreva o agendamento em detalhes" 
                                className="min-h-[100px]"
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormField
                          control={form.control}
                          name="scheduleDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Data*</FormLabel>
                              <FormControl>
                                <Input type="date" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="startTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Hora de Início*</FormLabel>
                              <FormControl>
                                <Input type="time" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="endTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Hora de Término*</FormLabel>
                              <FormControl>
                                <Input type="time" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="isAllDay"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Dia inteiro</FormLabel>
                              <FormDescription>
                                Marque esta opção se for um compromisso para o dia inteiro
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Detalhes Adicionais</h3>
                      
                      <FormField
                        control={form.control}
                        name="location"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Local</FormLabel>
                            <FormControl>
                              <Input placeholder="Ex: Endereço do cliente" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione o status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {SCHEDULE_STATUSES.map((status) => (
                                  <SelectItem key={status.value} value={status.value}>
                                    {status.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="clientId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cliente</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um cliente (opcional)" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="">Nenhum</SelectItem>
                                {clients.map((client) => (
                                  <SelectItem key={client.id} value={client.id.toString()}>
                                    {client.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Cliente relacionado a este agendamento (opcional)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="serviceOrderId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Ordem de Serviço</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione uma ordem de serviço (opcional)" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="">Nenhuma</SelectItem>
                                {serviceOrders.map((order) => (
                                  <SelectItem key={order.id} value={order.id.toString()}>
                                    {order.orderNumber} - {order.description.substring(0, 30)}...
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Ordem de serviço relacionada a este agendamento (opcional)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <div className="flex justify-end space-x-2">
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={handleCancel}
                      >
                        Cancelar
                      </Button>
                      <Button 
                        type="submit" 
                        disabled={createScheduleMutation.isPending}
                        className="bg-primary hover:bg-primary-dark"
                      >
                        {createScheduleMutation.isPending ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                            Salvando...
                          </div>
                        ) : (
                          "Criar Agendamento"
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancelar Criação de Agendamento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja cancelar? Todas as informações inseridas serão perdidas.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={dismissCancel}>Não, continuar editando</AlertDialogCancel>
            <AlertDialogAction onClick={confirmCancel}>Sim, cancelar criação</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}