import { useState } from "react";
import { Link } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { 
  Table, TableBody, TableCaption, TableCell, 
  TableHead, TableHeader, TableRow 
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Plus, Search, Filter, ArrowUpDown, AlertTriangle, Archive, BarChart, Package } from "lucide-react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";

export default function InventoryManagementPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("overview");

  // Dados do mock para as estatísticas
  const inventoryStats = {
    totalItems: 421,
    totalValue: 87500,
    lowStockItems: 15,
    categories: 8
  };

  // Dados do mock para categorias mais populares
  const topCategories = [
    { name: "Peças para notebooks", count: 98, percentaje: 23 },
    { name: "Componentes de rede", count: 76, percentaje: 18 },
    { name: "Memória e armazenamento", count: 65, percentaje: 15 },
    { name: "Periféricos", count: 52, percentaje: 12 }
  ];

  // Dados do mock para itens com baixo estoque
  const lowStockItems = [
    { id: 1, name: "SSD 240GB", category: "Armazenamento", quantity: 3, minQuantity: 5 },
    { id: 2, name: "Memória RAM 8GB DDR4", category: "Memória", quantity: 2, minQuantity: 10 },
    { id: 3, name: "Tela LCD 15.6\"", category: "Peças de notebook", quantity: 4, minQuantity: 8 },
    { id: 4, name: "Fonte ATX 500W", category: "Energia", quantity: 3, minQuantity: 6 },
    { id: 5, name: "Placa de rede WiFi", category: "Componentes de rede", quantity: 2, minQuantity: 5 }
  ];

  // Formatar valor em moeda
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Gerenciamento de Estoque" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="max-w-7xl mx-auto">
            <div className="mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold">Gestão de Inventário</h1>
                <p className="text-gray-500">Controle e gerenciamento avançado do inventário</p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">
                  <BarChart className="mr-2 h-4 w-4" />
                  Relatórios
                </Button>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Filtros
                </Button>
                <Link href="/inventory/new">
                  <Button className="bg-primary hover:bg-primary-dark">
                    <Plus className="mr-2 h-4 w-4" />
                    Adicionar Item
                  </Button>
                </Link>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList className="grid grid-cols-3 md:w-[400px]">
                <TabsTrigger value="overview">Visão Geral</TabsTrigger>
                <TabsTrigger value="categories">Categorias</TabsTrigger>
                <TabsTrigger value="low-stock">Estoque Baixo</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Total de Itens</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{inventoryStats.totalItems}</div>
                      <p className="text-xs text-muted-foreground">
                        Itens no inventário
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatCurrency(inventoryStats.totalValue)}</div>
                      <p className="text-xs text-muted-foreground">
                        Em estoque
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Estoque Baixo</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-amber-500">{inventoryStats.lowStockItems}</div>
                      <p className="text-xs text-muted-foreground">
                        Itens abaixo do mínimo
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Categorias</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{inventoryStats.categories}</div>
                      <p className="text-xs text-muted-foreground">
                        Categorias ativas
                      </p>
                    </CardContent>
                  </Card>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="col-span-1">
                    <CardHeader>
                      <CardTitle>Categorias Principais</CardTitle>
                      <CardDescription>
                        Distribuição de itens por categoria
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {topCategories.map((category, index) => (
                          <div key={index} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-primary"></div>
                                <span className="text-sm font-medium">{category.name}</span>
                              </div>
                              <span className="text-sm text-muted-foreground">
                                {category.count} ({category.percentaje}%)
                              </span>
                            </div>
                            <div className="h-2 w-full rounded-full bg-secondary">
                              <div 
                                className="h-2 rounded-full bg-primary" 
                                style={{ width: `${category.percentaje}%` }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card className="col-span-1">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle>Itens com Estoque Baixo</CardTitle>
                          <CardDescription>
                            Itens que precisam de reposição
                          </CardDescription>
                        </div>
                        <AlertTriangle className="h-5 w-5 text-amber-500" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Nome</TableHead>
                            <TableHead>Categoria</TableHead>
                            <TableHead className="text-right">Quantidade</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {lowStockItems.slice(0, 4).map((item) => (
                            <TableRow key={item.id}>
                              <TableCell className="font-medium">{item.name}</TableCell>
                              <TableCell>{item.category}</TableCell>
                              <TableCell className="text-right">
                                <Badge variant="outline" className="text-amber-500 border-amber-500">
                                  {item.quantity}/{item.minQuantity}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                      <div className="mt-4 text-center">
                        <Button variant="link" size="sm" onClick={() => setActiveTab("low-stock")}>
                          Ver todos os {inventoryStats.lowStockItems} itens
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Ações Rápidas</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Button variant="outline" className="flex flex-col h-24 items-center justify-center">
                        <Package className="h-6 w-6 mb-2" />
                        <span>Inventário Completo</span>
                      </Button>
                      <Button variant="outline" className="flex flex-col h-24 items-center justify-center">
                        <BarChart className="h-6 w-6 mb-2" />
                        <span>Relatório de Validade</span>
                      </Button>
                      <Button variant="outline" className="flex flex-col h-24 items-center justify-center">
                        <Archive className="h-6 w-6 mb-2" />
                        <span>Ajuste de Estoque</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="categories" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Categorias de Produtos</CardTitle>
                    <CardDescription>
                      Gerencie as categorias de produtos do seu inventário
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-20">
                      <Package className="h-10 w-10 mx-auto mb-4 text-gray-400" />
                      <h3 className="text-lg font-medium mb-2">Em desenvolvimento</h3>
                      <p className="text-gray-500 mb-4">
                        Esta funcionalidade estará disponível em breve.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="low-stock" className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Itens com Estoque Baixo</CardTitle>
                        <CardDescription>
                          Itens abaixo do nível mínimo de estoque configurado
                        </CardDescription>
                      </div>
                      <AlertTriangle className="h-5 w-5 text-amber-500" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Nome</TableHead>
                          <TableHead>Categoria</TableHead>
                          <TableHead className="text-center">Quantidade Atual</TableHead>
                          <TableHead className="text-center">Quantidade Mínima</TableHead>
                          <TableHead className="text-right">Ações</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {lowStockItems.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.name}</TableCell>
                            <TableCell>{item.category}</TableCell>
                            <TableCell className="text-center">
                              <Badge variant="outline" className="text-amber-500 border-amber-500">
                                {item.quantity}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-center">{item.minQuantity}</TableCell>
                            <TableCell className="text-right">
                              <Button variant="ghost" size="sm">
                                Reabastecer
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}