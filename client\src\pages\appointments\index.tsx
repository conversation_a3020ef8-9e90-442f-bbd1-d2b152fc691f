import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Plus, Calendar, Clock, MapPin, User, Search, CalendarDays, List, Trash2 } from "lucide-react";
import AppointmentCalendarSimple from "@/components/calendar/AppointmentCalendarSimple";
import { apiRequest } from "@/lib/queryClient";
import type { Appointment, Client } from "@shared/schema";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";

const statusLabels = {
  scheduled: "Agendado",
  confirmed: "Confirmado", 
  in_progress: "Em Andamento",
  completed: "Concluído",
  cancelled: "Cancelado",
  rescheduled: "Reagendado"
};

const statusColors = {
  scheduled: "bg-blue-100 text-blue-800",
  confirmed: "bg-green-100 text-green-800",
  in_progress: "bg-yellow-100 text-yellow-800", 
  completed: "bg-gray-100 text-gray-800",
  cancelled: "bg-red-100 text-red-800",
  rescheduled: "bg-purple-100 text-purple-800"
};

const appointmentTypeLabels = {
  technical_visit: "Visita Técnica",
  equipment_delivery: "Entrega de Equipamento",
  supplier_meeting: "Reunião com Fornecedor"
};

export default function AppointmentsPage() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("");
  const [filterStatus, setFilterStatus] = useState("");
  const [currentTab, setCurrentTab] = useState("list");

  const { data: appointments = [], isLoading, error } = useQuery({
    queryKey: ["/api/appointments"],
    queryFn: async () => {
      console.log("Carregando appointments...");
      const result = await apiRequest("GET", "/api/appointments");
      console.log("Appointments carregados:", result);
      return result;
    }
  });

  console.log("Estado da query:", { appointments, isLoading, error });

  const deleteAppointmentMutation = useMutation({
    mutationFn: async (appointmentId: number) => {
      const response = await apiRequest("DELETE", `/api/appointments/${appointmentId}`);
      return response;
    },
    onSuccess: async () => {
      toast({
        title: "Sucesso",
        description: "Agendamento excluído com sucesso!",
      });
      await queryClient.invalidateQueries({ queryKey: ["/api/appointments"] });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao excluir agendamento",
        variant: "destructive",
      });
    }
  });

  const { data: clients = [] } = useQuery({
    queryKey: ["/api/clients"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/clients");
      return response;
    }
  });

  const getClientName = (clientId: number | null) => {
    if (!clientId) return "-";
    const client = clients.find((c: any) => c.id === clientId);
    return client ? client.name : "-";
  };

  const filteredAppointments = appointments.filter((appointment: Appointment) => {
    const matchesSearch = !searchTerm || 
      appointment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      getClientName(appointment.clientId).toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = !filterType || appointment.type === filterType;
    const matchesStatus = !filterStatus || appointment.status === filterStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    return timeString ? timeString.substring(0, 5) : "";
  };

  if (isLoading) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Agendamentos" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden max-w-7xl mx-auto p-6">
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
                <span className="ml-2">Carregando agendamentos...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Agendamentos" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden max-w-7xl mx-auto p-6">
              <div className="flex items-center justify-center min-h-[400px] flex-col">
                <div className="text-red-500 text-lg font-medium mb-2">Erro ao carregar agendamentos</div>
                <div className="text-gray-600 mb-4">{error.toString()}</div>
                <Button
                  onClick={async () => {
                    await queryClient.invalidateQueries({ queryKey: ["/api/appointments"] });
                  }}
                >
                  Tentar novamente
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Agendamentos" />
        <main className="flex-1 overflow-y-auto bg-slate-50">
          <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-7xl">
            {/* Page Header - Remove duplicate title, keep only action */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
              <div className="min-w-0 flex-1">
                <p className="text-sm text-gray-600">Gerencie visitas técnicas, entregas e reuniões</p>
              </div>
              <div className="flex-shrink-0">
                <Link href="/appointments/new">
                  <Button className="w-full sm:w-auto">
                    <Plus className="w-4 h-4 mr-2" />
                    Novo Agendamento
                  </Button>
                </Link>
              </div>
            </div>

            {/* View Tabs */}
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
              <Tabs value={currentTab} onValueChange={setCurrentTab}>
                <TabsList className="grid w-full grid-cols-2 max-w-md">
                  <TabsTrigger value="list" className="flex items-center gap-2">
                    <List className="w-4 h-4" />
                    Lista
                  </TabsTrigger>
                  <TabsTrigger value="calendar" className="flex items-center gap-2">
                    <CalendarDays className="w-4 h-4" />
                    Calendário
                  </TabsTrigger>
                </TabsList>
              </Tabs>
              
              <Button
                variant="outline"
                size="sm"
                onClick={async () => {
                  console.log("Forçando reload dos dados...");
                  await queryClient.invalidateQueries({ queryKey: ["/api/appointments"] });
                  await queryClient.refetchQueries({ queryKey: ["/api/appointments"] });
                }}
              >
                🔄 Recarregar
              </Button>
            </div>
            
            {/* Filters Section - Only show for list view */}
            {currentTab === "list" && (
              <Card className="mb-6 shadow-sm">
                <CardContent className="p-4 sm:p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder="Buscar agendamentos..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-10"
                      />
                    </div>
                    <select
                      value={filterType}
                      onChange={(e) => setFilterType(e.target.value)}
                      className="h-10 px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                    >
                      <option value="">Todos os tipos</option>
                      <option value="technical_visit">Visita Técnica</option>
                      <option value="equipment_delivery">Entrega de Equipamento</option>
                      <option value="supplier_meeting">Reunião com Fornecedor</option>
                    </select>
                    <select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value)}
                      className="h-10 px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                    >
                      <option value="">Todos os status</option>
                      <option value="scheduled">Agendado</option>
                      <option value="confirmed">Confirmado</option>
                      <option value="in_progress">Em Andamento</option>
                      <option value="completed">Concluído</option>
                      <option value="cancelled">Cancelado</option>
                      <option value="rescheduled">Reagendado</option>
                    </select>
                    <div className="sm:col-span-2 lg:col-span-1 flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSearchTerm("");
                          setFilterType("");
                          setFilterStatus("");
                        }}
                        className="text-xs"
                      >
                        Limpar Filtros
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* Content based on current tab */}
            {currentTab === "list" && (
              <div className="space-y-4">
                {filteredAppointments.length === 0 ? (
                  <Card className="shadow-sm">
                    <CardContent className="py-12">
                      <div className="text-center">
                        <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          {appointments.length === 0 ? "Nenhum agendamento encontrado" : "Nenhum resultado"}
                        </h3>
                        <p className="text-gray-600 mb-4">
                          {appointments.length === 0 
                            ? "Comece criando seu primeiro agendamento."
                            : "Tente ajustar os filtros de busca."
                          }
                        </p>
                        {appointments.length === 0 && (
                          <Link href="/appointments/new">
                            <Button>
                              <Plus className="w-4 h-4 mr-2" />
                              Criar Agendamento
                            </Button>
                          </Link>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  filteredAppointments.map((appointment: Appointment) => (
                    <Card key={appointment.id} className="hover:shadow-md transition-shadow duration-200 shadow-sm">
                      <CardContent className="p-4 sm:p-6">
                        <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start gap-3 mb-4">
                              <div className="flex-1 min-w-0">
                                <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">
                                  {appointment.title}
                                </h3>
                                <div className="flex flex-wrap gap-2 mb-3">
                                  <Badge className={statusColors[appointment.status as keyof typeof statusColors]}>
                                    {statusLabels[appointment.status as keyof typeof statusLabels]}
                                  </Badge>
                                  <Badge variant="outline">
                                    {appointmentTypeLabels[appointment.type as keyof typeof appointmentTypeLabels]}
                                  </Badge>
                                </div>
                                {appointment.description && (
                                  <p className="text-gray-600 mb-3 text-sm leading-relaxed">{appointment.description}</p>
                                )}
                              </div>
                            </div>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                              <div className="flex items-center gap-2">
                                <Calendar className="w-4 h-4 text-gray-400 flex-shrink-0" />
                                <span className="truncate">{formatDate(String(appointment.appointmentDate))}</span>
                              </div>
                              {appointment.startTime && (
                                <div className="flex items-center gap-2">
                                  <Clock className="w-4 h-4 text-gray-400 flex-shrink-0" />
                                  <span className="truncate">{formatTime(appointment.startTime)}</span>
                                </div>
                              )}
                              {appointment.location && (
                                <div className="flex items-center gap-2">
                                  <MapPin className="w-4 h-4 text-gray-400 flex-shrink-0" />
                                  <span className="truncate">{appointment.location}</span>
                                </div>
                              )}
                              <div className="flex items-center gap-2">
                                <User className="w-4 h-4 text-gray-400 flex-shrink-0" />
                                <span className="truncate">Cliente: {getClientName(appointment.clientId)}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex-shrink-0 pt-2 lg:pt-0 flex gap-2">
                            <Link href={`/appointments/edit/${appointment.id}`}>
                              <Button variant="outline" size="sm">
                                Editar
                              </Button>
                            </Link>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50">
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Tem certeza que deseja excluir o agendamento "{appointment.title}"? 
                                    Esta ação não pode ser desfeita.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => deleteAppointmentMutation.mutate(appointment.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                    disabled={deleteAppointmentMutation.isPending}
                                  >
                                    {deleteAppointmentMutation.isPending ? "Excluindo..." : "Excluir"}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            )}

            {currentTab === "calendar" && (
              <AppointmentCalendarSimple 
                onSelectEvent={(event) => {
                  // Navigate to appointment details when clicking on event
                  window.location.href = `/appointments/edit/${event.id}`;
                }}
                onSelectSlot={(slotInfo) => {
                  // Navigate to new appointment form with pre-filled date
                  const dateStr = slotInfo.start.toISOString().split('T')[0];
                  window.location.href = `/appointments/new?date=${dateStr}`;
                }}
              />
            )}
          </div>
        </main>
      </div>
    </div>
  );
}