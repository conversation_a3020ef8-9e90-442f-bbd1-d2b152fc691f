import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Plus, MoreVertical, CreditCard, Edit, Trash2 } from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Definindo o tipo PaymentMethod
type PaymentMethod = {
  id: number;
  name: string;
  type: "credit_card" | "debit_card" | "bank_transfer" | "cash" | "pix" | "boleto";
  description: string | null;
  active: boolean | null;
  requiresApproval: boolean | null;
  requiresDocument: boolean | null;
  processingFee: number | null;
  additionalInfo: any;
  createdAt: Date | null;
};

// Schema para criação/edição de método de pagamento
const paymentMethodSchema = z.object({
  name: z.string().min(1, { message: "Nome é obrigatório" }),
  type: z.enum(["credit_card", "debit_card", "bank_transfer", "cash", "pix", "boleto"], {
    required_error: "Tipo de pagamento é obrigatório",
  }),
  description: z.string().nullable(),
  active: z.boolean().default(true),
  requiresApproval: z.boolean().default(false),
  requiresDocument: z.boolean().default(false),
  processingFee: z.coerce.number().min(0).max(100).nullable().default(0),
});

export default function PaymentMethodsSettingsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Estado para controle dos diálogos
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);

  // Form para criação
  const createForm = useForm<z.infer<typeof paymentMethodSchema>>({
    resolver: zodResolver(paymentMethodSchema),
    defaultValues: {
      name: "",
      type: "credit_card",
      description: "",
      active: true,
      requiresApproval: false,
      requiresDocument: false,
      processingFee: 0,
    },
  });

  // Form para edição
  const editForm = useForm<z.infer<typeof paymentMethodSchema>>({
    resolver: zodResolver(paymentMethodSchema),
    defaultValues: {
      name: "",
      type: "credit_card",
      description: "",
      active: true,
      requiresApproval: false,
      requiresDocument: false,
      processingFee: 0,
    },
  });

  // Consulta para buscar métodos de pagamento
  const { data: paymentMethods, isLoading } = useQuery({
    queryKey: ['/api/payment-methods'],
  });

  // Mutação para criar método de pagamento
  const createPaymentMethodMutation = useMutation({
    mutationFn: async (data: z.infer<typeof paymentMethodSchema>) => {
      return apiRequest('POST', '/api/payment-methods', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/payment-methods'] });
      toast({
        title: "Método de pagamento criado",
        description: "O método de pagamento foi criado com sucesso.",
      });
      setIsCreateDialogOpen(false);
      createForm.reset();
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar método de pagamento",
        description: "Ocorreu um erro ao criar o método de pagamento. Tente novamente.",
        variant: "destructive",
      });
    }
  });

  // Mutação para atualizar método de pagamento
  const updatePaymentMethodMutation = useMutation({
    mutationFn: async (data: z.infer<typeof paymentMethodSchema> & { id: number }) => {
      return apiRequest('PATCH', `/api/payment-methods/${data.id}`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/payment-methods'] });
      toast({
        title: "Método de pagamento atualizado",
        description: "O método de pagamento foi atualizado com sucesso.",
      });
      setIsEditDialogOpen(false);
      setSelectedPaymentMethod(null);
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar método de pagamento",
        description: "Ocorreu um erro ao atualizar o método de pagamento. Tente novamente.",
        variant: "destructive",
      });
    }
  });

  // Mutação para excluir método de pagamento
  const deletePaymentMethodMutation = useMutation({
    mutationFn: async (id: number) => {
      return apiRequest('DELETE', `/api/payment-methods/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/payment-methods'] });
      toast({
        title: "Método de pagamento excluído",
        description: "O método de pagamento foi excluído com sucesso.",
      });
      setIsDeleteDialogOpen(false);
      setSelectedPaymentMethod(null);
    },
    onError: (error) => {
      toast({
        title: "Erro ao excluir método de pagamento",
        description: "Ocorreu um erro ao excluir o método de pagamento. Tente novamente.",
        variant: "destructive",
      });
    }
  });

  // Handlers
  const onCreateSubmit = (values: z.infer<typeof paymentMethodSchema>) => {
    createPaymentMethodMutation.mutate(values);
  };

  const onEditSubmit = (values: z.infer<typeof paymentMethodSchema>) => {
    if (selectedPaymentMethod) {
      updatePaymentMethodMutation.mutate({ ...values, id: selectedPaymentMethod.id });
    }
  };

  const handleDelete = () => {
    if (selectedPaymentMethod) {
      deletePaymentMethodMutation.mutate(selectedPaymentMethod.id);
    }
  };

  const handleEdit = (method: PaymentMethod) => {
    setSelectedPaymentMethod(method);
    editForm.reset({
      name: method.name,
      type: method.type,
      description: method.description || "",
      active: method.active ?? true,
      requiresApproval: method.requiresApproval ?? false,
      requiresDocument: method.requiresDocument ?? false,
      processingFee: method.processingFee ?? 0,
    });
    setIsEditDialogOpen(true);
  };

  // Função para obter label do tipo de pagamento em português
  const getPaymentTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      credit_card: "Cartão de Crédito",
      debit_card: "Cartão de Débito",
      bank_transfer: "Transferência Bancária",
      cash: "Dinheiro",
      pix: "PIX",
      boleto: "Boleto",
    };
    return types[type] || type;
  };

  // Renderização dos tipos de pagamento para o select
  const renderPaymentTypeOptions = () => {
    const options = [
      { value: "credit_card", label: "Cartão de Crédito" },
      { value: "debit_card", label: "Cartão de Débito" },
      { value: "bank_transfer", label: "Transferência Bancária" },
      { value: "cash", label: "Dinheiro" },
      { value: "pix", label: "PIX" },
      { value: "boleto", label: "Boleto" },
    ];

    return options.map((option) => (
      <SelectItem key={option.value} value={option.value}>
        {option.label}
      </SelectItem>
    ));
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Métodos de Pagamento" />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="container mx-auto">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl font-semibold">Métodos de Pagamento</h2>
                <p className="text-gray-500 mt-1">
                  Gerencie os métodos de pagamento disponíveis no sistema.
                </p>
              </div>
              
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus size={16} />
                    Novo Método
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>Adicionar Método de Pagamento</DialogTitle>
                    <DialogDescription>
                      Preencha os detalhes do novo método de pagamento.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <Form {...createForm}>
                    <form onSubmit={createForm.handleSubmit(onCreateSubmit)} className="space-y-4">
                      <FormField
                        control={createForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Nome</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Ex: Cartão de Crédito Visa" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={createForm.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tipo</FormLabel>
                            <Select 
                              onValueChange={field.onChange} 
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione o tipo" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {renderPaymentTypeOptions()}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={createForm.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Descrição</FormLabel>
                            <FormControl>
                              <Textarea 
                                {...field} 
                                placeholder="Descrição adicional do método de pagamento"
                                className="resize-none h-20"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={createForm.control}
                          name="active"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between p-3 border rounded-md">
                              <div>
                                <FormLabel>Ativo</FormLabel>
                                <FormDescription className="text-xs">
                                  Disponível para uso
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={createForm.control}
                          name="requiresApproval"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between p-3 border rounded-md">
                              <div>
                                <FormLabel>Requer Aprovação</FormLabel>
                                <FormDescription className="text-xs">
                                  Verificação manual
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={createForm.control}
                          name="requiresDocument"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between p-3 border rounded-md">
                              <div>
                                <FormLabel>Requer Documento</FormLabel>
                                <FormDescription className="text-xs">
                                  Comprovante de pagamento
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={createForm.control}
                          name="processingFee"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Taxa de Processamento (%)</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  step="0.01" 
                                  min="0" 
                                  max="100"
                                  {...field}
                                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                  className="w-full"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <DialogFooter>
                        <Button 
                          type="button" 
                          variant="outline" 
                          onClick={() => setIsCreateDialogOpen(false)}
                        >
                          Cancelar
                        </Button>
                        <Button 
                          type="submit" 
                          disabled={createPaymentMethodMutation.isPending}
                        >
                          {createPaymentMethodMutation.isPending ? "Salvando..." : "Salvar"}
                        </Button>
                      </DialogFooter>
                    </form>
                  </Form>
                </DialogContent>
              </Dialog>
            </div>
            
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Taxa</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <div className="flex justify-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                          </div>
                          <span className="mt-2 block text-sm text-gray-500">Carregando métodos de pagamento...</span>
                        </TableCell>
                      </TableRow>
                    ) : paymentMethods && paymentMethods.length > 0 ? (
                      paymentMethods.map((method: PaymentMethod) => (
                        <TableRow key={method.id}>
                          <TableCell className="font-medium">{method.name}</TableCell>
                          <TableCell>{getPaymentTypeLabel(method.type)}</TableCell>
                          <TableCell>
                            {method.processingFee ? `${method.processingFee}%` : '-'}
                          </TableCell>
                          <TableCell>
                            <Badge variant={method.active ? "default" : "secondary"}>
                              {method.active ? "Ativo" : "Inativo"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreVertical size={16} />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Ações</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleEdit(method)}
                                  className="flex items-center cursor-pointer"
                                >
                                  <Edit size={14} className="mr-2" /> Editar
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => {
                                    setSelectedPaymentMethod(method);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                  className="flex items-center text-red-600 cursor-pointer"
                                >
                                  <Trash2 size={14} className="mr-2" /> Excluir
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <CreditCard size={24} className="mx-auto text-gray-400 mb-2" />
                          <p className="text-gray-500">Nenhum método de pagamento configurado.</p>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="mt-2"
                            onClick={() => setIsCreateDialogOpen(true)}
                          >
                            Adicionar método
                          </Button>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Dialog de Edição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Editar Método de Pagamento</DialogTitle>
            <DialogDescription>
              Atualize os detalhes do método de pagamento.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Ex: Cartão de Crédito Visa" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editForm.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {renderPaymentTypeOptions()}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Textarea 
                        {...field} 
                        placeholder="Descrição adicional do método de pagamento"
                        className="resize-none h-20"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between p-3 border rounded-md">
                      <div>
                        <FormLabel>Ativo</FormLabel>
                        <FormDescription className="text-xs">
                          Disponível para uso
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editForm.control}
                  name="requiresApproval"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between p-3 border rounded-md">
                      <div>
                        <FormLabel>Requer Aprovação</FormLabel>
                        <FormDescription className="text-xs">
                          Verificação manual
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="requiresDocument"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between p-3 border rounded-md">
                      <div>
                        <FormLabel>Requer Documento</FormLabel>
                        <FormDescription className="text-xs">
                          Comprovante de pagamento
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editForm.control}
                  name="processingFee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Taxa de Processamento (%)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01" 
                          min="0" 
                          max="100"
                          value={field.value || 0}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                          className="w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  disabled={updatePaymentMethodMutation.isPending}
                >
                  {updatePaymentMethodMutation.isPending ? "Salvando..." : "Salvar"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Dialog de Confirmação de Exclusão */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir o método de pagamento 
              <span className="font-medium">{selectedPaymentMethod?.name}</span>? 
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              {deletePaymentMethodMutation.isPending ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"/>
                  Excluindo...
                </>
              ) : (
                "Excluir"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}