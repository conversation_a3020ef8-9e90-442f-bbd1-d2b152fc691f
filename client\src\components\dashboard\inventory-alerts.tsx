import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { InventoryItem } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import { Link } from "wouter";

export default function InventoryAlerts() {
  const { data: lowStockItems = [], isLoading } = useQuery<InventoryItem[]>({
    queryKey: ['/api/inventory/low-stock'],
  });

  const criticalItems = lowStockItems.filter(item => item.quantity === 0 || item.quantity <= item.minQuantity / 2);
  const lowItems = lowStockItems.filter(item => item.quantity > item.minQuantity / 2 && item.quantity <= item.minQuantity);

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-800">Alertas de Estoque</h3>
        {criticalItems.length > 0 && (
          <Badge variant="destructive" className="rounded-full">
            {criticalItems.length} Crítico
          </Badge>
        )}
      </div>
      <div className="p-4">
        {isLoading ? (
          <div className="py-8 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : lowStockItems.length === 0 ? (
          <div className="py-8 text-center text-gray-500">
            Todos os itens do estoque estão em níveis aceitáveis.
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {/* Critical items first */}
            {criticalItems.map((item) => (
              <li key={item.id} className="py-3 flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">{item.name}</p>
                  <p className="text-xs text-gray-500">
                    {item.quantity} em estoque (mín: {item.minQuantity})
                  </p>
                </div>
                <Badge variant="destructive">Crítico</Badge>
              </li>
            ))}
            
            {/* Low items next */}
            {lowItems.map((item) => (
              <li key={item.id} className="py-3 flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-800">{item.name}</p>
                  <p className="text-xs text-gray-500">
                    {item.quantity} em estoque (mín: {item.minQuantity})
                  </p>
                </div>
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                  Baixo
                </Badge>
              </li>
            ))}
          </ul>
        )}
        
        <Link href="/inventory">
          <Button className="mt-3 w-full py-2 bg-primary text-white font-medium rounded-md hover:bg-primary-dark transition-colors">
            Comprar Suprimentos
          </Button>
        </Link>
      </div>
    </div>
  );
}
