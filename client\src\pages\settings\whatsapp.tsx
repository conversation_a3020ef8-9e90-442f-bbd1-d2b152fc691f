import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle, Send } from "lucide-react";

// Schema para validação do formulário de configuração do WhatsApp
const whatsappConfigSchema = z.object({
  apiToken: z.string().min(1, "Token da API é obrigatório"),
  phoneNumberId: z.string().min(1, "ID do número de telefone é obrigatório"),
});

type WhatsappConfigFormValues = z.infer<typeof whatsappConfigSchema>;

// Schema para validação do formulário de mensagem de teste
const testMessageSchema = z.object({
  to: z.string().min(1, "Número de telefone é obrigatório"),
  message: z.string().min(1, "Mensagem é obrigatória"),
});

type TestMessageFormValues = z.infer<typeof testMessageSchema>;

export default function WhatsAppSettings() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("configuration");

  // Buscar o status atual da configuração do WhatsApp
  const { data: whatsappStatus, isLoading: isLoadingStatus } = useQuery({
    queryKey: ["/api/whatsapp/status"],
  });

  // Formulário de configuração do WhatsApp
  const configForm = useForm<WhatsappConfigFormValues>({
    resolver: zodResolver(whatsappConfigSchema),
    defaultValues: {
      apiToken: "",
      phoneNumberId: "",
    },
  });

  // Formulário de mensagem de teste
  const testMessageForm = useForm<TestMessageFormValues>({
    resolver: zodResolver(testMessageSchema),
    defaultValues: {
      to: "",
      message: "Esta é uma mensagem de teste do Simplesmed TechServer.",
    },
  });

  // Mutation para atualizar configuração do WhatsApp
  const updateConfigMutation = useMutation({
    mutationFn: async (data: WhatsappConfigFormValues) => {
      return apiRequest("POST", "/api/whatsapp/config", data);
    },
    onSuccess: () => {
      toast({
        title: "Configuração atualizada",
        description: "As configurações do WhatsApp foram atualizadas com sucesso.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/status"] });
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar configuração",
        description: error.message || "Ocorreu um erro ao atualizar as configurações do WhatsApp.",
        variant: "destructive",
      });
    },
  });

  // Mutation para enviar mensagem de teste
  const sendTestMessageMutation = useMutation({
    mutationFn: async (data: TestMessageFormValues) => {
      return apiRequest("POST", "/api/whatsapp/test-message", data);
    },
    onSuccess: () => {
      toast({
        title: "Mensagem enviada",
        description: "A mensagem de teste foi enviada com sucesso.",
      });
      testMessageForm.reset({
        to: testMessageForm.getValues("to"),
        message: "Esta é uma mensagem de teste do Simplesmed TechServer.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao enviar mensagem",
        description: error.message || "Ocorreu um erro ao enviar a mensagem de teste.",
        variant: "destructive",
      });
    },
  });

  const onSubmitConfig = (values: WhatsappConfigFormValues) => {
    updateConfigMutation.mutate(values);
  };

  const onSubmitTestMessage = (values: TestMessageFormValues) => {
    sendTestMessageMutation.mutate(values);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Configurações do WhatsApp" />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="container mx-auto">
            <div className="mb-6">
              <h2 className="text-2xl font-semibold">Integração com WhatsApp</h2>
              <p className="text-gray-500 mt-1">
                Configure a integração com o WhatsApp Business API para enviar notificações aos clientes.
              </p>
            </div>

            <div className="mb-4">
              {!isLoadingStatus && (
                <Alert variant={whatsappStatus?.isConfigured ? "default" : "destructive"}>
                  {whatsappStatus?.isConfigured ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <AlertTitle>
                    Status: {whatsappStatus?.isConfigured ? "Configurado" : "Não configurado"}
                  </AlertTitle>
                  <AlertDescription>
                    {whatsappStatus?.isConfigured
                      ? "A integração com o WhatsApp está configurada e pronta para uso."
                      : "A integração com o WhatsApp não está configurada. Preencha os dados abaixo para configurar."}
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="configuration">Configuração</TabsTrigger>
                <TabsTrigger 
                  value="test-message" 
                  disabled={!whatsappStatus?.isConfigured}
                >
                  Mensagem de Teste
                </TabsTrigger>
                <TabsTrigger value="templates">Templates</TabsTrigger>
              </TabsList>
              
              <TabsContent value="configuration" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Configuração da API</CardTitle>
                    <CardDescription>
                      Configure as credenciais da API do WhatsApp Business.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...configForm}>
                      <form onSubmit={configForm.handleSubmit(onSubmitConfig)} className="space-y-4">
                        <FormField
                          control={configForm.control}
                          name="apiToken"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Token da API</FormLabel>
                              <FormControl>
                                <Input 
                                  type="password" 
                                  placeholder="Token de acesso à API do WhatsApp Business" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                Token de acesso fornecido pelo Meta for Developers.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={configForm.control}
                          name="phoneNumberId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ID do Número de Telefone</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="ID do número de telefone no WhatsApp Business" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                ID do número de telefone registrado na API do WhatsApp Business.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <Button 
                          type="submit" 
                          className="w-full mt-4"
                          disabled={updateConfigMutation.isPending}
                        >
                          {updateConfigMutation.isPending ? (
                            <div className="flex items-center">
                              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                              Salvando...
                            </div>
                          ) : (
                            "Salvar Configurações"
                          )}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="test-message" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Enviar Mensagem de Teste</CardTitle>
                    <CardDescription>
                      Envie uma mensagem de teste para verificar a integração.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...testMessageForm}>
                      <form onSubmit={testMessageForm.handleSubmit(onSubmitTestMessage)} className="space-y-4">
                        <FormField
                          control={testMessageForm.control}
                          name="to"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Número de Telefone</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="5511999999999" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                Número de telefone com código do país e DDD, sem caracteres especiais.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={testMessageForm.control}
                          name="message"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Mensagem</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="Sua mensagem de teste" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                Mensagem que será enviada para o número especificado.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <Button 
                          type="submit" 
                          className="w-full mt-4"
                          disabled={sendTestMessageMutation.isPending}
                        >
                          {sendTestMessageMutation.isPending ? (
                            <div className="flex items-center">
                              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                              Enviando...
                            </div>
                          ) : (
                            <div className="flex items-center justify-center">
                              <Send className="h-4 w-4 mr-2" />
                              Enviar Mensagem de Teste
                            </div>
                          )}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="templates" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Templates de Mensagens</CardTitle>
                    <CardDescription>
                      Gerencie seus modelos de mensagens para diferentes tipos de notificações.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="border rounded-md p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-medium">Status de Ordem de Serviço</h3>
                          <Badge>Obrigatório</Badge>
                        </div>
                        <p className="text-sm text-gray-500 mb-4">
                          Notifica o cliente sobre alterações no status de uma ordem de serviço.
                        </p>
                        <div className="bg-gray-100 p-3 rounded-md text-sm">
                          <p>Olá [1], sua ordem de serviço [2] teve seu status atualizado para [3]. Você pode verificar mais detalhes no nosso site ou entrar em contato conosco.</p>
                        </div>
                      </div>

                      <div className="border rounded-md p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-medium">Confirmação de Agendamento</h3>
                          <Badge>Obrigatório</Badge>
                        </div>
                        <p className="text-sm text-gray-500 mb-4">
                          Confirma um agendamento de visita técnica.
                        </p>
                        <div className="bg-gray-100 p-3 rounded-md text-sm">
                          <p>Olá [1], confirmamos seu agendamento para [2] no dia [3] às [4]. Nosso técnico estará no local no horário marcado.</p>
                        </div>
                      </div>

                      <div className="border rounded-md p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-medium">Orçamento Pronto</h3>
                          <Badge>Obrigatório</Badge>
                        </div>
                        <p className="text-sm text-gray-500 mb-4">
                          Notifica o cliente que um orçamento está disponível.
                        </p>
                        <div className="bg-gray-100 p-3 rounded-md text-sm">
                          <p>Olá [1], seu orçamento número [2] no valor de [3] está pronto e válido até [4]. Entre em contato para aprovação ou tirar dúvidas.</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-center">
                    <p className="text-sm text-gray-500">
                      Você deve criar estes templates no painel do WhatsApp Business antes de usar as notificações.
                    </p>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}