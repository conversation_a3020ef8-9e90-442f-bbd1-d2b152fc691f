/* ======= VARIÁVEIS E RESET ======= */
:root {
    /* Cores Principais */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --accent-color: #e74c3c;
    
    /* Cores Neutras */
    --dark-color: #2c3e50;
    --gray-dark: #34495e;
    --gray-medium: #7f8c8d;
    --gray-light: #ecf0f1;
    --light-color: #ffffff;
    
    /* Tipografia */
    --heading-font: 'Roboto', sans-serif;
    --body-font: '<PERSON><PERSON>', sans-serif;
    
    /* Dimensões */
    --container-width: 1200px;
    --header-height: 80px;
    --footer-height: 400px;
    --border-radius: 8px;
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 20px;
    --spacing-lg: 40px;
    --spacing-xl: 80px;
    
    /* Sombras */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    
    /* Transições */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset Básico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--body-font);
    font-size: 16px;
    line-height: 1.5;
    color: var(--dark-color);
    background-color: var(--light-color);
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--dark-color);
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Container principal */
.container {
    width: 100%;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
    position: relative;
    display: inline-block;
}

.section-header h2:after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
}

.section-header p {
    font-size: 1.1rem;
    color: var(--gray-medium);
    max-width: 600px;
    margin: var(--spacing-md) auto 0;
}

/* ======= BOTÕES ======= */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition-fast);
    border: none;
    outline: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--light-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    color: var(--light-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--light-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    color: var(--light-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--light-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* ======= HEADER ======= */
#header {
    background-color: rgba(255, 255, 255, 0.95);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--header-height);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    backdrop-filter: blur(10px);
    transition: var(--transition-medium);
}

#header.scrolled {
    height: 70px;
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

#header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
}

.logo a {
    display: flex;
    align-items: center;
    color: var(--dark-color);
}

.logo-text {
    color: var(--dark-color);
}

.logo-accent {
    color: var(--primary-color);
    margin-left: 4px;
}

nav {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-menu li {
    margin-left: var(--spacing-md);
}

.nav-link {
    color: var(--gray-dark);
    font-weight: 500;
    position: relative;
    padding: 8px 0;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-cta {
    margin-left: var(--spacing-md);
}

.hamburger {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
}

.bar {
    height: 3px;
    width: 100%;
    background-color: var(--dark-color);
    border-radius: 10px;
    transition: var(--transition-fast);
}

/* ======= HERO SECTION ======= */
.hero-section {
    padding: calc(var(--header-height) + var(--spacing-xl)) 0 var(--spacing-xl);
    background: linear-gradient(to right, var(--light-color), #f9f9f9);
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-section .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
}

.hero-content {
    flex: 1;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    position: relative;
}

.hero-image img {
    max-width: 100%;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    transform: perspective(1000px) rotateY(-5deg);
    transition: var(--transition-medium);
}

.hero-image img:hover {
    transform: perspective(1000px) rotateY(0);
}

.hero-section h1 {
    font-size: 3rem;
    margin-bottom: var(--spacing-sm);
    color: var(--dark-color);
    line-height: 1.2;
}

.hero-section h2 {
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--gray-dark);
    margin-bottom: var(--spacing-md);
}

.hero-section p {
    font-size: 1.1rem;
    color: var(--gray-medium);
    margin-bottom: var(--spacing-lg);
    max-width: 600px;
}

.hero-cta {
    display: flex;
    gap: var(--spacing-md);
}

/* ======= FEATURES SECTION ======= */
.features-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--gray-light);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-medium);
    text-align: center;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: var(--spacing-sm);
}

.feature-card p {
    color: var(--gray-medium);
    font-size: 0.95rem;
}

/* ======= BENEFITS SECTION ======= */
.benefits-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--light-color);
}

.benefits-wrapper {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.benefits-image {
    flex: 1;
}

.benefits-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.benefits-content {
    flex: 1;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.benefit-number {
    background-color: var(--primary-color);
    color: var(--light-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: var(--spacing-md);
    flex-shrink: 0;
}

.benefit-text h3 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xs);
}

.benefit-text p {
    color: var(--gray-medium);
    font-size: 0.95rem;
    margin: 0;
}

/* ======= PRICING SECTION ======= */
.pricing-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--gray-light);
}

.pricing-cards {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.pricing-card {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    flex: 1;
    min-width: 280px;
    max-width: 350px;
    position: relative;
    transition: var(--transition-medium);
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.pricing-card.featured {
    transform: scale(1.05);
    border: 2px solid var(--primary-color);
    z-index: 2;
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: -15px;
    right: var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--light-color);
    font-size: 0.8rem;
    font-weight: 700;
    padding: 5px 10px;
    border-radius: 20px;
}

.pricing-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-light);
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
}

.pricing-price {
    margin-bottom: var(--spacing-sm);
}

.currency {
    font-size: 1.2rem;
    font-weight: 700;
    vertical-align: top;
    line-height: 1;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1;
}

.period {
    font-size: 1rem;
    color: var(--gray-medium);
}

.pricing-header p {
    color: var(--gray-medium);
    font-size: 0.9rem;
    margin: 0;
}

.pricing-features {
    margin-bottom: var(--spacing-lg);
}

.pricing-features ul li {
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
}

.pricing-features i {
    color: var(--secondary-color);
    margin-right: var(--spacing-sm);
    font-size: 0.9rem;
}

.feature-disabled {
    color: var(--gray-medium);
    text-decoration: line-through;
}

.feature-disabled i {
    color: var(--gray-medium);
}

.pricing-footer {
    text-align: center;
}

/* ======= TESTIMONIALS SECTION ======= */
.testimonials-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--light-color);
}

.testimonials-slider {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
}

.testimonial-card {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    flex: 1;
    min-width: 280px;
    transition: var(--transition-medium);
    border: 1px solid var(--gray-light);
}

.testimonial-content {
    margin-bottom: var(--spacing-md);
}

.testimonial-content p {
    font-style: italic;
    color: var(--gray-dark);
    position: relative;
}

.testimonial-content p::before,
.testimonial-content p::after {
    content: '"';
    font-size: 1.5rem;
    color: var(--primary-color);
    font-family: serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: var(--spacing-md);
    border: 3px solid var(--primary-color);
}

.testimonial-info h4 {
    margin-bottom: 0;
    font-size: 1.1rem;
}

.testimonial-info p {
    margin: 0;
    color: var(--gray-medium);
    font-size: 0.9rem;
}

.testimonials-dots {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--gray-light);
    cursor: pointer;
    transition: var(--transition-fast);
}

.dot.active {
    background-color: var(--primary-color);
}

/* ======= CTA SECTION ======= */
.cta-section {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
    color: var(--light-color);
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-section h2 {
    color: var(--light-color);
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
}

.cta-section p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: var(--spacing-lg);
}

.cta-section .btn-primary {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.cta-section .btn-primary:hover {
    background-color: var(--gray-light);
}

/* ======= FOOTER ======= */
#footer {
    background-color: var(--dark-color);
    color: var(--gray-light);
    padding: var(--spacing-xl) 0 var(--spacing-lg);
}

.footer-columns {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.footer-column {
    flex: 1;
    min-width: 200px;
}

.footer-logo {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.footer-column h4 {
    color: var(--light-color);
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
    position: relative;
}

.footer-column h4::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-column ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-column ul li a {
    color: var(--gray-light);
    transition: var(--transition-fast);
}

.footer-column ul li a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.social-icons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.social-icons a {
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.social-icons a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-bottom {
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-links {
    display: flex;
    gap: var(--spacing-md);
}

.footer-links a {
    color: var(--gray-light);
    font-size: 0.9rem;
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* ======= MEDIA QUERIES ======= */
@media (max-width: 1024px) {
    .hero-section {
        padding: calc(var(--header-height) + var(--spacing-lg)) 0 var(--spacing-lg);
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .benefits-wrapper {
        flex-direction: column;
    }
    
    .pricing-cards {
        justify-content: center;
    }
    
    .pricing-card {
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    :root {
        --spacing-xl: 60px;
    }
    
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: var(--header-height);
        flex-direction: column;
        background-color: var(--light-color);
        width: 100%;
        text-align: center;
        transition: var(--transition-medium);
        box-shadow: var(--shadow-md);
        padding: var(--spacing-md) 0;
        z-index: 999;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: var(--spacing-sm) 0;
    }
    
    .nav-link {
        display: block;
        padding: var(--spacing-sm) 0;
    }
    
    .hero-section .container {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-content {
        order: 2;
    }
    
    .hero-image {
        order: 1;
        margin-bottom: var(--spacing-lg);
        justify-content: center;
    }
    
    .hero-cta {
        justify-content: center;
    }
    
    .benefits-image {
        margin-bottom: var(--spacing-lg);
    }
    
    .testimonials-slider {
        flex-direction: column;
    }
    
    .testimonial-card {
        margin-bottom: var(--spacing-md);
    }
    
    .footer-columns {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .footer-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    :root {
        --spacing-xl: 40px;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .hero-section h2 {
        font-size: 1.2rem;
    }
    
    .hero-cta {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-card {
        min-width: 100%;
    }
    
    .cta-section h2 {
        font-size: 1.8rem;
    }
}