import { useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Lock, ShieldCheck, ShieldOff, ShieldAlert, UserCog, BadgeHelp, Save, Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Tipos para permissões e relacionamentos
interface Permission {
  id: number;
  name: string;
  description: string;
  module: string;
}

interface RolePermission {
  roleId: string; // admin, technician, receptionist
  permissionId: number;
  canAccess: boolean;
}

// Módulos do sistema
const MODULES = [
  "clients",
  "equipment",
  "technicians",
  "inventory",
  "service_orders",
  "quotes",
  "invoices",
  "reports",
  "settings"
];

// Permissões pré-definidas por módulo
const PERMISSIONS: Permission[] = [
  // Clientes
  { id: 1, name: "client_view", description: "Visualizar clientes", module: "clients" },
  { id: 2, name: "client_create", description: "Criar clientes", module: "clients" },
  { id: 3, name: "client_edit", description: "Editar clientes", module: "clients" },
  { id: 4, name: "client_delete", description: "Desativar clientes", module: "clients" },
  
  // Equipamentos
  { id: 5, name: "equipment_view", description: "Visualizar equipamentos", module: "equipment" },
  { id: 6, name: "equipment_create", description: "Criar equipamentos", module: "equipment" },
  { id: 7, name: "equipment_edit", description: "Editar equipamentos", module: "equipment" },
  { id: 8, name: "equipment_delete", description: "Excluir equipamentos", module: "equipment" },
  
  // Técnicos
  { id: 9, name: "technician_view", description: "Visualizar técnicos", module: "technicians" },
  { id: 10, name: "technician_create", description: "Criar técnicos", module: "technicians" },
  { id: 11, name: "technician_edit", description: "Editar técnicos", module: "technicians" },
  { id: 12, name: "technician_schedule", description: "Gerenciar agenda", module: "technicians" },
  
  // Estoque
  { id: 13, name: "inventory_view", description: "Visualizar estoque", module: "inventory" },
  { id: 14, name: "inventory_create", description: "Adicionar itens", module: "inventory" },
  { id: 15, name: "inventory_edit", description: "Editar itens", module: "inventory" },
  { id: 16, name: "inventory_adjust", description: "Ajustar quantidades", module: "inventory" },
  
  // Ordens de serviço
  { id: 17, name: "service_order_view", description: "Visualizar ordens", module: "service_orders" },
  { id: 18, name: "service_order_create", description: "Criar ordens", module: "service_orders" },
  { id: 19, name: "service_order_edit", description: "Editar ordens", module: "service_orders" },
  { id: 20, name: "service_order_status", description: "Alterar status", module: "service_orders" },
  
  // Orçamentos
  { id: 21, name: "quote_view", description: "Visualizar orçamentos", module: "quotes" },
  { id: 22, name: "quote_create", description: "Criar orçamentos", module: "quotes" },
  { id: 23, name: "quote_edit", description: "Editar orçamentos", module: "quotes" },
  { id: 24, name: "quote_approve", description: "Aprovar/rejeitar", module: "quotes" },
  
  // Faturas/Pagamentos
  { id: 25, name: "invoice_view", description: "Visualizar faturas", module: "invoices" },
  { id: 26, name: "invoice_create", description: "Criar faturas", module: "invoices" },
  { id: 27, name: "invoice_payment", description: "Registrar pagamentos", module: "invoices" },
  
  // Relatórios
  { id: 28, name: "report_financials", description: "Relatórios financeiros", module: "reports" },
  { id: 29, name: "report_services", description: "Relatórios de serviços", module: "reports" },
  { id: 30, name: "report_technicians", description: "Relatórios de técnicos", module: "reports" },
  
  // Configurações
  { id: 31, name: "settings_view", description: "Visualizar configurações", module: "settings" },
  { id: 32, name: "settings_users", description: "Gerenciar usuários", module: "settings" },
  { id: 33, name: "settings_permissions", description: "Gerenciar permissões", module: "settings" },
  { id: 34, name: "settings_system", description: "Configurações do sistema", module: "settings" },
];

// Estado inicial de permissões por papel
const DEFAULT_ROLE_PERMISSIONS: RolePermission[] = [
  // Por padrão, administradores têm todas as permissões
  ...PERMISSIONS.map(p => ({ roleId: "admin", permissionId: p.id, canAccess: true })),
  
  // Recepcionistas têm acesso limitado
  ...PERMISSIONS.map(p => ({ 
    roleId: "receptionist", 
    permissionId: p.id, 
    // Permissões padrão para recepcionistas
    canAccess: ["client_view", "client_create", "client_edit", 
                "equipment_view", "service_order_view", "service_order_create",
                "invoice_view", "invoice_payment"].includes(p.name)
  })),
  
  // Técnicos têm acesso focado em ordens de serviço
  ...PERMISSIONS.map(p => ({ 
    roleId: "technician", 
    permissionId: p.id, 
    // Permissões padrão para técnicos
    canAccess: ["client_view", "equipment_view", 
                "service_order_view", "service_order_status",
                "inventory_view", "technician_schedule"].includes(p.name)
  })),
];

export default function PermissionsSettings() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("admin");
  const [rolePermissions, setRolePermissions] = useState<RolePermission[]>(DEFAULT_ROLE_PERMISSIONS);
  const [saving, setSaving] = useState(false);
  const [savedState, setSavedState] = useState(false);

  // Função para atualizar uma permissão individual
  const updatePermission = (roleId: string, permissionId: number, canAccess: boolean) => {
    setRolePermissions(prev => 
      prev.map(rp => 
        rp.roleId === roleId && rp.permissionId === permissionId
          ? { ...rp, canAccess }
          : rp
      )
    );
    setSavedState(false);
  };

  // Função para verificar se uma permissão está habilitada
  const isPermissionEnabled = (roleId: string, permissionId: number): boolean => {
    const permission = rolePermissions.find(
      rp => rp.roleId === roleId && rp.permissionId === permissionId
    );
    return permission ? permission.canAccess : false;
  };

  // Função fictícia para salvar as permissões
  // Em uma implementação real, isso enviaria os dados para o servidor
  const savePermissions = () => {
    setSaving(true);
    
    // Simular um request de API
    setTimeout(() => {
      setSaving(false);
      setSavedState(true);
      
      toast({
        title: "Permissões salvas",
        description: "As permissões foram atualizadas com sucesso",
      });
      
      // Remover mensagem de "salvo" após alguns segundos
      setTimeout(() => setSavedState(false), 3000);
    }, 1000);
  };

  // Função que retorna o ícone apropriado para a função selecionada
  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin":
        return <ShieldAlert className="h-5 w-5 mr-2" />;
      case "technician":
        return <UserCog className="h-5 w-5 mr-2" />;
      case "receptionist":
        return <BadgeHelp className="h-5 w-5 mr-2" />;
      default:
        return null;
    }
  };

  // Função que retorna o nome formatado da função
  const getRoleName = (role: string) => {
    switch (role) {
      case "admin":
        return "Administrador";
      case "technician":
        return "Técnico";
      case "receptionist":
        return "Recepcionista";
      default:
        return role;
    }
  };

  // Função que retorna o nome formatado do módulo
  const getModuleName = (module: string) => {
    switch (module) {
      case "clients":
        return "Clientes";
      case "equipment":
        return "Equipamentos";
      case "technicians":
        return "Técnicos";
      case "inventory":
        return "Estoque";
      case "service_orders":
        return "Ordens de Serviço";
      case "quotes":
        return "Orçamentos";
      case "invoices":
        return "Faturas";
      case "reports":
        return "Relatórios";
      case "settings":
        return "Configurações";
      default:
        return module;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Configurações > Permissões" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Controle de Permissões</CardTitle>
                <CardDescription>
                  Configure as permissões de acesso para cada função no sistema.
                </CardDescription>
              </div>
              <div className="flex items-center gap-3">
                {savedState && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                    <Check className="h-3 w-3" />
                    Salvo
                  </Badge>
                )}
                <Button onClick={savePermissions} disabled={saving} variant="default">
                  {saving ? (
                    <>
                      <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent border-white rounded-full"></div>
                      Salvando...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Salvar Alterações
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
                <TabsList className="grid grid-cols-3 mb-4">
                  <TabsTrigger value="admin" className="flex items-center justify-center">
                    <ShieldAlert className="h-4 w-4 mr-2" />
                    Administrador
                  </TabsTrigger>
                  <TabsTrigger value="technician" className="flex items-center justify-center">
                    <UserCog className="h-4 w-4 mr-2" />
                    Técnico
                  </TabsTrigger>
                  <TabsTrigger value="receptionist" className="flex items-center justify-center">
                    <BadgeHelp className="h-4 w-4 mr-2" />
                    Recepcionista
                  </TabsTrigger>
                </TabsList>
                
                {["admin", "technician", "receptionist"].map(role => (
                  <TabsContent key={role} value={role} className="p-2">
                    <div className="flex items-center mb-4">
                      {getRoleIcon(role)}
                      <h3 className="text-lg font-medium">{getRoleName(role)}</h3>
                    </div>
                    
                    <div className="rounded-md border overflow-hidden">
                      <Table>
                        <TableCaption>
                          Permissões para {getRoleName(role)}
                          {role === "admin" && (
                            <div className="mt-2 text-amber-600 flex items-center justify-center">
                              <ShieldAlert className="h-4 w-4 mr-1" />
                              Administradores sempre têm acesso completo ao sistema
                            </div>
                          )}
                        </TableCaption>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="min-w-[220px]">Módulo</TableHead>
                            <TableHead>Permissões</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {MODULES.map(module => (
                            <TableRow key={module}>
                              <TableCell className="font-medium">{getModuleName(module)}</TableCell>
                              <TableCell>
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                                  {PERMISSIONS
                                    .filter(perm => perm.module === module)
                                    .map(permission => (
                                      <div key={permission.id} className="flex items-start space-x-2">
                                        <Checkbox
                                          id={`${role}-${permission.id}`}
                                          checked={isPermissionEnabled(role, permission.id)}
                                          onCheckedChange={(checked) => {
                                            updatePermission(role, permission.id, !!checked);
                                          }}
                                          disabled={role === "admin"} // Administradores têm todas as permissões
                                          className="mt-1"
                                        />
                                        <div className="space-y-1 leading-none">
                                          <label
                                            htmlFor={`${role}-${permission.id}`}
                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                          >
                                            {permission.description}
                                          </label>
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    <div className="flex justify-end mt-4">
                      <Button onClick={savePermissions} disabled={saving} variant="default">
                        {saving ? (
                          <>
                            <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent border-white rounded-full"></div>
                            Salvando...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Salvar Alterações
                          </>
                        )}
                      </Button>
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}