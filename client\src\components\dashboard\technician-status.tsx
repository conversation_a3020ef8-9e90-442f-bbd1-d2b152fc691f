
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { 
  Users, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Phone,
  Mail,
  MapPin
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { ServiceOrder } from "@/lib/types";

interface Technician {
  id: number;
  userId: number;
  specialties: string;
  user?: {
    id: number;
    name: string;
    email: string;
    phone?: string;
  };
}

export default function TechnicianStatus() {
  const { data: technicians = [] } = useQuery<Technician[]>({
    queryKey: ['/api/technicians'],
  });

  const { data: serviceOrders = [] } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
  });

  // Calcular estatísticas por técnico
  const techniciansWithStats = technicians.map(tech => {
    const techOrders = serviceOrders.filter(order => order.technicianId === tech.id);
    const activeOrders = techOrders.filter(order => 
      order.status === 'in_progress' || order.status === 'waiting_parts'
    );
    const completedToday = techOrders.filter(order => 
      order.status === 'completed' && 
      new Date(order.updatedAt).toDateString() === new Date().toDateString()
    );

    let status = 'available';
    if (activeOrders.length > 3) status = 'busy';
    else if (activeOrders.length > 0) status = 'working';

    return {
      ...tech,
      activeOrders: activeOrders.length,
      completedToday: completedToday.length,
      totalOrders: techOrders.length,
      status
    };
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500';
      case 'working': return 'bg-yellow-500';
      case 'busy': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'available': return 'Disponível';
      case 'working': return 'Trabalhando';
      case 'busy': return 'Ocupado';
      default: return 'Offline';
    }
  };

  if (technicians.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Status dos Técnicos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Users className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <p className="text-muted-foreground mb-4">Nenhum técnico cadastrado</p>
            <Link href="/technicians/new">
              <Button>Cadastrar Primeiro Técnico</Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5" />
          Status dos Técnicos
        </CardTitle>
        <Link href="/technicians">
          <Button variant="outline" size="sm">
            Ver Todos
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {techniciansWithStats.slice(0, 4).map((tech) => (
            <div key={tech.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${tech.user?.name}`} />
                    <AvatarFallback>
                      {tech.user?.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'T'}
                    </AvatarFallback>
                  </Avatar>
                  <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(tech.status)}`} />
                </div>
                
                <div className="flex-1">
                  <Link href={`/technicians/${tech.id}`}>
                    <h4 className="font-medium hover:text-primary cursor-pointer">
                      {tech.user?.name || `Técnico ${tech.id}`}
                    </h4>
                  </Link>
                  <p className="text-sm text-muted-foreground truncate">
                    {tech.specialties}
                  </p>
                  <div className="flex items-center space-x-3 mt-1">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      {tech.activeOrders} ativas
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      {tech.completedToday} hoje
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <Badge 
                  variant="outline" 
                  className={`${getStatusColor(tech.status)} text-white border-0`}
                >
                  {getStatusLabel(tech.status)}
                </Badge>
              </div>
            </div>
          ))}
          
          {technicians.length > 4 && (
            <Link href="/technicians">
              <Button variant="outline" className="w-full">
                Ver Todos os {technicians.length} Técnicos
              </Button>
            </Link>
          )}
        </div>

        {/* Resumo Geral */}
        <div className="mt-6 pt-4 border-t grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-green-600">
              {techniciansWithStats.filter(t => t.status === 'available').length}
            </div>
            <div className="text-xs text-muted-foreground">Disponíveis</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-600">
              {techniciansWithStats.filter(t => t.status === 'working').length}
            </div>
            <div className="text-xs text-muted-foreground">Trabalhando</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-600">
              {techniciansWithStats.filter(t => t.status === 'busy').length}
            </div>
            <div className="text-xs text-muted-foreground">Ocupados</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
