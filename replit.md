# Simplesmed TechServer

## Overview

Simplesmed TechServer is a comprehensive full-stack web application designed to streamline technical service operations. The system manages the complete service lifecycle from order creation to delivery, including client management, equipment tracking, inventory control, and billing operations. Built with modern web technologies, it provides an intuitive interface for technical service businesses to manage their operations efficiently.

## System Architecture

The application follows a clean client-server architecture with clear separation of concerns:

```
Frontend (React SPA) ↔ Backend (Express API) ↔ Database (PostgreSQL)
```

**Frontend Architecture:**
- React 18 with TypeScript for type safety
- Vite for fast development and optimized builds
- Tailwind CSS with Radix UI components for consistent design
- React Query for server state management
- Wouter for lightweight routing
- React Hook Form with Zod validation for form handling

**Backend Architecture:**
- Node.js 20 with Express framework
- RESTful API design with clear endpoint structure
- Session-based authentication using Passport.js
- Drizzle ORM for database operations
- TypeScript for full-stack type safety

**Database Architecture:**
- PostgreSQL with schema managed by Drizzle Kit
- Comprehensive relational model covering all business entities
- Migration-based schema evolution

## Key Components

### Core Business Modules

1. **Service Order Management**
   - Complete lifecycle tracking (received → in_analysis → waiting_parts → waiting_approval → in_execution → completed → delivered)
   - Service order items and parts tracking
   - Assignment to technicians
   - Status updates and notifications

2. **Client Management**
   - Client registration and profile management
   - Equipment association
   - Service history tracking

3. **Equipment Management**
   - Equipment categorization and tracking
   - Serial number and warranty management
   - Client association and service history

4. **Inventory Control**
   - Parts and inventory item management
   - Stock level monitoring and alerts
   - Low stock notifications

5. **Technician Management**
   - Technician profiles and specialties
   - Schedule management
   - Performance tracking

6. **Financial Operations**
   - Quotes and invoicing
   - Payment processing with Stripe integration
   - Payment method management

### Technical Infrastructure

1. **Authentication & Authorization**
   - Session-based authentication with Passport.js
   - Role-based access control (admin, technician, receptionist)
   - Secure session management

2. **API Layer**
   - RESTful endpoints organized by resource
   - Consistent error handling and response formats
   - Input validation using Zod schemas

3. **Database Layer**
   - Drizzle ORM with type-safe queries
   - PostgreSQL with proper indexing
   - Migration-based schema management

4. **UI Components**
   - Reusable component library built on Radix UI
   - Responsive design with mobile support
   - Consistent theming and styling

## Data Flow

1. **User Authentication**: Users authenticate via login form, creating server-side sessions
2. **Data Fetching**: React Query manages server state with automatic caching and revalidation
3. **Form Submissions**: React Hook Form handles client-side validation, Zod schemas validate server-side
4. **Database Operations**: Drizzle ORM translates operations to SQL queries
5. **Real-time Updates**: Manual refresh model with optimistic updates via React Query

## External Dependencies

### Core Framework Dependencies
- **React Ecosystem**: React 18, React Query, React Hook Form
- **UI Framework**: Radix UI components, Tailwind CSS
- **Backend**: Express.js, Passport.js for authentication
- **Database**: PostgreSQL via Neon, Drizzle ORM
- **Development**: Vite, TypeScript, ESLint, Prettier

### Business Integration Dependencies
- **Payment Processing**: Stripe SDK for payment handling
- **Communication**: WhatsApp Business API for customer notifications
- **Email**: SendGrid for email communications
- **File Storage**: Local file system (can be extended for cloud storage)

### Development and Deployment
- **Package Manager**: PNPM for efficient dependency management
- **Environment**: Node.js 20 with ES modules
- **Build System**: Vite for frontend, esbuild for backend
- **Hosting**: Designed for Replit deployment with Nix environment

## Deployment Strategy

The application is designed for deployment on Replit with the following approach:

1. **Development Environment**: 
   - Vite dev server for frontend with hot module replacement
   - tsx for backend development with automatic restarts
   - Shared types and schemas for full-stack consistency

2. **Production Build**:
   - Frontend: Vite builds optimized static assets
   - Backend: esbuild creates bundled Node.js application
   - Database: Neon PostgreSQL with connection pooling

3. **Configuration**:
   - Environment variables for database connections and API keys
   - Session secrets and external service configurations
   - Drizzle migrations for database schema management

4. **Static Assets**:
   - Landing page served separately for marketing
   - Built React app served from Express in production
   - Asset optimization and caching strategies

## Changelog

Changelog:
- July 03, 2025. Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.