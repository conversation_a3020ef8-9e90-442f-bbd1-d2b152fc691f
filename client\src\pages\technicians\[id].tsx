import { useState, useEffect } from "react";
import { useRoute } from "wouter";
import { useQuery } from "@tanstack/react-query";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Edit, 
  Calendar, 
  User as UserIcon, 
  Mail, 
  Phone, 
  Wrench,
  Clock,
  MapPin,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";
import { Link } from "wouter";
import type { Technician, User, TechnicianSchedule } from "@/lib/types";
import { TECHNICIAN_STATUSES, SCHEDULE_STATUSES } from "@/lib/constants";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

export default function TechnicianDetailsPage() {
  const [, params] = useRoute("/technicians/:id");
  const technicianId = params?.id ? parseInt(params.id) : null;
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Fetch technician data
  const { data: technician, isLoading: technicianLoading } = useQuery<Technician>({
    queryKey: ['/api/technicians', technicianId],
    enabled: !!technicianId
  });

  // Fetch user data for this technician
  const { data: user, isLoading: userLoading } = useQuery<User>({
    queryKey: ['/api/users', technician?.userId],
    enabled: !!technician?.userId
  });

  // Fetch technician schedules
  const { data: schedules = [], isLoading: schedulesLoading } = useQuery<TechnicianSchedule[]>({
    queryKey: ['/api/technician-schedules/technician', technicianId],
    enabled: !!technicianId
  });

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'on_service':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'off_duty':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    const statusObj = TECHNICIAN_STATUSES.find(s => s.value === status);
    return statusObj?.label || status;
  };

  const getScheduleStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getScheduleStatusLabel = (status: string) => {
    const statusObj = SCHEDULE_STATUSES.find(s => s.value === status);
    return statusObj?.label || status;
  };

  const getScheduleStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      case 'in_progress':
        return <Clock className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  if (!technicianId) {
    return <div className="p-8 text-center">Técnico não encontrado</div>;
  }

  const isLoading = technicianLoading || userLoading;

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Detalhes do Técnico" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          {isLoading ? (
            <div className="p-8 flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : !technician ? (
            <div className="p-8 text-center text-gray-500">
              Técnico não encontrado
            </div>
          ) : (
            <div className="space-y-6">
              {/* Header Actions */}
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                  <Link href="/technicians">
                    <Button variant="outline" size="sm">
                      <ArrowLeft className="h-4 w-4 mr-1" />
                      Voltar
                    </Button>
                  </Link>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {user?.name || 'Carregando...'}
                  </h1>
                </div>
                <div className="flex gap-2">
                  <Link href={`/technicians/edit/${technician.id}`}>
                    <Button className="bg-primary hover:bg-primary-dark">
                      <Edit className="h-4 w-4 mr-1" />
                      Editar Técnico
                    </Button>
                  </Link>
                </div>
              </div>

              {/* Technician Information Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <UserIcon className="h-5 w-5" />
                    Informações do Técnico
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <UserIcon className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">Nome:</span>
                        <span>{user?.name || '-'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">Email:</span>
                        <span>{user?.email || '-'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">Telefone:</span>
                        <span>{user?.phone || '-'}</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Wrench className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">Especialidades:</span>
                        <span>{technician.specialties || '-'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">Status:</span>
                        <Badge className={getStatusBadgeColor(technician.status)}>
                          {getStatusLabel(technician.status)}
                        </Badge>
                      </div>
                      {technician.notes && (
                        <div className="flex items-start gap-2">
                          <span className="font-medium">Observações:</span>
                          <span className="text-gray-600">{technician.notes}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Schedules */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Agendamentos Recentes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {schedulesLoading ? (
                    <div className="p-4 flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                    </div>
                  ) : schedules.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      Nenhum agendamento encontrado
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {schedules.slice(0, 5).map((schedule) => (
                        <div key={schedule.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              {getScheduleStatusIcon(schedule.status)}
                              <h4 className="font-medium">{schedule.title}</h4>
                              <Badge className={getScheduleStatusBadgeColor(schedule.status)}>
                                {getScheduleStatusLabel(schedule.status)}
                              </Badge>
                            </div>
                            {schedule.description && (
                              <p className="text-sm text-gray-600 mb-2">{schedule.description}</p>
                            )}
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {format(new Date(schedule.scheduleDate), "dd/MM/yyyy", { locale: ptBR })}
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {schedule.startTime} - {schedule.endTime}
                              </div>
                              {schedule.location && (
                                <div className="flex items-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  {schedule.location}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                      {schedules.length > 5 && (
                        <div className="text-center pt-2">
                          <Link href={`/schedules?technician=${technician.id}`}>
                            <Button variant="outline" size="sm">
                              Ver Todos os Agendamentos ({schedules.length})
                            </Button>
                          </Link>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}