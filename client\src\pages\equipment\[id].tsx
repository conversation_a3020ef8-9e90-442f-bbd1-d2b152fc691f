import { useState } from "react";
import { use<PERSON>ara<PERSON>, useLocation } from "wouter";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Equipment, Client, EquipmentCategory, ServiceOrder } from "@/lib/types";
import { formatDateTime, getStatusColor, getStatusLabel } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertD<PERSON>ogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Link } from "wouter";
import {
  Edit,
  Trash,
  Plus,
  Eye,
  Calendar,
  Tag,
  FileText,
  User,
  Clipboard,
  Info,
} from "lucide-react";

export default function EquipmentDetails() {
  const params = useParams<{ id: string }>();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const equipmentId = parseInt(params.id);
  
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Load equipment details
  const {
    data: equipment,
    isLoading: isLoadingEquipment,
    isError,
    error,
  } = useQuery<Equipment>({
    queryKey: [`/api/equipment/${equipmentId}`],
    enabled: !isNaN(equipmentId),
  });

  // Load client details
  const { data: clients = [], isLoading: isLoadingClients } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  // Load categories
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery<EquipmentCategory[]>({
    queryKey: ['/api/equipment-categories'],
  });

  // Load service orders
  const { data: allServiceOrders = [] } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
    enabled: !isNaN(equipmentId),
  });

  // Filter service orders for this equipment
  const equipmentServiceOrders = allServiceOrders.filter(
    (order) => order.equipmentId === equipmentId
  );

  // Mutation for deleting equipment
  const deleteEquipmentMutation = useMutation({
    mutationFn: async () => {
      return apiRequest("DELETE", `/api/equipment/${equipmentId}`, undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/equipment'] });
      toast({
        title: "Equipamento excluído",
        description: "O equipamento foi excluído com sucesso",
      });
      navigate("/equipment");
    },
    onError: (error) => {
      toast({
        title: "Erro ao excluir equipamento",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });



  const handleDelete = () => {
    deleteEquipmentMutation.mutate();
  };

  const client = equipment ? clients.find(c => c.id === equipment.clientId) : undefined;
  const category = equipment?.categoryId ? categories.find(c => c.id === equipment.categoryId) : undefined;

  const isLoading = isLoadingEquipment || isLoadingClients || isLoadingCategories;

  if (isLoading) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes do Equipamento" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes do Equipamento" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-red-500">Erro</CardTitle>
                <CardDescription>
                  Falha ao carregar detalhes do equipamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-red-500">{error?.message || "Ocorreu um erro desconhecido"}</p>
                <Button 
                  onClick={() => navigate("/equipment")} 
                  className="mt-4"
                >
                  Voltar para Equipamentos
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!equipment) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes do Equipamento" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <Card>
              <CardHeader>
                <CardTitle>Não Encontrado</CardTitle>
                <CardDescription>
                  Equipamento não encontrado
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>O equipamento que você está procurando não existe ou foi excluído.</p>
                <Button 
                  onClick={() => navigate("/equipment")} 
                  className="mt-4"
                >
                  Voltar para Equipamentos
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  const equipmentName = `${equipment.brand || ''} ${equipment.model || ''}`.trim() || equipment.description;

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title={equipmentName} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="mb-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-800 mr-3">
                {equipmentName}
              </h1>
              <Badge
                variant={equipment.status === "active" ? "default" : "secondary"}
                className={
                  equipment.status === "active"
                    ? "bg-green-100 text-green-800"
                    : equipment.status === "inactive"
                    ? "bg-gray-100 text-gray-800"
                    : equipment.status === "maintenance"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }
              >
                {equipment.status === "active" ? "Ativo" : 
                 equipment.status === "inactive" ? "Inativo" : 
                 equipment.status === "maintenance" ? "Em Manutenção" : 
                 equipment.status === "retired" ? "Descontinuado" : 
                 'Desconhecido'}
              </Badge>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Link href={`/equipment/${equipmentId}/edit`}>
                <Button 
                  variant="outline" 
                  size="sm"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Editar Equipamento
                </Button>
              </Link>
              
              <Button 
                variant="outline" 
                size="sm"
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={() => setShowDeleteDialog(true)}
              >
                <Trash className="h-4 w-4 mr-1" />
                Excluir
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <div className="lg:col-span-2">
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="overview">Visão Geral</TabsTrigger>
                  <TabsTrigger value="service-history">Histórico de Serviços ({equipmentServiceOrders.length})</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview">
                  <Card>
                    <CardHeader>
                      <CardTitle>Detalhes do Equipamento</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Informações Básicas</h3>
                              <div className="mt-2 space-y-2">
                                {equipment.brand && (
                                  <div className="flex items-center">
                                    <Tag className="h-4 w-4 text-gray-400 mr-2" />
                                    <span className="font-medium">Marca:</span> 
                                    <span className="ml-2">{equipment.brand}</span>
                                  </div>
                                )}
                                {equipment.model && (
                                  <div className="flex items-center">
                                    <Info className="h-4 w-4 text-gray-400 mr-2" />
                                    <span className="font-medium">Modelo:</span>
                                    <span className="ml-2">{equipment.model}</span>
                                  </div>
                                )}
                                {equipment.serialNumber && (
                                  <div className="flex items-center">
                                    <Clipboard className="h-4 w-4 text-gray-400 mr-2" />
                                    <span className="font-medium">Número de Série:</span>
                                    <span className="ml-2">{equipment.serialNumber}</span>
                                  </div>
                                )}
                                {category && (
                                  <div className="flex items-center">
                                    <Tag className="h-4 w-4 text-gray-400 mr-2" />
                                    <span className="font-medium">Categoria:</span>
                                    <span className="ml-2">{category.name}</span>
                                  </div>
                                )}
                                {equipment.purchaseDate && (
                                  <div className="flex items-center">
                                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                                    <span className="font-medium">Data de Compra:</span>
                                    <span className="ml-2">{formatDateTime(equipment.purchaseDate, { 
                                      year: 'numeric', 
                                      month: 'long', 
                                      day: 'numeric' 
                                    })}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <div className="space-y-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Informações do Proprietário</h3>
                              <div className="mt-2 space-y-2">
                                {client && (
                                  <div>
                                    <div className="flex items-center">
                                      <User className="h-4 w-4 text-gray-400 mr-2" />
                                      <span className="font-medium">Cliente:</span>
                                      <Link href={`/clients/${client.id}`} className="ml-2 text-primary hover:underline">
                                        {client.name}
                                      </Link>
                                    </div>
                                    {client.phone && (
                                      <div className="flex items-center mt-1 ml-6">
                                        <span className="text-sm text-gray-600">{client.phone}</span>
                                      </div>
                                    )}
                                    {client.email && (
                                      <div className="flex items-center mt-1 ml-6">
                                        <span className="text-sm text-gray-600">{client.email}</span>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Resumo de Serviços</h3>
                              <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
                                <div className="rounded-md bg-gray-50 p-2">
                                  <p className="text-gray-500">Total de Serviços</p>
                                  <p className="text-lg font-semibold">{equipmentServiceOrders.length}</p>
                                </div>
                                <div className="rounded-md bg-gray-50 p-2">
                                  <p className="text-gray-500">Último Serviço</p>
                                  <p className="text-lg font-semibold">
                                    {equipmentServiceOrders.length > 0
                                      ? formatDateTime(
                                          [...equipmentServiceOrders].sort(
                                            (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
                                          )[0].updatedAt,
                                          { month: 'short', day: 'numeric', year: 'numeric' }
                                        )
                                      : "Nunca"}
                                  </p>
                                </div>
                                <div className="rounded-md bg-gray-50 p-2">
                                  <p className="text-gray-500">Serviços Ativos</p>
                                  <p className="text-lg font-semibold">{
                                    equipmentServiceOrders.filter(order => 
                                      order.status !== 'completed' && order.status !== 'delivered'
                                    ).length
                                  }</p>
                                </div>
                                <div className="rounded-md bg-gray-50 p-2">
                                  <p className="text-gray-500">Status</p>
                                  <p className="text-lg font-semibold">
                                    {equipment.status === "active" ? "Ativo" : 
                                     equipment.status === "inactive" ? "Inativo" : 
                                     equipment.status === "maintenance" ? "Em Manutenção" : 
                                     equipment.status === "retired" ? "Descontinuado" : 
                                     "Desconhecido"}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        {equipment.description && (
                          <div>
                            <Separator className="my-4" />
                            <h3 className="text-sm font-medium text-gray-500 mb-2">Descrição</h3>
                            <p className="text-gray-800 whitespace-pre-line">{equipment.description}</p>
                          </div>
                        )}
                        
                        {equipment.notes && (
                          <div>
                            <Separator className="my-4" />
                            <h3 className="text-sm font-medium text-gray-500 mb-2">Observações</h3>
                            <p className="text-gray-800 whitespace-pre-line">{equipment.notes}</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between border-t pt-5">
                      <Button 
                        variant="outline" 
                        onClick={() => navigate("/equipment")}
                      >
                        Voltar para Equipamentos
                      </Button>
                      <Link href={`/service-orders/new?equipment=${equipmentId}`}>
                        <Button className="bg-primary hover:bg-primary-dark">
                          <Plus className="h-4 w-4 mr-1" />
                          Nova Ordem de Serviço
                        </Button>
                      </Link>
                    </CardFooter>
                  </Card>
                </TabsContent>
                
                <TabsContent value="service-history">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <div>
                        <CardTitle>Histórico de Serviços</CardTitle>
                        <CardDescription>
                          Todas as ordens de serviço para este equipamento
                        </CardDescription>
                      </div>
                      <Link href={`/service-orders/new?equipment=${equipmentId}&client=${equipment.clientId}`}>
                        <Button className="bg-primary hover:bg-primary-dark">
                          <Plus className="h-4 w-4 mr-1" />
                          Nova Ordem de Serviço
                        </Button>
                      </Link>
                    </CardHeader>
                    <CardContent>
                      {equipmentServiceOrders.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          Nenhum histórico de serviço para este equipamento ainda
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Ordem #</TableHead>
                              <TableHead>Descrição</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Criado em</TableHead>
                              <TableHead>Última Atualização</TableHead>
                              <TableHead className="text-right">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {[...equipmentServiceOrders]
                              .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                              .map((order) => {
                                const statusColors = getStatusColor(order.status);
                                return (
                                  <TableRow key={order.id}>
                                    <TableCell className="font-medium">#{order.orderNumber}</TableCell>
                                    <TableCell className="max-w-xs truncate">{order.description}</TableCell>
                                    <TableCell>
                                      <Badge className={`${statusColors.bg} ${statusColors.text}`}>
                                        {getStatusLabel(order.status)}
                                      </Badge>
                                    </TableCell>
                                    <TableCell>{formatDateTime(order.createdAt)}</TableCell>
                                    <TableCell>{formatDateTime(order.updatedAt)}</TableCell>
                                    <TableCell className="text-right">
                                      <Link href={`/service-orders/${order.id}`}>
                                        <Button variant="ghost" size="sm">
                                          <Eye className="h-4 w-4 mr-1" />
                                          Visualizar
                                        </Button>
                                      </Link>
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                          </TableBody>
                        </Table>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
            
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Ações Rápidas</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Link href={`/service-orders/new?equipment=${equipmentId}&client=${equipment.clientId}`}>
                    <Button className="w-full justify-start bg-primary hover:bg-primary-dark">
                      <FileText className="h-4 w-4 mr-2" />
                      Nova Ordem de Serviço
                    </Button>
                  </Link>
                  <Link href={`/equipment/${equipmentId}/edit`}>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Editar Equipamento
                    </Button>
                  </Link>
                  <Link href={`/clients/${equipment.clientId}`}>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                    >
                      <User className="h-4 w-4 mr-2" />
                      Ver Cliente
                    </Button>
                  </Link>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Ordens de Serviço Ativas</CardTitle>
                </CardHeader>
                <CardContent>
                  {equipmentServiceOrders.filter(
                    order => order.status !== 'completed' && order.status !== 'delivered'
                  ).length === 0 ? (
                    <p className="text-center text-gray-500 py-2">Nenhuma ordem de serviço ativa</p>
                  ) : (
                    <div className="space-y-3">
                      {equipmentServiceOrders
                        .filter(order => order.status !== 'completed' && order.status !== 'delivered')
                        .slice(0, 5)
                        .map(order => {
                          const statusColors = getStatusColor(order.status);
                          return (
                            <Link key={order.id} href={`/service-orders/${order.id}`}>
                              <div className="border rounded-md p-3 hover:bg-gray-50 cursor-pointer">
                                <div className="flex justify-between items-start">
                                  <p className="font-medium">#{order.orderNumber}</p>
                                  <Badge className={`${statusColors.bg} ${statusColors.text}`}>
                                    {getStatusLabel(order.status)}
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-500 mt-1 truncate">{order.description}</p>
                                <p className="text-xs text-gray-400 mt-1">
                                  {formatDateTime(order.updatedAt)}
                                </p>
                              </div>
                            </Link>
                          );
                        })
                      }
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Equipamento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este equipamento? Esta ação não pode ser desfeita.
              Todas as ordens de serviço associadas permanecerão no sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-500 hover:bg-red-600"
            >
              {deleteEquipmentMutation.isPending ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Excluindo...
                </div>
              ) : (
                "Excluir"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
