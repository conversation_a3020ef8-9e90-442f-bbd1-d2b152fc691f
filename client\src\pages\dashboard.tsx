import { useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import StatsCard from "@/components/dashboard/stats-card";
import RecentServiceOrders from "@/components/dashboard/recent-service-orders";
import QuickActions from "@/components/dashboard/quick-actions";
import TechnicianStatus from "@/components/dashboard/technician-status";
import InventoryAlerts from "@/components/dashboard/inventory-alerts";
import ServiceOrderStatusBoard from "@/components/dashboard/service-order-status-board";
import CalendarWidgetSimple from "@/components/calendar/CalendarWidgetSimple";
import { useQuery } from "@tanstack/react-query";
import { StatCard, ServiceOrder, Client, InventoryItem } from "@/lib/types";
import {
  ClipboardList,
  DollarSign,
  Users,
  Tag
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

export default function Dashboard() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  const { data: serviceOrders = [] } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
  });

  const { data: clients = [] } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  const { data: inventoryAlerts = [] } = useQuery<InventoryItem[]>({
    queryKey: ['/api/inventory/low-stock'],
  });

  // Calculate some stats
  const activeOrdersCount = serviceOrders.filter(
    order => ['received', 'in_analysis', 'waiting_parts', 'waiting_approval', 'in_execution'].includes(order.status)
  ).length;

  // We don't have actual revenue data, but we can fake something based on the completed orders
  const mockRevenueData = formatCurrency(5240);

  // Just set critical alerts
  const criticalAlerts = inventoryAlerts.filter(
    item => item.quantity === 0 || item.quantity <= item.minQuantity / 2
  ).length;

  const statsCards: StatCard[] = [
    {
      title: "Ordens Ativas",
      value: activeOrdersCount,
      change: "+4.6%",
      changeText: "da semana passada",
      icon: <ClipboardList className="h-6 w-6 text-primary" />,
      iconBgColor: "bg-blue-50",
      changeColor: "success"
    },
    {
      title: "Receita",
      value: mockRevenueData,
      change: "+2.1%",
      changeText: "do mês passado",
      icon: <DollarSign className="h-6 w-6 text-secondary" />,
      iconBgColor: "bg-green-50",
      changeColor: "success"
    },
    {
      title: "Clientes",
      value: clients.length,
      change: "+12",
      changeText: "novos este mês",
      icon: <Users className="h-6 w-6 text-info" />,
      iconBgColor: "bg-cyan-50",
      changeColor: "success"
    },
    {
      title: "Alertas de Estoque",
      value: inventoryAlerts.length,
      change: criticalAlerts > 0 ? `${criticalAlerts} críticos` : "",
      changeText: criticalAlerts > 0 ? "itens precisam de atenção" : "todos os itens OK",
      icon: <Tag className="h-6 w-6 text-warning" />,
      iconBgColor: "bg-yellow-50",
      changeColor: criticalAlerts > 0 ? "danger" : "success"
    }
  ];

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Painel de Controle" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {statsCards.map((card, i) => (
              <StatsCard key={i} card={card} />
            ))}
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-12 gap-6">
            {/* Main content area */}
            <div className="xl:col-span-8 space-y-6">
              <ServiceOrderStatusBoard />
              <RecentServiceOrders />
            </div>

            {/* Sidebar content */}
            <div className="xl:col-span-4 space-y-6">
              <QuickActions />
              <CalendarWidgetSimple daysToShow={7} />
              <TechnicianStatus />
              <InventoryAlerts />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}