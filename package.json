{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@jridgewell/trace-mapping": "^0.3.29", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tanstack/react-query": "^5.81.5", "@testing-library/dom": "^10.4.0", "@types/react-grid-layout": "^1.3.5", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "embla-carousel-react": "^8.6.0", "export-to-csv": "^1.4.0", "express": "^5.1.0", "express-session": "^1.18.1", "framer-motion": "^12.23.0", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.525.0", "memorystore": "^1.6.7", "nanoid": "^5.1.5", "passport": "^0.7.0", "passport-local": "^1.0.0", "postgres": "^3.4.7", "react": "^19.1.0", "react-big-calendar": "^1.19.4", "react-day-picker": "^9.8.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.0.2", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "wouter": "^3.7.1", "ws": "^8.18.3", "zod": "^3.25.75", "zod-validation-error": "4.0.0-beta.1"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/connect-pg-simple": "^7.0.3", "@types/express": "5.0.3", "@types/express-session": "^1.18.2", "@types/node": "24.0.10", "@types/passport": "^1.0.17", "@types/passport-local": "^1.0.38", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/supertest": "^6.0.3", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "dotenv": "^17.0.1", "drizzle-kit": "^0.31.4", "esbuild": "^0.25.6", "jsdom": "^26.1.0", "postcss": "^8.5.6", "supertest": "^7.1.2", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "typescript": "5.8.3", "vite": "^7.0.2", "vitest": "^3.2.4"}, "optionalDependencies": {"bufferutil": "^4.0.9"}}