import { ServiceOrderStatus } from "./types";

export const SERVICE_ORDER_STATUSES: {
  value: ServiceOrderStatus;
  label: string;
}[] = [
  { value: 'received', label: 'Recebido' },
  { value: 'in_analysis', label: '<PERSON> Anális<PERSON>' },
  { value: 'waiting_parts', label: '<PERSON>guardand<PERSON>' },
  { value: 'waiting_approval', label: 'Aguardando Aprovação' },
  { value: 'in_execution', label: 'Em Execução' },
  { value: 'completed', label: 'Concluí<PERSON>' },
  { value: 'delivered', label: 'Entregue' }
];

export const TECHNICIAN_STATUSES = [
  { value: 'available', label: 'Disponível' },
  { value: 'on_service', label: 'Em Serviço' },
  { value: 'off_duty', label: 'Fora de Serviço' }
];

export const SCHEDULE_STATUSES = [
  { value: 'scheduled', label: 'Agendado' },
  { value: 'in_progress', label: 'Em Andamento' },
  { value: 'completed', label: 'Conc<PERSON>í<PERSON>' },
  { value: 'cancelled', label: '<PERSON>celado' }
];

export const USER_ROLES = [
  { value: 'admin', label: 'Administrador' },
  { value: 'technician', label: 'Técnico' },
  { value: 'receptionist', label: 'Recepcionista' }
];

export const NAVIGATION_ITEMS = [
  {
    title: 'Principal',
    items: [
      { name: 'Dashboard', href: '/', icon: 'LayoutDashboard' },
      { name: 'Ordens de Serviço', href: '/service-orders', icon: 'ClipboardList' },
      { name: 'Clientes', href: '/clients', icon: 'Users' },
      { name: 'Serviços', href: '/services', icon: 'Wrench' },
      { name: 'Equipamentos', href: '/equipment', icon: 'Laptop' },
      { name: 'Peças', href: '/parts', icon: 'Package' },
      { name: 'Recibos', href: '/invoices', icon: 'FileText' }
    ]
  },
  {
    title: 'Gerenciamento',
    items: [
      { name: 'Técnicos', href: '/technicians', icon: 'Wrench' },
      { name: 'Agendamentos', href: '/appointments', icon: 'Calendar' },
      { name: 'Estoque', href: '/inventory', icon: 'Package' },
      { name: 'Orçamentos', href: '/quotes', icon: 'FileText' },
      { name: 'Relatórios', href: '/reports', icon: 'BarChart3' }
    ]
  },
  {
    title: "Configurações",
    items: [
      {
        name: "Configurações",
        href: "/settings",
        icon: "Settings"
      },
      {
        name: "Assinatura",
        href: "/settings/billing",
        icon: "CreditCard"
      }
    ]
  }
];