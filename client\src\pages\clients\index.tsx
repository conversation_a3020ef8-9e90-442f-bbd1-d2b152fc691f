import { useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Client } from "@/lib/types";
import { Eye, Plus, Search, ArrowUpDown } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

export default function Clients() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const { data: clients = [], isLoading } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  // Filter clients based on search term and status
  const filteredClients = clients.filter(client => {
    const matchesSearch = 
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (client.email && client.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (client.phone && client.phone.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = 
      statusFilter === "all" || 
      (statusFilter === "active" && client.active) || 
      (statusFilter === "inactive" && !client.active);
    
    return matchesSearch && matchesStatus;
  });

  // Sort clients
  const sortedClients = [...filteredClients].sort((a, b) => {
    let result = 0;
    
    switch (sortBy) {
      case "name":
        result = a.name.localeCompare(b.name);
        break;
      case "email":
        result = (a.email || "").localeCompare(b.email || "");
        break;
      case "phone":
        result = (a.phone || "").localeCompare(b.phone || "");
        break;
      case "location":
        result = (a.city || "").localeCompare(b.city || "");
        break;
      case "created":
        result = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      default:
        result = a.name.localeCompare(b.name);
    }
    
    return sortOrder === "asc" ? result : -result;
  });

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Clientes" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-2 md:mb-0">Todos os Clientes</h3>
              <div className="flex flex-col md:flex-row gap-2">
                <Link href="/clients/new">
                  <Button className="bg-primary hover:bg-primary-dark">
                    <Plus className="h-4 w-4 mr-1" />
                    Novo Cliente
                  </Button>
                </Link>
              </div>
            </div>
            
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar clientes..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filtrar por status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Clientes</SelectItem>
                  <SelectItem value="active">Clientes Ativos</SelectItem>
                  <SelectItem value="inactive">Clientes Inativos</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Nome</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="phone">Telefone</SelectItem>
                  <SelectItem value="location">Localização</SelectItem>
                  <SelectItem value="created">Data de Cadastro</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="overflow-x-auto">
              {isLoading ? (
                <div className="p-8 flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : filteredClients.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  Nenhum cliente encontrado. Crie seu primeiro cliente!
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]"></TableHead>
                      <TableHead className="w-[300px]">
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("name");
                            toggleSortOrder();
                          }}
                        >
                          Nome
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("email");
                            toggleSortOrder();
                          }}
                        >
                          Email
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("phone");
                            toggleSortOrder();
                          }}
                        >
                          Telefone
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("location");
                            toggleSortOrder();
                          }}
                        >
                          Localização
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedClients.map((client) => (
                      <TableRow key={client.id} className="hover:bg-gray-50">
                        <TableCell className="text-center">
                          <Link href={`/clients/${client.id}`}>
                            <Button variant="ghost" size="sm" className="text-primary hover:text-primary-dark">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                        </TableCell>
                        <TableCell className="font-medium">{client.name}</TableCell>
                        <TableCell>{client.email || "-"}</TableCell>
                        <TableCell>{client.phone || "-"}</TableCell>
                        <TableCell>
                          {client.city && client.state
                            ? `${client.city}, ${client.state}`
                            : client.city || client.state || "-"}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={client.active ? "default" : "secondary"}
                            className={
                              client.active
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-800"
                            }
                          >
                            {client.active ? "Ativo" : "Inativo"}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
            
            <div className="px-6 py-3 border-t border-gray-200 bg-gray-50 text-sm text-gray-500">
              Exibindo {filteredClients.length} de {clients.length} clientes no total
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
