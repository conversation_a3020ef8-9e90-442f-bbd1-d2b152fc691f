import { Switch, Route, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { useEffect, useState } from "react";

// Pages
import Login from "@/pages/login";
import EsqueceuSenha from "@/pages/esqueceu-senha";
import Dashboard from "@/pages/dashboard";
import ServiceOrders from "@/pages/service-orders";
import NewServiceOrder from "@/pages/service-orders/new";
import ServiceOrderDetails from "@/pages/service-orders/[id]";
import EditServiceOrder from "@/pages/service-orders/edit/[id]";
import PaymentServiceOrder from "@/pages/service-orders/payment/[id]";
import Clients from "@/pages/clients";
import NewClient from "@/pages/clients/new";
import ClientDetails from "@/pages/clients/[id]";
import Equipment from "@/pages/equipment";
import NewEquipment from "@/pages/equipment/new";
import EquipmentDetails from "@/pages/equipment/[id]";
import EditEquipment from "@/pages/equipment/edit/[id]";
import Services from "@/pages/services";
import NewService from "@/pages/services/new";
import EditService from "@/pages/services/edit";
import Inventory from "@/pages/inventory";
import NewInventoryItem from "@/pages/inventory/new";
import Technicians from "@/pages/technicians";
import NewTechnician from "@/pages/technicians/new";
import TechnicianDetails from "@/pages/technicians/[id]";
import EditTechnician from "@/pages/technicians/edit/[id]";
import TechnicianSchedule from "@/pages/technicians/schedule";
import NewTechnicianSchedule from "@/pages/technicians/schedule/new";
import Quotes from "@/pages/quotes";
import NewQuote from "@/pages/quotes/new";
import QuoteDetails from "@/pages/quotes/[id]";
import Reports from "@/pages/reports";
import Settings from "@/pages/settings";
import UsersSettings from "@/pages/settings/users";
import PermissionsSettings from "@/pages/settings/permissions";
import WhatsAppSettings from "@/pages/settings/whatsapp";
import BillingPortal from "@/pages/settings/billing";
import PaymentMethodsSettings from "@/pages/settings/payment-methods";
import StripeSettings from "@/pages/settings/stripe";
import Invoices from "@/pages/invoices";
import NewInvoice from "@/pages/invoices/new";
import InvoiceDetails from "@/pages/invoices/[id]";
import Parts from "@/pages/parts";
import NewPart from "@/pages/parts/new";
import PartDetails from "@/pages/parts/[id]";
import Appointments from "@/pages/appointments";
import NewAppointment from "@/pages/appointments/new";
import EditAppointment from "@/pages/appointments/edit/[id]";
import NotFound from "@/pages/not-found";

function App() {
  const [location, setLocation] = useLocation();
  const { data: session, isLoading } = useQuery<{user: any|null, isAuthenticated: boolean}>({
    queryKey: ["session"],
    queryFn: async () => {
      const response = await fetch("/api/auth/session");
      if (!response.ok) {
        throw new Error("Failed to fetch session");
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 2, // Consider fresh for 2 minutes
    refetchInterval: 1000 * 60 * 5, // Check every 5 minutes
    retry: false,
  });

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !session?.isAuthenticated && 
        location !== '/login' && 
        location !== '/esqueceu-senha') {
      setLocation('/login');
    }

    // Redirect to dashboard if authenticated and on login page
    if (!isLoading && session?.isAuthenticated && 
        (location === '/login' || location === '/esqueceu-senha')) {
      setLocation('/');
    }
  }, [session, isLoading, location, setLocation]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      <Switch>
        <Route path="/login" component={Login} />
        <Route path="/esqueceu-senha" component={EsqueceuSenha} />

        {/* Protected routes */}
        {session?.isAuthenticated && (
          <Switch>
            <Route path="/" component={Dashboard} />
            <Route path="/service-orders" component={ServiceOrders} />
            <Route path="/service-orders/new" component={NewServiceOrder} />
            <Route path="/service-orders/:id/edit" component={EditServiceOrder} />
            <Route path="/service-orders/:id/payment" component={PaymentServiceOrder} />
            <Route path="/service-orders/:id" component={ServiceOrderDetails} />
            <Route path="/clients" component={Clients} />
            <Route path="/clients/new" component={NewClient} />
            <Route path="/clients/:id" component={ClientDetails} />
            <Route path="/equipment" component={Equipment} />
            <Route path="/equipment/new" component={NewEquipment} />
            <Route path="/equipment/:id/edit" component={EditEquipment} />
            <Route path="/equipment/:id" component={EquipmentDetails} />
            <Route path="/services" component={Services} />
            <Route path="/services/new" component={NewService} />
            <Route path="/services/edit/:id" component={EditService} />
            <Route path="/inventory" component={Inventory} />
            <Route path="/inventory/new" component={NewInventoryItem} />
            <Route path="/technicians" component={Technicians} />
            <Route path="/technicians/new" component={NewTechnician} />
            <Route path="/technicians/:id/edit" component={EditTechnician} />
            <Route path="/technicians/:id" component={TechnicianDetails} />
            <Route path="/technicians/schedule" component={TechnicianSchedule} />
            <Route path="/technicians/schedule/new" component={NewTechnicianSchedule} />
            <Route path="/quotes" component={Quotes} />
            <Route path="/quotes/new" component={NewQuote} />
            <Route path="/quotes/:id" component={QuoteDetails} />
            <Route path="/invoices" component={Invoices} />
            <Route path="/invoices/new" component={NewInvoice} />
            <Route path="/invoices/:id" component={InvoiceDetails} />
            <Route path="/parts" component={Parts} />
            <Route path="/parts/new" component={NewPart} />
            <Route path="/parts/:id" component={PartDetails} />
            <Route path="/appointments" component={Appointments} />
            <Route path="/appointments/new" component={NewAppointment} />
            <Route path="/appointments/edit/:id" component={EditAppointment} />
            <Route path="/reports" component={Reports} />
            <Route path="/settings" component={Settings} />
            <Route path="/settings/users" component={UsersSettings} />
            <Route path="/settings/permissions" component={PermissionsSettings} />
            <Route path="/settings/whatsapp" component={WhatsAppSettings} />
            <Route path="/settings/payment-methods" component={PaymentMethodsSettings} />
            <Route path="/settings/stripe" component={StripeSettings} />
            <Route path="/settings/billing" component={BillingPortal} />
          </Switch>
        )}

        <Route component={NotFound} />
      </Switch>
      <Toaster />
    </>
  );
}

export default App;