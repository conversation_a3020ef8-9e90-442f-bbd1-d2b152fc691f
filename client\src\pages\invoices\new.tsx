import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, Plus, Trash } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { insertInvoiceSchema, insertInvoiceItemSchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";

import type { Client, ServiceOrder, InvoiceItem } from "@shared/schema";

// Esquema de validação do formulário
const formSchema = insertInvoiceSchema.extend({
  dueDate: z.date().optional(),
  serviceOrderId: z.string().optional().transform(val => val ? parseInt(val) : null),
  clientId: z.string().transform(val => parseInt(val)),
});

const itemFormSchema = z.object({
  description: z.string().min(1, "A descrição é obrigatória"),
  quantity: z.string().min(1, "Quantidade obrigatória"),
  unitPrice: z.string().min(1, "Valor obrigatório"),
});

export default function NewInvoicePage() {
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [selectedServiceOrder, setSelectedServiceOrder] = useState<ServiceOrder | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Formulário principal
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      dueDate: new Date(new Date().setDate(new Date().getDate() + 30)), // 30 dias a partir de hoje
      notes: "",
      status: "draft",
    },
  });

  // Formulário de item
  const itemForm = useForm<z.infer<typeof itemFormSchema>>({
    resolver: zodResolver(itemFormSchema),
    defaultValues: {
      description: "",
      quantity: "1",
      unitPrice: "",
    },
  });

  // Consulta de clientes
  const { data: clients } = useQuery<Client[]>({
    queryKey: ["/api/clients"],
  });

  // Consulta de ordens de serviço
  const { data: serviceOrders } = useQuery<ServiceOrder[]>({
    queryKey: ["/api/service-orders"],
  });

  // Mutação para criar a fatura
  const createInvoiceMutation = useMutation({
    mutationFn: async (data: any) => {
      const invoiceData = {
        ...data,
        items: items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
      };

      const response = await apiRequest("POST", "/api/invoices", invoiceData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['/api/invoices']
      });
      toast({
        title: "Recibo criado com sucesso",
        description: "O recibo foi criado e está pronto para ser enviado.",
      });
      navigate("/invoices");
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar recibo",
        description: "Ocorreu um erro ao criar o recibo. Tente novamente.",
        variant: "destructive",
      });
      console.error(error);
    },
  });

  // Quando a ordem de serviço for selecionada, pré-preenche o cliente
  useEffect(() => {
    if (selectedServiceOrder && selectedServiceOrder.clientId) {
      form.setValue("clientId", selectedServiceOrder.clientId.toString());
    }
  }, [selectedServiceOrder, form]);

  // Adiciona um item à fatura
  const addItem = async () => {
    const isValid = await itemForm.trigger();

    if (!isValid) {
      return;
    }

    const values = itemForm.getValues();

    if (!values.description || !values.quantity || !values.unitPrice) {
      toast({
        title: "Erro ao adicionar item",
        description: "Preencha todos os campos do item",
        variant: "destructive",
      });
      return;
    }

    const unitPrice = Math.round(parseFloat(values.unitPrice) * 100); // Converte para centavos

    setItems((current) => [
      ...current,
      {
        id: 0, // Será definido pelo backend
        invoiceId: 0, // Será definido pelo backend
        description: values.description,
        quantity: parseInt(values.quantity),
        unitPrice: unitPrice,
      },
    ]);

    // Limpa o formulário
    itemForm.reset({
      description: "",
      quantity: "1",
      unitPrice: "",
    });
  };

  // Remove um item da fatura
  const removeItem = (index: number) => {
    setItems((current) => current.filter((_, i) => i !== index));
  };

  // Calcula o total da fatura
  const calculateTotal = () => {
    return items.reduce((sum, item) => {
      return sum + (item.quantity * item.unitPrice);
    }, 0);
  };

  // Envia o formulário
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      // Valida se há itens
      if (items.length === 0) {
        toast({
          title: "Erro ao criar recibo",
          description: "Adicione pelo menos um item ao recibo.",
          variant: "destructive",
        });
        return;
      }

      // Valida se há cliente selecionado
      if (!values.clientId) {
        toast({
          title: "Erro ao criar recibo",
          description: "Selecione um cliente.",
          variant: "destructive",
        });
        return;
      }

      // Calcule o total e prepare os dados conforme o esquema esperado
      const total = calculateTotal();

      const invoiceData = {
        clientId: Number(values.clientId),
        serviceOrderId: values.serviceOrderId ? Number(values.serviceOrderId) : null,
        status: "draft" as const,
        subtotal: total,
        discount: 0,
        tax: 0,
        totalAmount: total,
        paidAmount: 0,
        dueDate: values.dueDate || null,
        notes: values.notes || "",
        items: items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: 0,
          tax: 0,
        })),
      };

      await createInvoiceMutation.mutateAsync(invoiceData);
    } catch (error) {
      console.error("Erro ao criar recibo:", error);
      toast({
        title: "Erro ao criar recibo",
        description: "Ocorreu um erro ao criar o recibo. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />

      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header
          title="Novo Recibo"
          onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="container mx-auto py-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="order-2 md:order-1">
                <CardHeader>
                  <CardTitle>Detalhes do Recibo</CardTitle>
                </CardHeader>
                <CardContent>
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <FormField
                        control={form.control}
                        name="clientId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cliente</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um cliente" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {clients?.map((client) => (
                                  <SelectItem key={client.id} value={`${client.id}`}>
                                    {client.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="serviceOrderId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Ordem de Serviço (opcional)</FormLabel>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                                const serviceOrder = serviceOrders?.find(
                                  (order) => order.id.toString() === value
                                );
                                setSelectedServiceOrder(serviceOrder || null);
                              }}
                              defaultValue={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Vincular a uma ordem de serviço" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="0">Nenhuma</SelectItem>
                                {serviceOrders?.map((order) => (
                                  <SelectItem key={order.id} value={order.id.toString()}>
                                    {order.orderNumber} - {order.description.substring(0, 30)}
                                    {order.description.length > 30 ? "..." : ""}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dueDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Data de Vencimento</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant={"outline"}
                                    className={cn(
                                      "w-full pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "dd/MM/yyyy", { locale: ptBR })
                                    ) : (
                                      <span>Selecione uma data</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) =>
                                    date < new Date(new Date().setHours(0, 0, 0, 0))
                                  }
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="notes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Observações</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Observações adicionais para o recibo"
                                className="resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="pt-4">
                        <Button
                          type="button"
                          className="w-full"
                          disabled={createInvoiceMutation.isPending}
                          onClick={() => {
                            const values = form.getValues();
                            onSubmit(values as z.infer<typeof formSchema>);
                          }}
                        >
                          {createInvoiceMutation.isPending
                            ? "Criando recibo..."
                            : "Criar Recibo"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>

              <div className="order-1 md:order-2">
                <Card className="mb-6">
                  <CardHeader>
                    <CardTitle>Itens do Recibo</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Form {...itemForm}>
                      <div className="space-y-4">
                        <FormField
                          control={itemForm.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Descrição</FormLabel>
                              <FormControl>
                                <Input placeholder="Ex: Serviço de reparo" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={itemForm.control}
                            name="quantity"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Quantidade</FormLabel>
                                <FormControl>
                                  <Input type="number" min="1" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={itemForm.control}
                            name="unitPrice"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Valor Unitário (R$)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    placeholder="0,00"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <Button
                          type="button"
                          onClick={addItem}
                          className="w-full"
                          variant="outline"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Adicionar Item
                        </Button>
                      </div>
                    </Form>

                    {items.length > 0 && (
                      <div className="mt-6">
                        <h3 className="font-medium mb-2">Itens Adicionados</h3>
                        <div className="space-y-3">
                          {items.map((item, index) => (
                            <div
                              key={index}
                              className="flex justify-between items-center p-3 border rounded-md"
                            >
                              <div>
                                <p className="font-medium">{item.description}</p>
                                <p className="text-sm text-muted-foreground">
                                  {item.quantity} x{" "}
                                  {new Intl.NumberFormat("pt-BR", {
                                    style: "currency",
                                    currency: "BRL",
                                  }).format(item.unitPrice / 100)}
                                </p>
                              </div>
                              <div className="flex items-center gap-4">
                                <p className="font-medium">
                                  {new Intl.NumberFormat("pt-BR", {
                                    style: "currency",
                                    currency: "BRL",
                                  }).format((item.quantity * item.unitPrice) / 100)}
                                </p>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => removeItem(index)}
                                >
                                  <Trash className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>

                        <div className="flex justify-between items-center mt-4 pt-4 border-t">
                          <p className="font-bold">Total</p>
                          <p className="font-bold">
                            {new Intl.NumberFormat("pt-BR", {
                              style: "currency",
                              currency: "BRL",
                            }).format(calculateTotal() / 100)}
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}