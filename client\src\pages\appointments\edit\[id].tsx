import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useLocation, useParams } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, ArrowLeft, Loader2 } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { insertAppointmentSchema, type InsertAppointment } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";

export default function EditAppointmentPage() {
  const { id } = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: appointment, isLoading: appointmentLoading } = useQuery({
    queryKey: ["/api/appointments", id],
    queryFn: async () => {
      const response = await apiRequest("GET", `/api/appointments/${id}`);
      return response;
    },
    enabled: !!id
  });

  const { data: clients = [] } = useQuery({
    queryKey: ["/api/clients"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/clients");
      return response;
    }
  });

  const { data: technicians = [] } = useQuery({
    queryKey: ["/api/technicians"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/technicians");
      return response;
    }
  });

  const { data: users = [] } = useQuery({
    queryKey: ["/api/users"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/users");
      return response;
    }
  });

  const { data: serviceOrders = [] } = useQuery({
    queryKey: ["/api/service-orders"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/service-orders");
      return response;
    }
  });

  const form = useForm<InsertAppointment>({
    resolver: zodResolver(insertAppointmentSchema),
    defaultValues: {
      title: "",
      description: "",
      type: "technical_visit",
      status: "scheduled",
      appointmentDate: new Date(),
      startTime: "",
      endTime: "",
      location: "",
      clientId: null,
      technicianId: null,
      serviceOrderId: null,
      contactPerson: "",
      contactPhone: "",
      contactEmail: "",
      notes: "",
      isRecurring: false,
      recurringPattern: null
    }
  });

  useEffect(() => {
    if (appointment) {
      const appointmentDate = new Date(appointment.appointmentDate);
      form.reset({
        title: appointment.title || "",
        description: appointment.description || "",
        type: appointment.type,
        status: appointment.status,
        appointmentDate: appointmentDate,
        startTime: appointment.startTime || "",
        endTime: appointment.endTime || "",
        location: appointment.location || "",
        clientId: appointment.clientId,
        technicianId: appointment.technicianId,
        serviceOrderId: appointment.serviceOrderId,
        contactPerson: appointment.contactPerson || "",
        contactPhone: appointment.contactPhone || "",
        contactEmail: appointment.contactEmail || "",
        notes: appointment.notes || "",
        isRecurring: appointment.isRecurring || false,
        recurringPattern: appointment.recurringPattern
      });
    }
  }, [appointment, form]);

  const updateAppointmentMutation = useMutation({
    mutationFn: async (data: InsertAppointment) => {
      const response = await apiRequest("PUT", `/api/appointments/${id}`, data);
      return response;
    },
    onSuccess: () => {
      toast({
        title: "Sucesso",
        description: "Agendamento atualizado com sucesso!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/appointments"] });
      queryClient.invalidateQueries({ queryKey: ["/api/appointments", id] });
      setLocation("/appointments");
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: error.message || "Erro ao atualizar agendamento",
        variant: "destructive",
      });
    }
  });

  const onSubmit = (data: InsertAppointment) => {
    const appointmentData = {
      ...data,
      appointmentDate: typeof data.appointmentDate === "string"
        ? new Date(data.appointmentDate)
        : data.appointmentDate,
      clientId: data.clientId || null,
      technicianId: data.technicianId || null,
      serviceOrderId: data.serviceOrderId || null,
      endTime: data.endTime || null,
      location: data.location || null,
      contactPerson: data.contactPerson || null,
      contactPhone: data.contactPhone || null,
      contactEmail: data.contactEmail || null,
      notes: data.notes || null,
      recurringPattern: data.recurringPattern || null
    };

    updateAppointmentMutation.mutate(appointmentData);
  };

  const getTechnicianName = (technicianId: number) => {
    const technician = technicians.find((t: any) => t.id === technicianId);
    if (!technician) return "";
    const user = users.find((u: any) => u.id === technician.userId);
    return user ? user.name : "";
  };

  if (appointmentLoading) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Editar Agendamento" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="container mx-auto px-4 py-8 max-w-4xl">
              <div className="flex items-center justify-center min-h-[400px]">
                <Loader2 className="w-8 h-8 animate-spin" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!appointment) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Editar Agendamento" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="container mx-auto px-4 py-8 max-w-4xl">
              <div className="text-center">
                <h1 className="text-2xl font-bold text-gray-900 mb-4">Agendamento não encontrado</h1>
                <Link href="/appointments">
                  <Button variant="outline">Voltar para Agendamentos</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Editar Agendamento" />
        <main className="flex-1 overflow-y-auto bg-slate-50">
          <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl">
            {/* Page Header */}
            <div className="flex items-center gap-4 mb-6">
              <Link href="/appointments">
                <Button variant="outline" size="icon">
                  <ArrowLeft className="w-4 h-4" />
                </Button>
              </Link>
              <div className="min-w-0 flex-1">
                <p className="text-sm text-gray-600">Atualize as informações do agendamento</p>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Informações do Agendamento
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Título *</FormLabel>
                            <FormControl>
                              <Input placeholder="Ex: Visita técnica - Manutenção" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tipo *</FormLabel>
                            <Select onValueChange={field.onChange} value={String(field.value ?? "")}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione o tipo" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="technical_visit">Visita Técnica</SelectItem>
                                <SelectItem value="equipment_delivery">Entrega de Equipamento</SelectItem>
                                <SelectItem value="supplier_meeting">Reunião com Fornecedor</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select onValueChange={field.onChange} value={String(field.value ?? "")}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione o status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="scheduled">Agendado</SelectItem>
                                <SelectItem value="confirmed">Confirmado</SelectItem>
                                <SelectItem value="in_progress">Em Andamento</SelectItem>
                                <SelectItem value="completed">Concluído</SelectItem>
                                <SelectItem value="cancelled">Cancelado</SelectItem>
                                <SelectItem value="rescheduled">Reagendado</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Descrição</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Descreva os detalhes do agendamento..."
                              {...field}
                              value={String(field.value ?? "")}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name="appointmentDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Data *</FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                {...field}
                                value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value}
                                onChange={(e) => field.onChange(new Date(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="startTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Hora de Início *</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="endTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Hora de Término</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} value={String(field.value ?? "")} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="location"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Local</FormLabel>
                          <FormControl>
                            <Input placeholder="Endereço ou local do agendamento" {...field} value={String(field.value ?? "")} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name="clientId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cliente</FormLabel>
                            <Select onValueChange={(value) => field.onChange(value ? parseInt(value) : null)} value={String(field.value ?? "")}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um cliente" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {clients.map((client: any) => (
                                  <SelectItem key={client.id} value={client.id.toString()}>
                                    {client.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="technicianId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Técnico</FormLabel>
                            <Select onValueChange={(value) => field.onChange(value ? parseInt(value) : null)} value={String(field.value ?? "")}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um técnico" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {technicians.map((technician: any) => (
                                  <SelectItem key={technician.id} value={technician.id.toString()}>
                                    {getTechnicianName(technician.id)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="serviceOrderId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Ordem de Serviço</FormLabel>
                            <Select onValueChange={(value) => field.onChange(value ? parseInt(value) : null)} value={String(field.value ?? "")}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione uma OS" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {serviceOrders.map((order: any) => (
                                  <SelectItem key={order.id} value={order.id.toString()}>
                                    {order.orderNumber} - {order.description.substring(0, 50)}...
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">Informações de Contato</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          control={form.control}
                          name="contactPerson"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Pessoa de Contato</FormLabel>
                              <FormControl>
                                <Input placeholder="Nome do contato" {...field} value={String(field.value ?? "")} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="contactPhone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Telefone</FormLabel>
                              <FormControl>
                                <Input placeholder="(11) 99999-9999" {...field} value={String(field.value ?? "")} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="contactEmail"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>E-mail</FormLabel>
                              <FormControl>
                                <Input type="email" placeholder="<EMAIL>" {...field} value={String(field.value ?? "")} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Observações</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Observações adicionais sobre o agendamento..."
                              {...field}
                              value={String(field.value ?? "")}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Actions */}
                    <div className="flex flex-col sm:flex-row justify-end gap-3 pt-8 border-t border-gray-200">
                      <Link href="/appointments">
                        <Button type="button" variant="outline" className="w-full sm:w-auto">
                          Cancelar
                        </Button>
                      </Link>
                      <Button
                        type="submit"
                        disabled={updateAppointmentMutation.isPending}
                        className="w-full sm:w-auto"
                      >
                        {updateAppointmentMutation.isPending ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Atualizando...
                          </>
                        ) : (
                          "Atualizar Agendamento"
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
