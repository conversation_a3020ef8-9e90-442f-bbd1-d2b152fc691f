import axios from 'axios';

// Tipos de mensagens do WhatsApp
export enum WhatsAppMessageType {
  TEXT = 'text',
  TEMPLATE = 'template',
  INTERACTIVE = 'interactive',
}

// Tipos de notificações que podemos enviar
export enum NotificationType {
  SERVICE_ORDER_CREATED = 'service_order_created',
  SERVICE_ORDER_STATUS_CHANGED = 'service_order_status_changed',
  SERVICE_ORDER_COMPLETED = 'service_order_completed',
  APPOINTMENT_SCHEDULED = 'appointment_scheduled',
  APPOINTMENT_REMINDER = 'appointment_reminder',
  QUOTE_READY = 'quote_ready',
}

// Interface para mensagens de texto simples
interface TextMessage {
  type: WhatsAppMessageType.TEXT;
  text: {
    body: string;
  };
}

// Interface para mensagens usando templates
interface TemplateMessage {
  type: WhatsAppMessageType.TEMPLATE;
  template: {
    name: string;
    language: {
      code: string;
    };
    components: Array<{
      type: string;
      parameters: Array<{
        type: string;
        text?: string;
        currency?: {
          code: string;
          amount_1000: number;
        };
        date_time?: {
          fallback_value: string;
        };
      }>;
    }>;
  };
}

// Interface para mensagens interativas (com botões)
interface InteractiveMessage {
  type: WhatsAppMessageType.INTERACTIVE;
  interactive: {
    type: 'button' | 'list';
    body: {
      text: string;
    };
    action: {
      buttons?: Array<{
        type: 'reply';
        reply: {
          id: string;
          title: string;
        };
      }>;
    };
  };
}

// União de todos os tipos de mensagens
type WhatsAppMessage = TextMessage | TemplateMessage | InteractiveMessage;

/**
 * Classe para envio de mensagens via WhatsApp Business API
 */
export class WhatsAppService {
  private apiToken: string;
  private phoneNumberId: string;
  private apiUrl: string;
  private isConfigured: boolean;

  constructor() {
    // Inicializa com os valores das variáveis de ambiente
    this.apiToken = process.env.WHATSAPP_API_TOKEN || '';
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID || '';
    this.apiUrl = `https://graph.facebook.com/v17.0/${this.phoneNumberId}/messages`;
    this.isConfigured = !!(this.apiToken && this.phoneNumberId);
  }

  /**
   * Verifica se o serviço está configurado corretamente
   */
  public checkConfiguration(): boolean {
    return this.isConfigured;
  }

  /**
   * Atualiza a configuração do serviço
   */
  public updateConfiguration(apiToken: string, phoneNumberId: string): void {
    this.apiToken = apiToken;
    this.phoneNumberId = phoneNumberId;
    this.apiUrl = `https://graph.facebook.com/v17.0/${this.phoneNumberId}/messages`;
    this.isConfigured = !!(this.apiToken && this.phoneNumberId);
  }

  /**
   * Envia uma mensagem via WhatsApp
   */
  public async sendMessage(to: string, message: WhatsAppMessage): Promise<any> {
    if (!this.isConfigured) {
      throw new Error('WhatsApp service is not configured. Please set API token and phone number ID.');
    }

    try {
      const response = await axios.post(
        this.apiUrl,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to,
          ...message,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiToken}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      throw error;
    }
  }

  /**
   * Envia uma mensagem de texto simples
   */
  public async sendTextMessage(to: string, body: string): Promise<any> {
    const message: TextMessage = {
      type: WhatsAppMessageType.TEXT,
      text: { body },
    };

    return this.sendMessage(to, message);
  }

  /**
   * Envia uma notificação de status de ordem de serviço
   */
  public async sendServiceOrderStatusNotification(
    to: string,
    orderNumber: string,
    status: string,
    clientName: string
  ): Promise<any> {
    // Usando um template de exemplo - você precisará criar este template no WhatsApp Business
    const message: TemplateMessage = {
      type: WhatsAppMessageType.TEMPLATE,
      template: {
        name: 'service_order_status',
        language: { code: 'pt_BR' },
        components: [
          {
            type: 'body',
            parameters: [
              { type: 'text', text: clientName },
              { type: 'text', text: orderNumber },
              { type: 'text', text: status },
            ],
          },
        ],
      },
    };

    return this.sendMessage(to, message);
  }

  /**
   * Envia uma notificação de agendamento
   */
  public async sendAppointmentNotification(
    to: string,
    clientName: string,
    date: string,
    time: string,
    service: string
  ): Promise<any> {
    // Usando um template de exemplo - você precisará criar este template no WhatsApp Business
    const message: TemplateMessage = {
      type: WhatsAppMessageType.TEMPLATE,
      template: {
        name: 'appointment_confirmation',
        language: { code: 'pt_BR' },
        components: [
          {
            type: 'body',
            parameters: [
              { type: 'text', text: clientName },
              { type: 'text', text: service },
              { type: 'text', text: date },
              { type: 'text', text: time },
            ],
          },
        ],
      },
    };

    return this.sendMessage(to, message);
  }

  /**
   * Envia uma notificação de orçamento pronto
   */
  public async sendQuoteReadyNotification(
    to: string,
    clientName: string,
    quoteNumber: string,
    totalAmount: number,
    validUntil: string
  ): Promise<any> {
    // Usando um template de exemplo - você precisará criar este template no WhatsApp Business
    const message: TemplateMessage = {
      type: WhatsAppMessageType.TEMPLATE,
      template: {
        name: 'quote_ready',
        language: { code: 'pt_BR' },
        components: [
          {
            type: 'body',
            parameters: [
              { type: 'text', text: clientName },
              { type: 'text', text: quoteNumber },
              { 
                type: 'currency', 
                currency: { 
                  code: 'BRL', 
                  amount_1000: totalAmount * 1000 
                } 
              },
              { type: 'text', text: validUntil },
            ],
          },
        ],
      },
    };

    return this.sendMessage(to, message);
  }

  /**
   * Cria uma mensagem interativa com botões de confirmação
   */
  public async sendConfirmationRequest(
    to: string,
    body: string,
    confirmButtonText = 'Confirmar',
    cancelButtonText = 'Cancelar'
  ): Promise<any> {
    const message: InteractiveMessage = {
      type: WhatsAppMessageType.INTERACTIVE,
      interactive: {
        type: 'button',
        body: { text: body },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'confirm',
                title: confirmButtonText,
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'cancel',
                title: cancelButtonText,
              },
            },
          ],
        },
      },
    };

    return this.sendMessage(to, message);
  }
}

// Singleton instance
export const whatsappService = new WhatsAppService();