export interface User {
  id: number;
  name: string;
  email: string;
  username: string;
  role: 'admin' | 'technician' | 'receptionist';
  phone?: string;
  active: boolean;
  createdAt: string;
}

export interface Client {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  document?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  notes?: string;
  active: boolean;
  createdAt: string;
}

export interface EquipmentCategory {
  id: number;
  name: string;
  description?: string;
}

export interface Equipment {
  id: number;
  clientId: number;
  categoryId?: number;
  brand?: string;
  model?: string;
  serialNumber?: string;
  description?: string;
  status: string;
  purchaseDate?: string;
  notes?: string;
  createdAt: string;
}

export interface Technician {
  id: number;
  userId: number;
  specialties?: string;
  status: 'available' | 'on_service' | 'off_duty';
  notes?: string;
}

export type TechnicianWithUser = Technician & {
  user?: User;
};

export interface InventoryCategory {
  id: number;
  name: string;
  description?: string;
}

export interface InventoryItem {
  id: number;
  categoryId?: number;
  name: string;
  description?: string;
  sku?: string;
  quantity: number;
  minQuantity: number;
  price?: number;
  supplier?: string;
  notes?: string;
  createdAt: string;
}

export type ServiceOrderStatus = 
  | 'received'
  | 'in_analysis'
  | 'waiting_parts'
  | 'waiting_approval'
  | 'in_execution'
  | 'completed'
  | 'delivered';

export interface ServiceOrder {
  id: number;
  orderNumber: string;
  clientId: number;
  equipmentId?: number;
  technicianId?: number;
  status: ServiceOrderStatus;
  description: string;
  diagnostics?: string;
  solution?: string;
  internalNotes?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  estimatedCompletionDate?: string;
}

export interface ServiceOrderWithRelations extends ServiceOrder {
  client?: Client;
  equipment?: Equipment;
  technician?: TechnicianWithUser;
}

export interface ServiceOrderItem {
  id: number;
  serviceOrderId: number;
  inventoryItemId?: number;
  description: string;
  quantity: number;
  unitPrice: number;
  type?: 'service' | 'part';
  price?: number;
}

export interface Quote {
  id: number;
  quoteNumber: string;
  serviceOrderId?: number;
  clientId: number;
  status: 'pending' | 'approved' | 'rejected';
  total?: number;
  description?: string;
  notes?: string;
  validUntil?: string;
  createdAt: string;
  approvedAt?: string;
}

export interface StatCard {
  title: string;
  value: string | number;
  icon: any;
  iconBgColor: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  description?: string;
  change?: string;
  changeText?: string;
  changeColor?: 'success' | 'warning' | 'danger';
}

export interface TechnicianStatus {
  id: number;
  name: string;
  status: 'available' | 'on_service' | 'off_duty';
}

export interface InventoryAlert {
  id: number;
  name: string;
  quantity: number;
  minQuantity: number;
  severity: 'critical' | 'low';
}

export interface StatusBoardOrder {
  id: number;
  orderNumber: string;
  equipmentName: string;
  clientName: string;
  technicianName?: string;
  date: string;
}

export type ScheduleStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled';

export interface TechnicianSchedule {
  id: number;
  technicianId: number;
  serviceOrderId?: number;
  title: string;
  description?: string;
  scheduleDate: string;
  startTime: string;
  endTime: string;
  status: ScheduleStatus;
  location?: string;
  clientId?: number;
  isAllDay: boolean;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface TechnicianScheduleWithRelations extends TechnicianSchedule {
  technician?: TechnicianWithUser;
  client?: Client;
  serviceOrder?: ServiceOrderWithRelations;
}

export interface Service {
  id: number;
  name: string;
  description?: string;
  price: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Part {
  id: number;
  name: string;
  brand: string;
  description?: string;
  barcode: string;
  internalCode?: string;
  sku?: string;
  categoryId?: number;
  purchaseValue: number; // em centavos
  saleValue: number; // em centavos
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

// Tipos para o dashboard customizável
export type WidgetType = 
  | 'stats'
  | 'recent-orders'
  | 'quick-actions'
  | 'technician-status'
  | 'inventory-alerts'
  | 'order-status-board';

export interface WidgetConfig {
  id: string;
  type: WidgetType;
  title: string;
  width: number;  // Em unidades de grid
  height: number; // Em unidades de grid
  x: number;      // Posição X na grid
  y: number;      // Posição Y na grid
  minW?: number;  // Largura mínima
  minH?: number;  // Altura mínima
  maxW?: number;  // Largura máxima
  maxH?: number;  // Altura máxima
  visible: boolean;
}

export interface DashboardConfig {
  widgets: WidgetConfig[];
  cols: number;   // Número de colunas no grid
  rowHeight: number; // Altura de cada linha em pixels
  preventCollision: boolean; // Evitar colisão entre widgets
}