import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Link } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { ServiceOrder, Client, Equipment, Technician, User } from "@/lib/types";
import { SERVICE_ORDER_STATUSES } from "@/lib/constants";
import { formatDateTime, getStatusColor, getStatusLabel } from "@/lib/utils";
import { useState } from "react";
import { Plus, Search, Eye, ArrowUpDown, Calendar } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Button as CalendarButton } from "@/components/ui/button";

export default function ServiceOrders() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("newest");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);

  const { data: serviceOrders = [], isLoading: isLoadingOrders } = useQuery<
    ServiceOrder[]
  >({
    queryKey: ["/api/service-orders"],
  });

  const { data: clients = [], isLoading: isLoadingClients } = useQuery<
    Client[]
  >({
    queryKey: ["/api/clients"],
  });

  const { data: equipment = [], isLoading: isLoadingEquipment } = useQuery<
    Equipment[]
  >({
    queryKey: ["/api/equipment"],
  });

  const { data: technicians = [], isLoading: isLoadingTechnicians } = useQuery<
    Technician[]
  >({
    queryKey: ["/api/technicians"],
  });

  const { data: users = [], isLoading: isLoadingUsers } = useQuery<User[]>({
    queryKey: ["/api/users"],
  });

  const isLoading =
    isLoadingOrders ||
    isLoadingClients ||
    isLoadingEquipment ||
    isLoadingTechnicians ||
    isLoadingUsers;

  const getClientName = (clientId: number) => {
    const client = clients.find((c) => c.id === clientId);
    return client?.name || "Cliente Desconhecido";
  };

  const getEquipmentName = (equipmentId: number | undefined) => {
    if (!equipmentId) return "N/A";
    const item = equipment.find((e) => e.id === equipmentId);
    return item
      ? `${item.brand || ""} ${item.model || ""}`.trim() ||
          item.description ||
          "N/A"
      : "N/A";
  };

  const getTechnicianName = (technicianId: number | undefined) => {
    if (!technicianId) return "Não Atribuído";
    const technician = technicians.find((t) => t.id === technicianId);
    if (!technician) return "Não Atribuído";

    const user = users.find((u) => u.id === technician.userId);
    return user?.name || "Técnico Desconhecido";
  };

  // Filter service orders
  const filteredOrders = serviceOrders.filter((order) => {
    const matchesStatus =
      statusFilter === "all" || order.status === statusFilter;
    const matchesSearch =
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      getClientName(order.clientId)
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      getEquipmentName(order.equipmentId)
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      getTechnicianName(order.technicianId)
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

    // Verifica se a data bate com o filtro
    const matchesDate =
      !dateFilter ||
      (order.createdAt &&
        new Date(order.createdAt).toDateString() === dateFilter.toDateString());

    return matchesStatus && matchesSearch && matchesDate;
  });

  // Sort service orders
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    switch (sortBy) {
      case "oldest":
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      case "orderNumber":
        return a.orderNumber.localeCompare(b.orderNumber);
      case "client":
        return getClientName(a.clientId).localeCompare(
          getClientName(b.clientId),
        );
      case "status":
        return a.status.localeCompare(b.status);
      default: // "newest"
        return (
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
    }
  });

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />

      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header
          title="Ordens de Serviço"
          onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-2 md:mb-0">
                Todas as Ordens de Serviço
              </h3>
              <div className="flex flex-col md:flex-row gap-2">
                <Link href="/service-orders/new">
                  <Button className="bg-primary hover:bg-primary-dark">
                    <Plus className="h-4 w-4 mr-1" />
                    Nova Ordem de Serviço
                  </Button>
                </Link>
              </div>
            </div>

            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar ordens..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filtrar por status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Status</SelectItem>
                  {SERVICE_ORDER_STATUSES.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex items-center">
                <Popover>
                  <PopoverTrigger asChild>
                    <CalendarButton
                      variant="outline"
                      className={`w-full justify-start text-left font-normal flex items-center whitespace-nowrap ${!dateFilter ? "text-muted-foreground" : ""}`}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {dateFilter
                        ? format(dateFilter, "dd/MM/yyyy", { locale: ptBR })
                        : "Filtrar por data"}
                    </CalendarButton>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={dateFilter}
                      onSelect={setDateFilter}
                      initialFocus
                    />
                    {dateFilter && (
                      <div className="p-3 border-t border-gray-200 flex justify-between">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDateFilter(undefined)}
                        >
                          Limpar
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => {
                            // Fecha o popover após confirmar
                            document.body.click();
                          }}
                        >
                          Aplicar
                        </Button>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              </div>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Mais Recentes</SelectItem>
                  <SelectItem value="oldest">Mais Antigos</SelectItem>
                  <SelectItem value="orderNumber">Número da Ordem</SelectItem>
                  <SelectItem value="client">Nome do Cliente</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="overflow-x-auto max-h-[calc(100vh-300px)]">
              {isLoading ? (
                <div className="p-8 flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : filteredOrders.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  Nenhuma ordem de serviço encontrada. Crie sua primeira agora!
                </div>
              ) : (
                <div className="relative">
                  <table className="min-w-full divide-y divide-gray-200 table-fixed">
                    <thead className="bg-gray-50 sticky top-0 z-10">
                      <tr>
                        <th
                          scope="col"
                          className="w-[10%] relative px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Ações
                        </th>
                        <th
                          scope="col"
                          className="w-[15%] px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          <div
                            className="flex items-center space-x-1 cursor-pointer"
                            onClick={() => setSortBy("orderNumber")}
                          >
                            <span>Número OS</span>
                            <ArrowUpDown className="h-4 w-4" />
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="w-[18%] px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          <div
                            className="flex items-center space-x-1 cursor-pointer"
                            onClick={() => setSortBy("client")}
                          >
                            <span>Cliente</span>
                            <ArrowUpDown className="h-4 w-4" />
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="w-[18%] px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Equipamento
                        </th>
                        <th
                          scope="col"
                          className="w-[12%] px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          <div
                            className="flex items-center space-x-1 cursor-pointer"
                            onClick={() => setSortBy("status")}
                          >
                            <span>Status</span>
                            <ArrowUpDown className="h-4 w-4" />
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="w-[12%] px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Técnico
                        </th>
                        <th
                          scope="col"
                          className="w-[15%] px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Data Criação
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sortedOrders.map((order) => {
                        const statusColors = getStatusColor(order.status);
                        return (
                          <tr key={order.id} className="hover:bg-gray-50">
                            <td className="px-3 py-2 text-center text-sm font-medium">
                              <Link href={`/service-orders/${order.id}`}>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-primary hover:text-primary-dark p-1 h-auto min-w-0"
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                </Button>
                              </Link>
                            </td>
                            <td className="px-3 py-2 truncate text-sm font-medium text-primary">
                              #{order.orderNumber}
                            </td>
                            <td className="px-3 py-2 truncate text-sm text-gray-900">
                              {getClientName(order.clientId)}
                            </td>
                            <td className="px-3 py-2 truncate text-sm text-gray-500">
                              {getEquipmentName(order.equipmentId)}
                            </td>
                            <td className="px-3 py-2">
                              <span
                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors.bg} ${statusColors.text}`}
                              >
                                {getStatusLabel(order.status)}
                              </span>
                            </td>
                            <td className="px-3 py-2 truncate text-sm text-gray-500">
                              {getTechnicianName(order.technicianId)}
                            </td>
                            <td className="px-3 py-2 truncate text-sm text-gray-500">
                              {formatDateTime(order.createdAt)}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            <div className="px-6 py-3 border-t border-gray-200 bg-gray-50 text-sm text-gray-500">
              Exibindo {filteredOrders.length} de {serviceOrders.length} ordens
              de serviço no total
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
