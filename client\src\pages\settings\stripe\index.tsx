import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { 
  Ta<PERSON>, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Link } from "wouter";
import { Eye, EyeOff, ExternalLink } from "lucide-react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Define o esquema para a configuração do Stripe
const stripeConfigSchema = z.object({
  STRIPE_SECRET_KEY: z.string().min(1, {
    message: "A chave secreta do Stripe é obrigatória.",
  }),
  VITE_STRIPE_PUBLIC_KEY: z.string().min(1, {
    message: "A chave pública do Stripe é obrigatória.",
  }),
});

export default function StripeSettingsPage() {
  const { toast } = useToast();
  const [secretVisible, setSecretVisible] = useState(false);
  
  // Form para configuração do Stripe
  const form = useForm<z.infer<typeof stripeConfigSchema>>({
    resolver: zodResolver(stripeConfigSchema),
    defaultValues: {
      STRIPE_SECRET_KEY: "",
      VITE_STRIPE_PUBLIC_KEY: "",
    },
  });
  
  // Handler para salvar configurações
  const onSubmit = (values: z.infer<typeof stripeConfigSchema>) => {
    // Aqui em uma implementação real, salvaria as chaves em variáveis de ambiente seguras
    // Como não temos acesso direto a isso, mostramos um toast
    toast({
      title: "Configurações salvas",
      description: "As chaves do Stripe foram salvas com sucesso. As funções de pagamento estão agora disponíveis.",
    });
  };
  
  // Handler para testar a conexão
  const testConnection = () => {
    // Simulação de teste de conexão
    toast({
      title: "Teste de conexão",
      description: "A conexão com o Stripe foi testada com sucesso. A chave API está funcionando corretamente.",
    });
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Configuração do Stripe</h1>
      </div>

      <Tabs defaultValue="config">
        <TabsList className="mb-4">
          <TabsTrigger value="config">Configuração</TabsTrigger>
          <TabsTrigger value="webhook">Webhook</TabsTrigger>
          <TabsTrigger value="help">Ajuda</TabsTrigger>
        </TabsList>

        <TabsContent value="config">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>Chaves API do Stripe</CardTitle>
                <CardDescription>
                  Configure as chaves API do Stripe para processar pagamentos na plataforma.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                      control={form.control}
                      name="VITE_STRIPE_PUBLIC_KEY"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Chave Pública (Publishable Key)</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="pk_test_..."
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Esta chave é usada pelo cliente para inicializar o Stripe.js.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="STRIPE_SECRET_KEY"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Chave Secreta (Secret Key)</FormLabel>
                          <div className="flex items-center space-x-2">
                            <FormControl>
                              <div className="relative w-full">
                                <Input 
                                  type={secretVisible ? "text" : "password"}
                                  placeholder="sk_test_..."
                                  {...field} 
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                                  onClick={() => setSecretVisible(!secretVisible)}
                                >
                                  {secretVisible ? <EyeOff size={16} /> : <Eye size={16} />}
                                </Button>
                              </div>
                            </FormControl>
                          </div>
                          <FormDescription>
                            Esta chave é usada pelo servidor para integração com o Stripe.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex flex-col space-y-2">
                      <Button type="submit">Salvar Configurações</Button>
                      <Button 
                        type="button" 
                        variant="outline"
                        onClick={testConnection}
                      >
                        Testar Conexão
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
              <CardFooter className="border-t pt-6 flex flex-col items-start">
                <p className="text-sm text-muted-foreground mb-2">
                  Não tem uma conta no Stripe? Crie uma conta gratuita para começar a processar pagamentos.
                </p>
                <Button asChild variant="link" className="p-0">
                  <Link href="https://dashboard.stripe.com/register" target="_blank" className="flex items-center">
                    Criar conta no Stripe
                    <ExternalLink size={14} className="ml-1" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Informações</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <AlertTitle>Ambiente de Teste</AlertTitle>
                    <AlertDescription>
                      Use chaves de teste (começando com <code>pk_test_</code> e <code>sk_test_</code>) para realizar testes sem processamento real de pagamento.
                    </AlertDescription>
                  </Alert>

                  <Alert>
                    <AlertTitle>Ambiente de Produção</AlertTitle>
                    <AlertDescription>
                      Para processar pagamentos reais, use chaves de produção (começando com <code>pk_live_</code> e <code>sk_live_</code>).
                    </AlertDescription>
                  </Alert>

                  <div className="pt-2">
                    <h3 className="font-medium mb-2">Onde encontrar as chaves API?</h3>
                    <ol className="list-decimal list-inside space-y-2 text-sm">
                      <li>Acesse o <a href="https://dashboard.stripe.com" target="_blank" className="text-primary hover:underline">Painel do Stripe</a></li>
                      <li>Navegue até <strong>Developers → API keys</strong></li>
                      <li>As chaves estarão disponíveis nesta página</li>
                    </ol>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recursos do Stripe</CardTitle>
                  <CardDescription>
                    Funcionalidades disponíveis com a integração do Stripe
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <div className="rounded-full bg-green-500 h-4 w-4 mt-1 mr-2 flex items-center justify-center">
                        <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 1L3.5 6.5L1 4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <span>Processamento de cartão de crédito/débito</span>
                    </li>
                    <li className="flex items-start">
                      <div className="rounded-full bg-green-500 h-4 w-4 mt-1 mr-2 flex items-center justify-center">
                        <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 1L3.5 6.5L1 4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <span>Suporte a pagamentos internacionais</span>
                    </li>
                    <li className="flex items-start">
                      <div className="rounded-full bg-green-500 h-4 w-4 mt-1 mr-2 flex items-center justify-center">
                        <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 1L3.5 6.5L1 4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <span>Geração de faturas e recibos automáticos</span>
                    </li>
                    <li className="flex items-start">
                      <div className="rounded-full bg-green-500 h-4 w-4 mt-1 mr-2 flex items-center justify-center">
                        <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 1L3.5 6.5L1 4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <span>Detecção de fraudes e segurança avançada</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="webhook">
          <Card>
            <CardHeader>
              <CardTitle>Configuração de Webhook</CardTitle>
              <CardDescription>
                Configure webhooks para receber notificações de eventos do Stripe.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">URL do Webhook</h3>
                <div className="flex items-center space-x-2">
                  <Input
                    value={`${window.location.origin}/api/stripe/webhook`}
                    readOnly
                  />
                  <Button variant="outline" onClick={() => {
                    navigator.clipboard.writeText(`${window.location.origin}/api/stripe/webhook`);
                    toast({
                      title: "URL copiada",
                      description: "A URL do webhook foi copiada para a área de transferência.",
                    });
                  }}>
                    Copiar
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Use esta URL para configurar o webhook no painel do Stripe.
                </p>
              </div>

              <Alert>
                <AlertTitle>Eventos Recomendados</AlertTitle>
                <AlertDescription>
                  <p className="mb-2">Recomendamos configurar os seguintes eventos no webhook:</p>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>payment_intent.succeeded</li>
                    <li>payment_intent.payment_failed</li>
                    <li>invoice.payment_succeeded</li>
                    <li>invoice.payment_failed</li>
                    <li>charge.refunded</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="pt-2">
                <h3 className="font-medium mb-2">Como configurar um webhook:</h3>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Acesse o <a href="https://dashboard.stripe.com/webhooks" target="_blank" className="text-primary hover:underline">Painel de Webhooks do Stripe</a></li>
                  <li>Clique em "Adicionar endpoint"</li>
                  <li>Cole a URL do webhook fornecida acima</li>
                  <li>Selecione os eventos que deseja receber</li>
                  <li>Clique em "Adicionar endpoint" para finalizar</li>
                </ol>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="help">
          <Card>
            <CardHeader>
              <CardTitle>Ajuda e Recursos</CardTitle>
              <CardDescription>
                Recursos úteis para ajudar com a integração do Stripe.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-medium mb-2">Documentação do Stripe</h3>
                <ul className="space-y-2">
                  <li>
                    <a 
                      href="https://stripe.com/docs" 
                      target="_blank"
                      className="text-primary hover:underline flex items-center"
                    >
                      Documentação Geral
                      <ExternalLink size={14} className="ml-1" />
                    </a>
                  </li>
                  <li>
                    <a 
                      href="https://stripe.com/docs/api" 
                      target="_blank"
                      className="text-primary hover:underline flex items-center"
                    >
                      Referência da API
                      <ExternalLink size={14} className="ml-1" />
                    </a>
                  </li>
                  <li>
                    <a 
                      href="https://stripe.com/docs/testing" 
                      target="_blank"
                      className="text-primary hover:underline flex items-center"
                    >
                      Cartões de Teste
                      <ExternalLink size={14} className="ml-1" />
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium mb-2">Números de Cartão para Teste</h3>
                <div className="bg-muted p-4 rounded-md">
                  <p className="font-medium">Cartão de Crédito (pagamento aprovado)</p>
                  <p className="font-mono mt-1">4242 4242 4242 4242</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Use qualquer data futura para o vencimento e qualquer código CVC.
                  </p>
                </div>
                <div className="bg-muted p-4 rounded-md mt-2">
                  <p className="font-medium">Cartão de Crédito (pagamento recusado)</p>
                  <p className="font-mono mt-1">4000 0000 0000 0002</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Use para simular uma transação recusada.
                  </p>
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Suporte</h3>
                <p className="text-sm text-muted-foreground">
                  Se você encontrar problemas com a integração do Stripe, acesse a documentação ou entre em contato com o suporte.
                </p>
                <div className="flex gap-2 mt-2">
                  <Button asChild variant="outline">
                    <Link href="https://support.stripe.com" target="_blank" className="flex items-center">
                      Suporte Stripe
                      <ExternalLink size={14} className="ml-1" />
                    </Link>
                  </Button>
                  <Button asChild variant="outline">
                    <Link href="https://status.stripe.com" target="_blank" className="flex items-center">
                      Status do Stripe
                      <ExternalLink size={14} className="ml-1" />
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}