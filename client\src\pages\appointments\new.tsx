import { useForm } from "react-hook-form";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, ArrowLeft, Loader2 } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { insertAppointmentSchema, type InsertAppointment } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";

export default function NewAppointmentPage() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: clients = [] } = useQuery({
    queryKey: ["/api/clients"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/clients");
      return response;
    }
  });

  const { data: technicians = [] } = useQuery({
    queryKey: ["/api/technicians"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/technicians");
      return response;
    }
  });

  const { data: users = [] } = useQuery({
    queryKey: ["/api/users"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/users");
      return response;
    }
  });

  const { data: serviceOrders = [] } = useQuery({
    queryKey: ["/api/service-orders"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/service-orders");
      return response;
    }
  });

  const form = useForm<InsertAppointment>({
    resolver: zodResolver(insertAppointmentSchema),
    defaultValues: {
      title: "",
      description: null,
      type: "technical_visit",
      status: "scheduled",
      appointmentDate: new Date(),
      startTime: "",
      endTime: null,
      location: null,
      clientId: null,
      technicianId: null,
      serviceOrderId: null,
      contactPerson: null,
      contactPhone: null,
      contactEmail: null,
      notes: null,
      isRecurring: false,
      recurringPattern: null,
      createdBy: 1 // Default to logged in user - will be set by session
    }
  });

  const createAppointmentMutation = useMutation({
    mutationFn: async (data: InsertAppointment) => {
      try {
        console.log("Enviando dados para API:", data);
        // apiRequest já retorna o JSON parsed
        const result = await apiRequest("POST", "/api/appointments", data);
        console.log("Agendamento criado com sucesso:", result);
        return result;
      } catch (error) {
        console.error("Erro na requisição:", error);
        throw error;
      }
    },
    onSuccess: async (data) => {
      console.log("Agendamento criado com sucesso:", data);
      toast({
        title: "Sucesso",
        description: "Agendamento criado com sucesso!",
      });
      // Invalidar todas as queries relacionadas a appointments
      await queryClient.invalidateQueries({ queryKey: ["/api/appointments"] });
      await queryClient.invalidateQueries({ queryKey: ["/api/appointments/test"] });
      
      // Forçar refetch de appointments
      await queryClient.refetchQueries({ queryKey: ["/api/appointments"] });
      
      // Wait a bit before navigating to ensure data is updated
      setTimeout(() => {
        setLocation("/appointments");
      }, 100);
    },
    onError: (error) => {
      console.error("Erro ao criar agendamento:", error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao criar agendamento",
        variant: "destructive",
      });
    }
  });

  const checkConflictsMutation = useMutation({
    mutationFn: async (data: { technicianId?: number | null; appointmentDate: string; startTime: string; endTime?: string | null }) => {
      try {
        // TEMPORARIAMENTE DESABILITADO - Para permitir testes
        console.log('Verificação de conflitos temporariamente desabilitada');
        return { hasConflict: false, conflicts: [] };
        
        /* 
        if (!data.technicianId) return { hasConflict: false, conflicts: [] };
        
        const dateObj = new Date(data.appointmentDate);
        const dateStr = dateObj.toISOString().split('T')[0];
        
        // apiRequest já retorna o JSON parsed
        console.log('Verificando conflitos para:', { technicianId: data.technicianId, date: dateStr });
        const appointments = await apiRequest("GET", `/api/appointments?technicianId=${data.technicianId}&date=${dateStr}`);
        console.log('Agendamentos encontrados:', appointments);
        
        const conflicts = appointments.filter((appt: any) => {
          // Converter horários para minutos para comparação correta
          const timeToMinutes = (timeStr: string) => {
            if (!timeStr) return 0;
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
          };
          
          const apptStart = timeToMinutes(appt.startTime);
          const apptEnd = timeToMinutes(appt.endTime || appt.startTime);
          const newStart = timeToMinutes(data.startTime);
          const newEnd = timeToMinutes(data.endTime || data.startTime);
          
          // Debug logs
          console.log('Comparando horários:', {
            id: appt.id,
            agendamentoExistente: `${appt.startTime} - ${appt.endTime}`,
            novoAgendamento: `${data.startTime} - ${data.endTime}`,
            apptStart, apptEnd, newStart, newEnd
          });
          
          // Verificar sobreposição de horários
          const hasOverlap = (
            (newStart < apptEnd && newEnd > apptStart) // Simplified overlap check
          );
          
          if (hasOverlap) {
            console.log('CONFLITO DETECTADO:', appt);
          }
          
          return hasOverlap;
        });
        
        return { hasConflict: conflicts.length > 0, conflicts };
        */
      } catch (error) {
        console.error("Erro ao verificar conflitos:", error);
        // Em caso de erro, permite continuar sem verificação
        return { hasConflict: false, conflicts: [] };
      }
    },
  });

  const onSubmit = async (data: InsertAppointment) => {
    console.log("Dados do formulário:", data);
    
    // Validar campos obrigatórios
    if (!data.title || !data.appointmentDate || !data.startTime) {
      toast({
        title: "Erro de Validação",
        description: "Preencha todos os campos obrigatórios: Título, Data e Hora de Início.",
        variant: "destructive",
      });
      return;
    }
    
    // Validar conflitos de horário se há técnico selecionado
    if (data.technicianId && data.startTime) {
      try {
        const conflictCheck = await checkConflictsMutation.mutateAsync({
          technicianId: data.technicianId,
          appointmentDate: new Date(data.appointmentDate).toISOString(),
          startTime: data.startTime,
          endTime: data.endTime
        });
        
        if (conflictCheck.hasConflict) {
          toast({
            title: "Conflito de Horário",
            description: `O técnico já tem agendamento(s) neste horário. Verifique os horários existentes.`,
            variant: "destructive",
          });
          return;
        }
      } catch (error: any) {
        console.warn("Erro ao verificar conflitos:", error?.message || error);
        toast({
          title: "Erro ao verificar conflitos",
          description: error?.message || "Erro desconhecido",
          variant: "destructive",
        });
        return;
      }
    }

    // Preparar dados para submissão
    const appointmentData = {
      ...data,
      appointmentDate: data.appointmentDate instanceof Date
        ? data.appointmentDate
        : new Date(data.appointmentDate),
      startTime: data.startTime,
      endTime: data.endTime || null,
      location: data.location || null,
      clientId: data.clientId || null,
      technicianId: data.technicianId || null,
      serviceOrderId: data.serviceOrderId || null,
      contactPerson: data.contactPerson || null,
      contactPhone: data.contactPhone || null,
      contactEmail: data.contactEmail || null,
      notes: data.notes || null,
      isRecurring: data.isRecurring || false,
      recurringPattern: data.recurringPattern || null,
      createdBy: 1 // Will be set by server from session
    };
    
    console.log("Dados processados para envio:", appointmentData);
    createAppointmentMutation.mutate(appointmentData);
  };

  const getTechnicianName = (technicianId: number) => {
    const technician = technicians.find((t: any) => t.id === technicianId);
    if (!technician) return "";
    const user = users.find((u: any) => u.id === technician.userId);
    return user ? user.name : "";
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Novo Agendamento" />
        <main className="flex-1 overflow-y-auto bg-slate-50">
          <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl">
            {/* Page Header */}
            <div className="flex items-center gap-4 mb-6">
              <Link href="/appointments">
                <Button variant="outline" size="icon">
                  <ArrowLeft className="w-4 h-4" />
                </Button>
              </Link>
              <div className="min-w-0 flex-1">
                <p className="text-sm text-gray-600">Crie um novo agendamento para visitas técnicas, entregas ou reuniões</p>
              </div>
            </div>
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Informações do Agendamento
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                    {/* Basic Information Section */}
                    <div className="space-y-6">
                      <div className="border-b border-gray-200 pb-4">
                        <h3 className="text-lg font-medium text-gray-900">Informações Básicas</h3>
                        <p className="text-sm text-gray-500 mt-1">Defina o título e tipo do agendamento</p>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="title"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Título *</FormLabel>
                              <FormControl>
                                <Input placeholder="Ex: Visita técnica - Manutenção" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="type"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Tipo *</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Selecione o tipo" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="technical_visit">Visita Técnica</SelectItem>
                                  <SelectItem value="equipment_delivery">Entrega de Equipamento</SelectItem>
                                  <SelectItem value="equipment_pickup">Retirada de Equipamento</SelectItem>
                                  <SelectItem value="supplier_visit">Visita de Fornecedor</SelectItem>
                                  <SelectItem value="other">Outros</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Descrição</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Descreva os detalhes do agendamento..."
                                className="min-h-[100px]"
                                {...field}
                                value={String(field.value ?? "")}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Date and Time Section */}
                    <div className="space-y-6">
                      <div className="border-b border-gray-200 pb-4">
                        <h3 className="text-lg font-medium text-gray-900">Data e Horário</h3>
                        <p className="text-sm text-gray-500 mt-1">Configure quando o agendamento deve ocorrer</p>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          control={form.control}
                          name="appointmentDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Data *</FormLabel>
                              <FormControl>
                                <Input
                                  type="date"
                                  {...field}
                                  value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value}
                                  onChange={(e) => field.onChange(new Date(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="startTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Hora de Início *</FormLabel>
                              <FormControl>
                                <Input type="time" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="endTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Hora de Término</FormLabel>
                              <FormControl>
                                <Input type="time" {...field} value={String(field.value ?? "")} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="location"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Local</FormLabel>
                            <FormControl>
                              <Input placeholder="Endereço ou local do agendamento" {...field} value={String(field.value ?? "")} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Assignment Section */}
                    <div className="space-y-6">
                      <div className="border-b border-gray-200 pb-4">
                        <h3 className="text-lg font-medium text-gray-900">Atribuição</h3>
                        <p className="text-sm text-gray-500 mt-1">Defina cliente, técnico e ordem de serviço relacionada</p>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name="clientId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cliente</FormLabel>
                            <Select onValueChange={(value) => field.onChange(value ? parseInt(value) : null)}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um cliente" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {clients.map((client: any) => (
                                  <SelectItem key={client.id} value={client.id.toString()}>
                                    {client.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="technicianId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Técnico</FormLabel>
                            <Select onValueChange={(value) => field.onChange(value ? parseInt(value) : null)}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um técnico" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {technicians.map((technician: any) => (
                                  <SelectItem key={technician.id} value={technician.id.toString()}>
                                    {getTechnicianName(technician.id)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="serviceOrderId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Ordem de Serviço</FormLabel>
                            <Select onValueChange={(value) => field.onChange(value ? parseInt(value) : null)}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione uma OS" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {serviceOrders.map((order: any) => (
                                  <SelectItem key={order.id} value={order.id.toString()}>
                                    {order.orderNumber} - {order.description.substring(0, 50)}...
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      </div>
                    </div>

                    {/* Contact Information Section */}
                    <div className="space-y-6">
                      <div className="border-b border-gray-200 pb-4">
                        <h3 className="text-lg font-medium text-gray-900">Informações de Contato</h3>
                        <p className="text-sm text-gray-500 mt-1">Dados opcionais para contato direto</p>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          control={form.control}
                          name="contactPerson"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Pessoa de Contato</FormLabel>
                              <FormControl>
                                <Input placeholder="Nome do contato" {...field} value={String(field.value ?? "")} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="contactPhone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Telefone</FormLabel>
                              <FormControl>
                                <Input placeholder="(11) 99999-9999" {...field} value={String(field.value ?? "")} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="contactEmail"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>E-mail</FormLabel>
                              <FormControl>
                                <Input type="email" placeholder="<EMAIL>" {...field} value={String(field.value ?? "")} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Observações</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Observações adicionais sobre o agendamento..."
                              {...field}
                              value={String(field.value ?? "")}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Actions */}
                    <div className="flex flex-col sm:flex-row justify-end gap-3 pt-8 border-t border-gray-200">
                      <Link href="/appointments">
                        <Button type="button" variant="outline" className="w-full sm:w-auto">
                          Cancelar
                        </Button>
                      </Link>
                      <Button 
                        type="submit" 
                        disabled={createAppointmentMutation.isPending}
                        className="w-full sm:w-auto"
                      >
                        {createAppointmentMutation.isPending ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Criando...
                          </>
                        ) : (
                          "Criar Agendamento"
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}