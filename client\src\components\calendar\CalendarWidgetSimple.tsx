import React, { useMemo } from 'react';
import { format, addDays, startOfDay, endOfDay, isSameDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import type { Appointment, TechnicianSchedule } from '@shared/schema';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CalendarIcon, Clock } from 'lucide-react';

interface CalendarEvent {
  id: number;
  title: string;
  date: Date;
  time: string;
  type: 'appointment' | 'schedule';
  resource: Appointment | TechnicianSchedule;
}

interface CalendarWidgetSimpleProps {
  className?: string;
  daysToShow?: number;
}

export default function CalendarWidgetSimple({ 
  className = "",
  daysToShow = 7
}: CalendarWidgetSimpleProps) {
  const today = new Date();
  const endDate = addDays(today, daysToShow);

  const { data: appointments = [], error } = useQuery({
    queryKey: ["/api/appointments"],
    queryFn: async () => {
      console.log("Carregando appointments no widget...");
      const result = await apiRequest("GET", "/api/appointments");
      console.log("Appointments carregados no widget:", result);
      return result;
    }
  });

  console.log("Estado da query do widget:", { appointments, error });

  const { data: schedules = [] } = useQuery({
    queryKey: ["/api/technician-schedules"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/technician-schedules");
      return response;
    }
  });

  const events: CalendarEvent[] = useMemo(() => {
    const appointmentEvents = appointments
      .filter((appointment: Appointment) => {
        const appointmentDate = new Date(appointment.appointmentDate);
        return appointmentDate >= startOfDay(today) && appointmentDate <= endOfDay(endDate);
      })
      .map((appointment: Appointment) => {
        const appointmentDate = new Date(appointment.appointmentDate);
        const time = appointment.startTime ? appointment.startTime.substring(0, 5) : '09:00';

        return {
          id: appointment.id,
          title: appointment.title,
          date: appointmentDate,
          time,
          type: 'appointment' as const,
          resource: appointment
        };
      });

    const scheduleEvents = schedules
      .filter((schedule: TechnicianSchedule) => {
        const scheduleDate = new Date(schedule.scheduleDate);
        return scheduleDate >= startOfDay(today) && scheduleDate <= endOfDay(endDate);
      })
      .map((schedule: TechnicianSchedule) => {
        const scheduleDate = new Date(schedule.scheduleDate);
        const time = schedule.startTime ? schedule.startTime.substring(0, 5) : '09:00';

        return {
          id: schedule.id,
          title: `${schedule.title} (Técnico)`,
          date: scheduleDate,
          time,
          type: 'schedule' as const,
          resource: schedule
        };
      });

    return [...appointmentEvents, ...scheduleEvents].sort((a, b) => {
      const dateCompare = a.date.getTime() - b.date.getTime();
      if (dateCompare !== 0) return dateCompare;
      return a.time.localeCompare(b.time);
    });
  }, [appointments, schedules, today, endDate]);

  const eventsByDay = useMemo(() => {
    const days = [];
    for (let i = 0; i < daysToShow; i++) {
      const day = addDays(today, i);
      const dayEvents = events.filter(event => isSameDay(event.date, day));
      days.push({ date: day, events: dayEvents });
    }
    return days;
  }, [events, today, daysToShow]);

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <CalendarIcon className="w-5 h-5" />
          Próximos {daysToShow} dias
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-3 flex flex-wrap gap-2 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded bg-blue-500"></div>
            <span>Agendamentos</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded bg-green-500"></div>
            <span>Técnicos</span>
          </div>
        </div>
        
        <div className="space-y-3">
          {eventsByDay.map(({ date, events: dayEvents }, index) => (
            <div key={index} className="border-l-2 border-gray-200 pl-3">
              <div className="text-sm font-medium text-gray-600 mb-2">
                {format(date, 'EEEE, dd/MM', { locale: ptBR })}
                {isSameDay(date, today) && (
                  <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">Hoje</span>
                )}
              </div>
              
              {dayEvents.length === 0 ? (
                <div className="text-xs text-gray-400 italic">Nenhum evento</div>
              ) : (
                <div className="space-y-2">
                  {dayEvents.map((event) => (
                    <div
                      key={`${event.type}-${event.id}`}
                      className={`p-2 rounded-lg text-xs ${
                        event.type === 'appointment' 
                          ? 'bg-blue-50 border border-blue-200' 
                          : 'bg-green-50 border border-green-200'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{event.title}</div>
                          <div className="flex items-center gap-1 text-gray-500 mt-1">
                            <Clock className="w-3 h-3" />
                            <span>{event.time}</span>
                          </div>
                        </div>
                        <div className={`w-2 h-2 rounded-full mt-1 ${
                          event.type === 'appointment' ? 'bg-blue-500' : 'bg-green-500'
                        }`} />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
        
        {events.length === 0 && (
          <div className="text-center py-8 text-gray-500 text-sm">
            Nenhum evento nos próximos {daysToShow} dias
          </div>
        )}
      </CardContent>
    </Card>
  );
}