import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, Save, ArrowLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  ServiceOrder,
  Client,
  Equipment,
  Technician,
  User,
} from "@/lib/types";
import { SERVICE_ORDER_STATUSES } from "@/lib/constants";

// Schema de validação para edição da ordem de serviço
const editServiceOrderSchema = z.object({
  description: z.string().min(10, "A descrição deve ter pelo menos 10 caracteres"),
  clientId: z.coerce.number().min(1, "Cliente é obrigatório"),
  equipmentId: z.coerce.number().optional(),
  technicianId: z.coerce.number().optional(),
  status: z.enum(SERVICE_ORDER_STATUSES.map(s => s.value) as [string, ...string[]]),
  diagnostics: z.string().optional(),
  solution: z.string().optional(),
  estimatedCompletionDate: z.date().optional(),
});

type EditServiceOrderFormData = z.infer<typeof editServiceOrderSchema>;

export default function EditServiceOrderPage() {
  const { id } = useParams();
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Consulta da ordem de serviço
  const { data: serviceOrder, isLoading: isLoadingServiceOrder } = useQuery<ServiceOrder>({
    queryKey: [`/api/service-orders/${id}`],
    enabled: !!id,
  });

  // Consultas de dados relacionados
  const { data: clients } = useQuery<Client[]>({
    queryKey: ["/api/clients"],
  });

  const { data: equipment } = useQuery<Equipment[]>({
    queryKey: ["/api/equipment"],
  });

  const { data: technicians } = useQuery<Technician[]>({
    queryKey: ["/api/technicians"],
  });

  const { data: users } = useQuery<User[]>({
    queryKey: ["/api/users"],
  });

  // Formulário
  const form = useForm<EditServiceOrderFormData>({
    resolver: zodResolver(editServiceOrderSchema),
    defaultValues: {
      description: "",
      clientId: 0,
      status: "received",
      diagnostics: "",
      solution: "",
    },
  });

  // Preencher formulário quando dados carregarem
  useEffect(() => {
    if (serviceOrder) {
      form.reset({
        description: serviceOrder.description,
        clientId: serviceOrder.clientId,
        equipmentId: serviceOrder.equipmentId || undefined,
        technicianId: serviceOrder.technicianId || undefined,
        status: serviceOrder.status,
        diagnostics: serviceOrder.diagnostics || "",
        solution: serviceOrder.solution || "",
        estimatedCompletionDate: serviceOrder.estimatedCompletionDate 
          ? new Date(serviceOrder.estimatedCompletionDate) 
          : undefined,
      });
    }
  }, [serviceOrder, form]);

  // Mutação para atualizar a ordem de serviço
  const updateMutation = useMutation({
    mutationFn: async (data: EditServiceOrderFormData) => {
      const updateData = {
        description: data.description,
        clientId: data.clientId,
        equipmentId: data.equipmentId === 0 ? null : data.equipmentId,
        technicianId: data.technicianId === 0 ? null : data.technicianId,
        status: data.status,
        diagnostics: data.diagnostics || null,
        solution: data.solution || null,
        estimatedCompletionDate: data.estimatedCompletionDate?.toISOString() || null,
      };
      
      console.log('Enviando dados para atualização:', updateData);
      return await apiRequest("PUT", `/api/service-orders/${id}`, updateData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [`/api/service-orders/${id}`]
      });
      queryClient.invalidateQueries({
        queryKey: ['/api/service-orders']
      });
      toast({
        title: "Ordem de serviço atualizada",
        description: "As alterações foram salvas com sucesso.",
      });
      navigate(`/service-orders/${id}`);
    },
    onError: (error) => {
      console.error('Erro ao atualizar ordem de serviço:', error);
      
      // Verificar se o erro contém informação útil
      let errorMessage = "Não foi possível salvar as alterações. Tente novamente.";
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }
      
      toast({
        title: "Erro ao atualizar",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: EditServiceOrderFormData) => {
    updateMutation.mutate(data);
  };

  if (isLoadingServiceOrder) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header
            title="Carregando..."
            onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-lg text-gray-600">Carregando ordem de serviço...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!serviceOrder) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header
            title="Ordem não encontrada"
            onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-lg text-gray-600">Ordem de serviço não encontrada</p>
                <Button 
                  onClick={() => navigate("/service-orders")} 
                  className="mt-4"
                >
                  Voltar para Ordens de Serviço
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header
          title={`Editar Ordem ${serviceOrder.orderNumber}`}
          onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="container mx-auto py-6">
            <div className="mb-6">
              <Button
                variant="outline"
                onClick={() => navigate(`/service-orders/${id}`)}
                className="mb-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar aos Detalhes
              </Button>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Editar Ordem de Serviço</CardTitle>
                <CardDescription>
                  Edite as informações da ordem de serviço #{serviceOrder.orderNumber}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="clientId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Cliente</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value?.toString()}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um cliente" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {clients?.map((client) => (
                                  <SelectItem key={client.id} value={client.id.toString()}>
                                    {client.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="equipmentId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Equipamento (Opcional)</FormLabel>
                            <Select
                              onValueChange={(value) => field.onChange(value === "0" ? undefined : Number(value))}
                              value={field.value?.toString() || "0"}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um equipamento" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="0">Nenhum equipamento</SelectItem>
                                {equipment?.filter(item => item.id && item.id > 0).map((item) => (
                                  <SelectItem key={item.id} value={item.id.toString()}>
                                    {item.brand} {item.model}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="technicianId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Técnico Responsável</FormLabel>
                            <Select
                              onValueChange={(value) => field.onChange(value === "0" ? undefined : Number(value))}
                              value={field.value?.toString() || "0"}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um técnico" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="0">Não atribuído</SelectItem>
                                {technicians?.filter(technician => technician.id && technician.id > 0).map((technician) => {
                                  const user = users?.find(u => u.id === technician.userId);
                                  return (
                                    <SelectItem key={technician.id} value={technician.id.toString()}>
                                      {user?.name || `Técnico ${technician.id}`}
                                    </SelectItem>
                                  );
                                })}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione um status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {SERVICE_ORDER_STATUSES.map((status) => (
                                  <SelectItem key={status.value} value={status.value}>
                                    {status.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="estimatedCompletionDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Data Estimada de Conclusão</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "w-[240px] pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP", { locale: ptBR })
                                  ) : (
                                    <span>Selecione uma data</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date(new Date().setHours(0, 0, 0, 0))
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Descrição do Problema</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Descreva o problema relatado pelo cliente..."
                              className="resize-none"
                              rows={4}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="diagnostics"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Diagnóstico Técnico</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Descreva o diagnóstico técnico do problema..."
                              className="resize-none"
                              rows={4}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="solution"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Solução Aplicada</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Descreva a solução aplicada ou os reparos realizados..."
                              className="resize-none"
                              rows={4}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex gap-4 pt-6">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => navigate(`/service-orders/${id}`)}
                        className="flex-1"
                      >
                        Cancelar
                      </Button>
                      <Button
                        type="submit"
                        disabled={updateMutation.isPending}
                        className="flex-1"
                      >
                        {updateMutation.isPending ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Salvando...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Salvar Alterações
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}