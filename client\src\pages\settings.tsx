import { useLocation } from "wouter";
import { useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Bell, 
  Settings as SettingsIcon, 
  User, 
  Users, 
  Database, 
  Smartphone, 
  CreditCard, 
  Lock, 
  Globe, 
  Inbox 
} from "lucide-react";

export default function Settings() {
  const [, navigate] = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  const settingsCategories = [
    {
      title: "Comunicações",
      description: "Configurar notificações e integrações com serviços externos",
      items: [
        {
          icon: <Smartphone className="h-5 w-5" />,
          title: "WhatsApp",
          description: "Configurar integração com WhatsApp Business API",
          path: "/settings/whatsapp",
        },
        {
          icon: <Bell className="h-5 w-5" />,
          title: "Notificações por E-mail",
          description: "Configurar modelos de email e gatilhos de notificação",
          path: "/settings/email",
        }
      ]
    },
    {
      title: "Usuários e Permissões",
      description: "Gerenciar usuários, perfis e permissões de acesso",
      items: [
        {
          icon: <Users className="h-5 w-5" />,
          title: "Usuários",
          description: "Gerenciar usuários e perfis de acesso",
          path: "/settings/users",
        },
        {
          icon: <Lock className="h-5 w-5" />,
          title: "Permissões",
          description: "Configurar permissões por perfil",
          path: "/settings/permissions",
        }
      ]
    },
    {
      title: "Financeiro",
      description: "Configurar gateways de pagamento e parâmetros financeiros",
      items: [
        {
          icon: <CreditCard className="h-5 w-5" />,
          title: "Métodos de Pagamento",
          description: "Configurar métodos de pagamento aceitos pelo sistema",
          path: "/settings/payment-methods",
        },
        {
          icon: <Inbox className="h-5 w-5" />,
          title: "Integração Stripe",
          description: "Configurar integração com o gateway de pagamento Stripe",
          path: "/settings/stripe",
        }
      ]
    },
    {
      title: "Sistema",
      description: "Configurações gerais do sistema",
      items: [
        {
          icon: <Database className="h-5 w-5" />,
          title: "Backup e Recuperação",
          description: "Configurar backups automáticos e recuperação de dados",
          path: "/settings/backup",
        },
        {
          icon: <Globe className="h-5 w-5" />,
          title: "Localização",
          description: "Configurações de idioma, moeda e fuso horário",
          path: "/settings/localization",
        }
      ]
    }
  ];

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar 
        open={mobileMenuOpen} 
        setOpen={setMobileMenuOpen} 
      />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header 
          title="Configurações do Sistema" 
          onMenuButtonClick={toggleMobileMenu}
        />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="container mx-auto">
            <div className="mb-6">
              <h2 className="text-2xl font-semibold">Configurações do Sistema</h2>
              <p className="text-gray-500 mt-1">
                Gerencie as configurações do sistema para personalizar o funcionamento do Simplesmed TechServer.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {settingsCategories.map((category, index) => (
                <Card key={index} className="shadow-sm">
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <SettingsIcon className="h-5 w-5 text-primary" />
                      <CardTitle>{category.title}</CardTitle>
                    </div>
                    <CardDescription>{category.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {category.items.map((item, itemIndex) => (
                        <Button 
                          key={itemIndex} 
                          variant="ghost"
                          className="flex items-start w-full gap-4 p-3 rounded-md hover:bg-slate-50 transition-colors cursor-pointer h-auto justify-start"
                          onClick={() => navigate(item.path)}
                        >
                          <div className="mt-0.5 bg-primary/10 p-2 rounded-full">
                            {item.icon}
                          </div>
                          <div className="text-left">
                            <h3 className="font-medium">{item.title}</h3>
                            <p className="text-sm text-gray-500">{item.description}</p>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}