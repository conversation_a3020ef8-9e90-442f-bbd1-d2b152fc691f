import { cn } from "@/lib/utils";
import { Link, useLocation } from "wouter";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  LayoutDashboard,
  ClipboardList,
  Users,
  Laptop,
  Package,
  Wrench,
  FileText,
  ChartBarStacked,
  Settings,
  X,
  Menu,
  BarChart3,
  CreditCard,
  Calendar
} from "lucide-react";
import { NAVIGATION_ITEMS } from "@/lib/constants";

interface SidebarProps {
  className?: string;
  open?: boolean;
  setOpen?: (open: boolean) => void;
}

const iconMap: Record<string, React.ReactNode> = {
  LayoutDashboard: <LayoutDashboard className="h-5 w-5 mr-3" />,
  ClipboardList: <ClipboardList className="h-5 w-5 mr-3" />,
  Users: <Users className="h-5 w-5 mr-3" />,
  Laptop: <Laptop className="h-5 w-5 mr-3" />,
  Package: <Package className="h-5 w-5 mr-3" />,
  Wrench: <Wrench className="h-5 w-5 mr-3" />,
  FileText: <FileText className="h-5 w-5 mr-3" />,
  ChartBarStacked: <ChartBarStacked className="h-5 w-5 mr-3" />,
  BarChart3: <BarChart3 className="h-5 w-5 mr-3" />,
  Settings: <Settings className="h-5 w-5 mr-3" />,
  CreditCard: <CreditCard className="h-5 w-5 mr-3" />,
  Calendar: <Calendar className="h-5 w-5 mr-3" />
};

export default function Sidebar({ className, open: externalOpen, setOpen: externalSetOpen }: SidebarProps) {
  const [location] = useLocation();
  const [internalOpen, setInternalOpen] = useState(false);
  
  // Use external state if provided, otherwise use internal state
  const mobileMenuOpen = externalOpen !== undefined ? externalOpen : internalOpen;
  const setMobileMenuOpen = externalSetOpen || setInternalOpen;

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const sidebar = (
    <div className={cn("bg-primary text-white flex-shrink-0 w-64 h-full", className)}>
      <div className="flex items-center justify-between h-16 border-b border-primary-dark px-4 md:px-0 md:justify-center">
        <h1 className="text-2xl font-bold font-sans text-white">TechSupport</h1>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden text-white"
          onClick={toggleMobileMenu}
        >
          <X className="h-6 w-6" />
        </Button>
      </div>

      <ScrollArea className="h-[calc(100vh-4rem)]">
        <div className="py-4">
          {NAVIGATION_ITEMS.map((section, i) => (
            <div key={i} className="mb-4">
              <div className="px-4 py-2">
                <p className="text-xs uppercase tracking-wider text-teal-200 font-semibold">
                  {section.title}
                </p>
              </div>
              {section.items.map((item, j) => {
                const isActive = location === item.href;
                return (
                  <Link
                    key={j}
                    href={item.href}
                    onClick={() => setMobileMenuOpen(false)}
                    className={cn(
                      "flex items-center px-4 py-3 text-teal-100 hover:bg-primary-dark transition-colors overflow-hidden",
                      isActive && "text-white bg-primary-dark"
                    )}
                  >
                    <span className="flex-shrink-0">{iconMap[item.icon]}</span>
                    <span className="truncate">{item.name}</span>
                  </Link>
                );
              })}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );

  // Mobile menu button (visible only on mobile)
  const mobileMenuButton = (
    <div className="md:hidden fixed bottom-6 right-6 z-50">
      <Button
        onClick={toggleMobileMenu}
        className="rounded-full p-3 bg-primary hover:bg-primary-dark shadow-lg"
        size="icon"
        aria-label="Menu principal"
      >
        <Menu className="h-6 w-6 text-white" />
      </Button>
    </div>
  );

  // Mobile sidebar overlay
  const mobileSidebar = (
    <div
      className={cn(
        "fixed inset-0 bg-gray-900 bg-opacity-50 z-40 md:hidden transition-opacity duration-300",
        mobileMenuOpen ? "opacity-100 visible" : "opacity-0 invisible pointer-events-none"
      )}
      onClick={() => setMobileMenuOpen(false)}
    >
      <div
        className={cn(
          "bg-primary text-white w-64 h-full overflow-y-auto transform transition-transform duration-300",
          mobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {sidebar}
      </div>
    </div>
  );

  return (
    <>
      {/* Desktop sidebar */}
      <div className="hidden md:block">{sidebar}</div>
      
      {/* Mobile components */}
      {mobileMenuButton}
      {mobileSidebar}
    </>
  );
}
