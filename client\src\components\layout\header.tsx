import { useState } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BellIcon, Menu, ChevronDown } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User } from "@/lib/types";

interface HeaderProps {
  title: string;
  onMenuButtonClick?: () => void;
}

export default function Header({ title, onMenuButtonClick }: HeaderProps) {
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  // Estado local removido, agora usamos somente o estado passado via props

  const { data: session } = useQuery<{ user: User; isAuthenticated: boolean }>({
    queryKey: ['/api/auth/session'],
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      await apiRequest('GET', '/api/auth/logout');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/auth/session'] });
      setLocation('/login');
    }
  });

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const user = session?.user;

  return (
    <header className="shadow-sm z-10 bg-primary md:bg-white">
      <div className="flex items-center justify-between h-16 px-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden text-white md:text-gray-500 hover:text-gray-200 md:hover:text-gray-700"
            onClick={onMenuButtonClick || (() => console.log('Menu button clicked'))}
            aria-label="Menu principal"
          >
            <Menu className="h-6 w-6" />
          </Button>
          <h2 className="text-lg font-medium text-white md:text-gray-800 truncate max-w-[220px] sm:max-w-sm md:max-w-md lg:max-w-full">{title}</h2>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="ghost" size="icon" className="text-white md:text-gray-500 hover:text-gray-200 md:hover:text-gray-700">
            <BellIcon className="h-6 w-6" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center space-x-2 hover:bg-primary-dark md:hover:bg-gray-100 rounded-full pr-2"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src="" alt={user?.name || "User"} />
                  <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium text-white md:text-gray-700 hidden sm:inline-block max-w-[120px] truncate">
                  {user?.name || "User"}
                </span>
                <ChevronDown className="h-5 w-5 text-white md:text-gray-400" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel className="truncate">{user?.name || "Minha Conta"}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="truncate" onClick={() => setLocation('/settings')}>
                Configurações
              </DropdownMenuItem>
              <DropdownMenuItem className="truncate" onClick={handleLogout}>
                Sair
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
