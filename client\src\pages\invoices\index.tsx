
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Link } from "wouter";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Clock, AlertTriangle, CheckCircle } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

import type { Invoice } from "@shared/schema";

const invoiceStatusMap = {
  draft: { label: "<PERSON>s<PERSON><PERSON><PERSON>", variant: "outline", color: "bg-gray-100 text-gray-700" },
  issued: { label: "Emitida", variant: "secondary", color: "bg-blue-100 text-blue-700" },
  sent: { label: "Enviada", variant: "default", color: "bg-yellow-100 text-yellow-700" },
  paid: { label: "Paga", variant: "success", color: "bg-green-100 text-green-700" },
  overdue: { label: "Vencida", variant: "destructive", color: "bg-red-100 text-red-700" },
  cancelled: { label: "Cancelada", variant: "destructive", color: "bg-red-100 text-red-700" },
};

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value / 100);
};

const formatDate = (date: string | Date | null) => {
  if (!date) return "-";
  return format(new Date(date), "dd/MM/yyyy", { locale: ptBR });
};

export default function InvoicesPage() {
  const [activeTab, setActiveTab] = useState("all");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const { data: invoices, isLoading } = useQuery({
    queryKey: ["/api/invoices"],
    select: (data: Invoice[]) => {
      return data.sort((a, b) => {
        return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
      });
    },
  });

  const { data: overdueInvoices, isLoading: isLoadingOverdue } = useQuery({
    queryKey: ["/api/invoices/overdue"],
    enabled: activeTab === "overdue",
    select: (data: Invoice[]) => data || []
  });

  const { data: unpaidInvoices, isLoading: isLoadingUnpaid } = useQuery({
    queryKey: ["/api/invoices/unpaid"],
    enabled: activeTab === "unpaid",
    select: (data: Invoice[]) => data || []
  });

  const renderInvoiceList = (invoiceList: Invoice[] | undefined, isLoadingList: boolean) => {
    if (isLoadingList) {
      return (
        <TableRow>
          <TableCell colSpan={6} className="text-center py-12">
            <div className="flex items-center justify-center">
              <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mr-3" />
              Carregando recibos...
            </div>
          </TableCell>
        </TableRow>
      );
    }

    if (!invoiceList?.length) {
      return (
        <TableRow>
          <TableCell colSpan={6} className="text-center py-12">
            <div className="text-gray-500">
              <p className="text-lg font-medium mb-2">Nenhum recibo encontrado</p>
              <p className="text-sm">Os recibos aparecerão aqui quando forem criados.</p>
            </div>
          </TableCell>
        </TableRow>
      );
    }

    return invoiceList.map((invoice) => (
      <TableRow key={invoice.id} className="hover:bg-gray-50">
        <TableCell>
          <Link to={`/invoices/${invoice.id}`} className="text-blue-600 hover:underline font-medium">
            {invoice.invoiceNumber}
          </Link>
        </TableCell>
        <TableCell className="hidden sm:table-cell text-gray-600">
          {formatDate(invoice.createdAt)}
        </TableCell>
        <TableCell className="hidden md:table-cell text-gray-600">
          {formatDate(invoice.dueDate)}
        </TableCell>
        <TableCell className="text-right font-medium">
          {formatCurrency(invoice.totalAmount || 0)}
        </TableCell>
        <TableCell className="hidden lg:table-cell text-right text-gray-600">
          {formatCurrency(invoice.paidAmount || 0)}
        </TableCell>
        <TableCell>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            invoiceStatusMap[invoice.status as keyof typeof invoiceStatusMap]?.color || "bg-gray-100 text-gray-700"
          }`}>
            {invoiceStatusMap[invoice.status as keyof typeof invoiceStatusMap]?.label || invoice.status}
          </span>
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header
          title="Recibos"
          onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />
        
        <main className="flex-1 overflow-y-auto bg-gray-100">
          <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-7xl">
            {/* Page Header */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Recibos</h1>
              <p className="text-sm text-gray-600">Visualize, crie e gerencie recibos para seus clientes</p>
            </div>

            {/* Content Container */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              {/* Action Header */}
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 pb-4 border-b border-gray-100">
                <div className="min-w-0 flex-1">
                  <h2 className="text-lg font-medium text-gray-800">Lista de Recibos</h2>
                </div>
                <div className="flex-shrink-0">
                  <Button asChild className="w-full sm:w-auto gap-2">
                    <Link to="/invoices/new">
                      <Plus className="w-4 h-4" />
                      Novo Recibo
                    </Link>
                  </Button>
                </div>
              </div>
              <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-6 bg-gray-50 p-1">
                  <TabsTrigger value="all" className="gap-2 data-[state=active]:bg-white">
                    <CheckCircle className="w-4 h-4" />
                    Todas
                  </TabsTrigger>
                  <TabsTrigger value="unpaid" className="gap-2 data-[state=active]:bg-white">
                    <Clock className="w-4 h-4" />
                    Pendentes
                  </TabsTrigger>
                  <TabsTrigger value="overdue" className="gap-2 data-[state=active]:bg-white">
                    <AlertTriangle className="w-4 h-4" />
                    Vencidas
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="all">
                  <div className="rounded-md border overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[140px]">Número</TableHead>
                          <TableHead className="hidden sm:table-cell">Data de Emissão</TableHead>
                          <TableHead className="hidden md:table-cell">Vencimento</TableHead>
                          <TableHead className="text-right">Valor Total</TableHead>
                          <TableHead className="hidden lg:table-cell text-right">Valor Pago</TableHead>
                          <TableHead className="w-[100px]">Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {renderInvoiceList(invoices, isLoading)}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
                
                <TabsContent value="unpaid">
                  <div className="rounded-md border overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[140px]">Número</TableHead>
                          <TableHead className="hidden sm:table-cell">Data de Emissão</TableHead>
                          <TableHead className="hidden md:table-cell">Vencimento</TableHead>
                          <TableHead className="text-right">Valor Total</TableHead>
                          <TableHead className="hidden lg:table-cell text-right">Valor Pago</TableHead>
                          <TableHead className="w-[100px]">Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {renderInvoiceList(unpaidInvoices as Invoice[], isLoadingUnpaid)}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
                
                <TabsContent value="overdue">
                  <div className="rounded-md border overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[140px]">Número</TableHead>
                          <TableHead className="hidden sm:table-cell">Data de Emissão</TableHead>
                          <TableHead className="hidden md:table-cell">Vencimento</TableHead>
                          <TableHead className="text-right">Valor Total</TableHead>
                          <TableHead className="hidden lg:table-cell text-right">Valor Pago</TableHead>
                          <TableHead className="w-[100px]">Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {renderInvoiceList(overdueInvoices as Invoice[], isLoadingOverdue)}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
