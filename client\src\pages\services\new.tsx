import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useParams } from 'wouter';
import { ChevronLeft, Save } from 'lucide-react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { apiRequest } from '@/lib/queryClient';
import { toast } from '@/hooks/use-toast';
import Sidebar from '@/components/layout/sidebar';
import Header from '@/components/layout/header';

// Interface para representar um serviço
interface Service {
  id: number;
  name: string;
  description: string | null;
  price: number;
  active: boolean;
}

// Schema de validação para o formulário
const serviceFormSchema = z.object({
  name: z.string().min(1, { message: 'Nome do serviço é obrigatório' }),
  description: z.string().nullable().optional(),
  price: z.coerce.number().min(0, { message: 'Preço deve ser maior ou igual a zero' }),
  active: z.boolean().default(true),
});

type ServiceFormValues = z.infer<typeof serviceFormSchema>;

interface ServiceFormProps {
  isEditing?: boolean;
}

export default function ServiceForm({ isEditing = false }: ServiceFormProps) {
  const [, navigate] = useLocation();
  const params = useParams();
  const queryClient = useQueryClient();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  // Obtém o valor do parâmetro returnTo da URL
  const searchParams = new URLSearchParams(window.location.search);
  const returnTo = searchParams.get('returnTo') || '/services';

  // Configuração do formulário
  const form = useForm<ServiceFormValues>({
    resolver: zodResolver(serviceFormSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      active: true,
    },
  });

  // Busca detalhes do serviço para edição
  const { data: service, isLoading } = useQuery<Service>({
    queryKey: [`/api/services/${params.id}`],
    enabled: isEditing && !!params.id,
    refetchOnWindowFocus: false,
  });

  // Popula o formulário quando os dados são carregados
  useEffect(() => {
    if (service) {
      form.reset({
        name: service.name,
        description: service.description || '',
        price: service.price / 100, // Convertendo de centavos para reais no formulário
        active: service.active,
      });
    }
  }, [service, form]);

  // Mutação para criar serviço
  const createServiceMutation = useMutation({
    mutationFn: async (data: ServiceFormValues) => {
      const response = await apiRequest('POST', '/api/services', {
        ...data,
        price: Math.round(data.price * 100), // Convertendo para centavos
      });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/services'] });
      toast({
        title: 'Serviço criado',
        description: 'O serviço foi criado com sucesso.',
      });
      
      // Se temos um formulário salvo de ordem de serviço, mantemos os dados
      if (returnTo === '/service-orders/new') {
        // Não precisamos fazer nada especial, apenas redirecionar de volta
      }
      
      navigate(returnTo);
    },
    onError: (error) => {
      console.error('Erro ao criar serviço:', error);
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao criar o serviço.',
        variant: 'destructive',
      });
    },
  });

  // Mutação para atualizar serviço
  const updateServiceMutation = useMutation({
    mutationFn: async (data: ServiceFormValues) => {
      const response = await apiRequest('PATCH', `/api/services/${params.id}`, {
        ...data,
        price: Math.round(data.price * 100), // Convertendo para centavos
      });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/services'] });
      queryClient.invalidateQueries({ queryKey: [`/api/services/${params.id}`] });
      toast({
        title: 'Serviço atualizado',
        description: 'O serviço foi atualizado com sucesso.',
      });
      navigate(returnTo);
    },
    onError: (error) => {
      console.error('Erro ao atualizar serviço:', error);
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao atualizar o serviço.',
        variant: 'destructive',
      });
    },
  });

  // Função para lidar com o envio do formulário
  function onSubmit(data: ServiceFormValues) {
    if (isEditing) {
      updateServiceMutation.mutate(data);
    } else {
      createServiceMutation.mutate(data);
    }
  }

  const pageTitle = isEditing ? 'Editar Serviço' : 'Novo Serviço';

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />

      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title={pageTitle} onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />

        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="container mx-auto py-4 space-y-4">

            {isEditing && isLoading ? (
              <div className="text-center py-10">Carregando dados do serviço...</div>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>{isEditing ? 'Editar Serviço' : 'Adicionar Novo Serviço'}</CardTitle>
                  <CardDescription>
                    Preencha os campos abaixo para {isEditing ? 'atualizar' : 'cadastrar'} um serviço.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <div className="grid gap-6 md:grid-cols-2">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Nome do Serviço</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="Ex: Formatação de Computador" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="price"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Preço (R$)</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  min="0" 
                                  step="0.01"
                                  placeholder="0.00" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                Valor em reais (R$)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Descrição</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Descreva o serviço em detalhes (opcional)" 
                                className="min-h-[120px]"
                                {...field} 
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="active"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Serviço Ativo</FormLabel>
                              <FormDescription>
                                Marque esta opção se o serviço está disponível para uso.
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      
                      <div className="flex justify-end space-x-2">
                        <Button 
                          type="button" 
                          variant="outline"
                          onClick={() => navigate('/services')}
                        >
                          Cancelar
                        </Button>
                        <Button 
                          type="submit" 
                          disabled={createServiceMutation.isPending || updateServiceMutation.isPending}
                        >
                          <Save className="h-4 w-4 mr-2" />
                          {createServiceMutation.isPending || updateServiceMutation.isPending 
                            ? 'Salvando...' 
                            : 'Salvar Serviço'}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente wrapper para a página de novo serviço
export function NewServicePage() {
  return <ServiceForm isEditing={false} />;
}

// Componente wrapper para a página de edição de serviço
export function EditServicePage() {
  return <ServiceForm isEditing={true} />;
}