# Architecture Overview - Simplesmed TechServer

## Overview

Simplesmed TechServer is a full-stack web application designed for managing technical service operations, including service orders, inventory, client information, equipment management, and billing. The application follows a modern web architecture with a clear separation between frontend and backend components.

## System Architecture

The system employs a client-server architecture with the following key components:

- **Frontend**: React-based single-page application (SPA) using modern React patterns and UI libraries
- **Backend**: Node.js Express server providing RESTful API endpoints
- **Database**: PostgreSQL database accessed via Drizzle ORM
- **Authentication**: Session-based authentication using Passport.js

The system uses a monorepo structure, with clear separation between client, server, and shared code. This architecture enables code sharing while maintaining separation of concerns.

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│    Frontend     │────▶│     Backend     │────▶│    Database     │
│    (React)      │     │    (Express)    │     │  (PostgreSQL)   │
│                 │◀────│                 │◀────│                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Key Components

### Frontend Architecture

The frontend is built using React with the following key technologies:

- **UI Framework**: Custom components built on top of Radix UI primitives with Tailwind CSS
- **State Management**: React Query for server state and React context for UI state
- **Routing**: Wouter for lightweight client-side routing
- **Forms**: React Hook Form with Zod schema validation
- **Styling**: Tailwind CSS with component variants via class-variance-authority

The frontend is organized by features, with each major section of the application having its own directory in the pages folder. Reusable UI components are kept in a separate components directory.

### Backend Architecture

The backend is built with Express.js and follows these architectural patterns:

- **API Design**: RESTful API endpoints organized by resource
- **Database Access**: Drizzle ORM with PostgreSQL
- **Authentication**: Session-based authentication with Passport.js
- **Middleware**: Express middleware for request processing
- **Error Handling**: Centralized error handling with consistent response format

The backend routes are modular and organized by resource type, with storage abstraction to separate business logic from data access.

### Database Design

The application uses PostgreSQL with Drizzle ORM. The database schema includes:

- **Users and Roles**: Authentication and role-based access control
- **Clients**: Customer information management
- **Equipment**: Tracking customer equipment and devices
- **Service Orders**: Managing repair tickets and work orders
- **Inventory**: Tracking parts and supplies
- **Invoicing**: Billing and payment processing

The schema uses enums for statuses and roles, ensuring consistent state management across the application.

### Shared Code

Code that is used by both frontend and backend is stored in a shared directory, including:

- **Database Schemas**: Drizzle ORM schema definitions
- **Type Definitions**: TypeScript interfaces and types
- **Validation Schemas**: Zod schemas for data validation

This approach ensures type safety and consistency between client and server code.

## Data Flow

1. **Authentication Flow**:
   - User credentials are submitted to the backend
   - Server authenticates and creates a session
   - Session ID is stored in a cookie
   - Subsequent requests include the session cookie for authentication

2. **Service Order Flow**:
   - Client information is entered or selected from existing records
   - Equipment details are added to the service order
   - Service order is created with initial status
   - Technicians can update the status as work progresses
   - Parts and labor are added to the service order
   - Invoices are generated from completed service orders
   - Payments are recorded against invoices

3. **Inventory Management Flow**:
   - Inventory items are added to the system with quantity and pricing
   - Items are allocated to service orders as needed
   - Inventory is automatically adjusted when parts are used
   - Low stock alerts are generated for items below minimum quantities

## External Dependencies

The application integrates with the following external services:

1. **WhatsApp Business API**:
   - Used for client notifications
   - Status updates for service orders
   - Appointment reminders

2. **Payment Processing**:
   - Stripe integration for payment processing
   - Support for multiple payment methods

## Deployment Strategy

The application is configured for deployment on Replit, with the following setup:

- **Build Process**: Vite for frontend bundling, esbuild for backend transpilation
- **Database**: PostgreSQL database (likely provisioned by Replit)
- **Environment Variables**: Configuration through environment variables
- **Static Assets**: Static files served by Express in production

The deployment workflow includes:
1. Frontend build with Vite
2. Backend transpilation with esbuild
3. Combined deployment with static assets served from the dist directory
4. Database provisioning and migration

## Development Environment

The development environment is configured with:

- **TypeScript**: For type safety across the codebase
- **Vite**: For fast development server and hot module replacement
- **Drizzle Kit**: For database migrations and schema management
- **ESLint and Prettier**: For code linting and formatting

## Extensibility Considerations

The application is designed for extensibility through:

1. **Modular Architecture**: Clear separation of concerns allows for adding new features without modifying existing code
2. **Plugin System**: The WhatsApp integration shows a pattern for adding additional communication channels
3. **API-First Design**: Backend services are exposed through a well-defined API that can be consumed by additional clients

## Security Considerations

Security measures include:

1. **Authentication**: Session-based authentication with Passport.js
2. **Authorization**: Role-based access control
3. **Input Validation**: Zod schemas for validating input data
4. **HTTPS**: Support for secure communications
5. **Environment Variables**: Sensitive configuration stored in environment variables