# Simplesmed TechServer

Full-stack web application to manage technical service operations – service orders, inventory, clients, equipment and billing. Built with React + Vite (frontend), Node/Express (backend) and PostgreSQL via Drizzle ORM.

## Table of contents
1. Features
2. Architecture
3. Tech Stack
4. Project Structure
5. Getting Started
6. <PERSON>ripts
7. Environment Variables
8. Database Migrations
9. Deployment
10. Contributing & License

---

## 1. Features
* Service order lifecycle management (open → diagnose → repair → invoice → close)
* Client & equipment registry
* Inventory and low-stock alerts
* Invoicing & Stripe payments
* WhatsApp Business notifications
* Role-based access control & session authentication

## 2. Architecture
```
┌────────────┐      ┌────────────┐      ┌────────────┐
│  React SPA │ ───► │  Express   │ ───► │ PostgreSQL │
│  (client)  │ ◄─── │  (server)  │ ◄─── │   DB       │
└────────────┘      └────────────┘      └────────────┘
```
* **Client** – React/TypeScript SPA served by Vite during dev and as static files in prod.
* **Server** – Node 20, Express, RESTful JSON API, Passport.js sessions.
* **DB** – PostgreSQL; schema & migrations managed with Drizzle Kit.
* **Shared** – Type definitions & Zod schemas live in `shared/` and are consumed by both layers.

## 3. Tech Stack
| Layer       | Main libs / tools                          |
|-------------|--------------------------------------------|
| Frontend    | React, React Query, Wouter, Tailwind CSS, Radix UI, class-variance-authority, React Hook Form, Zod |
| Backend     | Express, Drizzle ORM, Passport.js, Stripe SDK, WhatsApp API |
| Tooling     | TypeScript, Vite, esbuild, ESLint & Prettier |
| DevOps      | PNPM, Replit/Nix, Node 20                  |

## 4. Project Structure
```
TechSupportManager/
├── client/            # React SPA
│   ├── index.html
│   └── src/
│       ├── App.tsx
│       ├── pages/    # feature-based route folders
│       ├── components/
│       └── ...
├── server/            # Node/Express API
│   ├── index.ts       # entrypoint
│   ├── routes.ts      # route registration
│   ├── services/      # business logic
│   └── db.ts          # db pool & Drizzle
├── shared/            # common types/schemas (planned)
├── replit_agent/      # architecture docs & Replit configs
└── README.md
```

## 5. Getting Started
### Prerequisites
* Node 20+
* PNPM 8+
* PostgreSQL 15+

### Installation
```bash
pnpm install
```

### Development
Run both client and server using concurrent dev scripts:
```bash
pnpm dev
```
* Client available at `http://localhost:5173`
* Server API at `http://localhost:3000/api`

## 6. Scripts (package.json)
| Command     | Description                  |
|-------------|------------------------------|
| `pnpm dev`  | Run client & server in watch |
| `pnpm build`| Build client and transpile server |
| `pnpm start`| Start server in production   |
| `pnpm lint` | Lint code with ESLint        |
| `pnpm db:migrate` | Run Drizzle migrations |

## 7. Environment Variables
| Variable               | Example / Note                      |
|------------------------|-------------------------------------|
| `DATABASE_URL`         | `postgres://user:pass@localhost/db` |
| `SESSION_SECRET`       | Random string                       |
| `WHATSAPP_TOKEN`       | WhatsApp Business API token         |
| `STRIPE_SECRET_KEY`    | Stripe secret                       |

Create a `.env` file in the project root and populate the variables.

## 8. Database Migrations
```bash
pnpm db:generate   # generate SQL from schema
pnpm db:migrate    # apply migrations
```

## 9. Deployment (Replit)
1. The Replit Nix environment installs Node, PNPM, and PostgreSQL.
2. `pnpm build` runs Vite (client) and esbuild (server).
3. Express serves static client files from `client/dist`.
4. Expose port `3000`.

## 10. Contributing & License
PRs are welcome! Please run `pnpm lint` and include tests where possible.

Licensed under the MIT License.
