import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useLocation } from 'wouter';
import { InventoryCategory } from '@/lib/types';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import Sidebar from '@/components/layout/sidebar';
import Header from '@/components/layout/header';

// Schema para validação do formulário
const partFormSchema = z.object({
  name: z.string().min(2, { message: 'O nome deve ter pelo menos 2 caracteres' }),
  brand: z.string().optional(),
  description: z.string().optional(),
  barcode: z.string().optional(),
  internalCode: z.string().optional(),
  sku: z.string().optional(),
  categoryId: z.union([z.string(), z.undefined()]).optional(),
  purchaseValue: z.coerce.number().min(0, { message: 'O valor de compra deve ser positivo' }),
  saleValue: z.coerce.number().min(0, { message: 'O valor de venda deve ser positivo' }),
  minQuantity: z.coerce.number().min(0, { message: 'A quantidade mínima deve ser positiva' }).default(0),
  active: z.boolean().default(true),
});

type PartFormValues = z.infer<typeof partFormSchema>;

export default function NewPartPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [location, navigate] = useLocation();
  
  // Buscar categorias do inventário
  const { data: categories = [] } = useQuery<InventoryCategory[]>({
    queryKey: ['/api/inventory-categories'],
  });
  
  // Extrair o parâmetro returnTo da URL
  const urlParams = new URLSearchParams(typeof window !== 'undefined' ? window.location.search : '');
  const returnTo = urlParams.get('returnTo');

  // Inicializar o formulário
  const form = useForm<PartFormValues>({
    resolver: zodResolver(partFormSchema),
    defaultValues: {
      name: '',
      brand: '',
      description: '',
      barcode: '',
      internalCode: '',
      sku: '',
      categoryId: '',
      purchaseValue: 0,
      saleValue: 0,
      minQuantity: 0,
      active: true,
    },
  });

  // Mutação para criar nova peça
  const createPartMutation = useMutation({
    mutationFn: (data: PartFormValues) => {
      // Converter valores monetários para centavos
      const transformedData = {
        ...data,
        purchaseValue: Math.round(data.purchaseValue * 100), // Converte para centavos
        saleValue: Math.round(data.saleValue * 100), // Converte para centavos
        categoryId: data.categoryId ? (data.categoryId === '0' ? null : parseInt(data.categoryId, 10) || null) : null,
        minQuantity: data.minQuantity || 0,
      };
      
      return apiRequest('POST', '/api/parts', transformedData);
    },
    onSuccess: () => {
      toast({
        title: 'Peça adicionada',
        description: 'A peça foi adicionada com sucesso.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/parts'] });
      
      // Se temos um formulário salvo de ordem de serviço, mantemos os dados
      if (returnTo === '/service-orders/new') {
        // Não precisamos fazer nada especial, apenas redirecionar de volta
      }
      
      // Se tiver parâmetro returnTo, navega para a rota especificada
      if (returnTo) {
        navigate(returnTo);
      } else {
        navigate('/parts');
      }
    },
    onError: (error) => {
      toast({
        title: 'Erro ao adicionar peça',
        description: 'Ocorreu um erro ao adicionar a peça. Tente novamente.',
        variant: 'destructive',
      });
    },
  });

  // Manipulador de envio do formulário
  function onSubmit(data: PartFormValues) {
    createPartMutation.mutate(data);
  }

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Nova Peça" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-medium text-gray-800">Cadastrar Nova Peça</h3>
                <p className="text-sm text-gray-500">Preencha os dados da peça</p>
              </div>
            </div>
            
            <div className="p-6">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome da Peça*</FormLabel>
                          <FormControl>
                            <Input placeholder="Digite o nome da peça" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
    
                    <FormField
                      control={form.control}
                      name="brand"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Marca</FormLabel>
                          <FormControl>
                            <Input placeholder="Digite a marca (opcional)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
    
                    <FormField
                      control={form.control}
                      name="barcode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Código de Barras</FormLabel>
                          <FormControl>
                            <Input placeholder="Digite o código de barras (opcional)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
    
                    <FormField
                      control={form.control}
                      name="internalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Código Interno</FormLabel>
                          <FormControl>
                            <Input placeholder="Digite o código interno (opcional)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="sku"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SKU</FormLabel>
                          <FormControl>
                            <Input placeholder="Digite o código SKU (opcional)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="categoryId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Categoria</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione uma categoria" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="0">Sem categoria</SelectItem>
                              {categories.map((category) => (
                                <SelectItem key={category.id} value={category.id.toString()}>
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
    
                    <FormField
                      control={form.control}
                      name="purchaseValue"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor de Compra (R$)*</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.01" 
                              min="0"
                              placeholder="0.00" 
                              value={field.value || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                field.onChange(value === '' ? 0 : parseFloat(value));
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
    
                    <FormField
                      control={form.control}
                      name="saleValue"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor de Venda (R$)*</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.01" 
                              min="0"
                              placeholder="0.00" 
                              value={field.value || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                field.onChange(value === '' ? 0 : parseFloat(value));
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="minQuantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Quantidade Mínima</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0"
                              placeholder="0" 
                              value={field.value || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                field.onChange(value === '' ? 0 : parseInt(value));
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            Quantidade mínima em estoque antes de gerar alerta
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
    
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Descrição</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Digite uma descrição detalhada da peça (opcional)"
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
    
                  <FormField
                    control={form.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Ativo</FormLabel>
                          <FormDescription>
                            Marque esta opção para que a peça apareça como disponível no sistema.
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
    
                  <div className="flex justify-end space-x-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => returnTo ? navigate(returnTo) : navigate('/parts')}
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      disabled={createPartMutation.isPending}
                    >
                      {createPartMutation.isPending ? 'Salvando...' : 'Salvar Peça'}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}