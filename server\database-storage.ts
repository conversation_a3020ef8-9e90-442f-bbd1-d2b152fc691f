import { 
  users, type User, type InsertUser,
  clients, type Client, type InsertClient,
  equipmentCategories, type EquipmentCategory, type InsertEquipmentCategory,
  equipment, type Equipment, type InsertEquipment,
  technicians, type Technician, type InsertTechnician,
  inventoryCategories, type InventoryCategory, type InsertInventoryCategory,
  inventoryItems, type InventoryItem, type InsertInventoryItem,
  serviceOrders, type ServiceOrder, type InsertServiceOrder,
  serviceOrderItems, type ServiceOrderItem, type InsertServiceOrderItem,
  quotes, type Quote, type InsertQuote,
  technicianSchedules, type TechnicianSchedule, type InsertTechnicianSchedule,
  // Appointments
  appointments, type Appointment, type InsertAppointment,
  // Services
  services, type Service, type InsertService,
  // Parts
  parts, type Part, type InsertPart,
  // Financial module
  paymentMethods, type PaymentMethod, type InsertPaymentMethod,
  payments, type Payment, type InsertPayment,
  invoices, type Invoice, type InsertInvoice,
  invoiceItems, type InvoiceItem, type InsertInvoiceItem
} from "@shared/schema";
import { IStorage } from "./storage";
import { db } from "./db";
import { and, eq, lte, desc, sql, gt, gte } from "drizzle-orm";

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values({
        ...insertUser,
        createdAt: new Date()
      })
      .returning();
    return user;
  }

  async updateUser(id: number, data: Partial<User>): Promise<User | undefined> {
    const [updatedUser] = await db
      .update(users)
      .set(data)
      .where(eq(users.id, id))
      .returning();
    return updatedUser || undefined;
  }

  async getUsers(): Promise<User[]> {
    return db.select().from(users);
  }

  // Client operations
  async getClient(id: number): Promise<Client | undefined> {
    const [client] = await db.select().from(clients).where(eq(clients.id, id));
    return client || undefined;
  }

  async getClients(): Promise<Client[]> {
    return db.select().from(clients);
  }

  async createClient(insertClient: InsertClient): Promise<Client> {
    const [client] = await db
      .insert(clients)
      .values({
        ...insertClient,
        createdAt: new Date()
      })
      .returning();
    return client;
  }

  async updateClient(id: number, data: Partial<Client>): Promise<Client | undefined> {
    const [updatedClient] = await db
      .update(clients)
      .set(data)
      .where(eq(clients.id, id))
      .returning();
    return updatedClient || undefined;
  }

  async deleteClient(id: number): Promise<boolean> {
    await db
      .delete(clients)
      .where(eq(clients.id, id));

    // Se após a exclusão, não for possível encontrar o cliente, a exclusão foi bem-sucedida
    const client = await this.getClient(id);
    return client === undefined;
  }

  // Equipment Category operations
  async getEquipmentCategory(id: number): Promise<EquipmentCategory | undefined> {
    const [category] = await db.select().from(equipmentCategories).where(eq(equipmentCategories.id, id));
    return category || undefined;
  }

  async getEquipmentCategories(): Promise<EquipmentCategory[]> {
    return db.select().from(equipmentCategories);
  }

  async createEquipmentCategory(category: InsertEquipmentCategory): Promise<EquipmentCategory> {
    const [newCategory] = await db
      .insert(equipmentCategories)
      .values(category)
      .returning();
    return newCategory;
  }

  // Equipment operations
  async getEquipment(id: number): Promise<Equipment | undefined> {
    const [item] = await db.select().from(equipment).where(eq(equipment.id, id));
    return item || undefined;
  }

  async getEquipmentByClient(clientId: number): Promise<Equipment[]> {
    return db.select().from(equipment).where(eq(equipment.clientId, clientId));
  }

  async getEquipments(): Promise<Equipment[]> {
    return db.select().from(equipment);
  }

  async createEquipment(insertEquipment: InsertEquipment): Promise<Equipment> {
    const [newEquipment] = await db
      .insert(equipment)
      .values({
        ...insertEquipment,
        createdAt: new Date()
      })
      .returning();
    return newEquipment;
  }

  async updateEquipment(id: number, data: Partial<Equipment>): Promise<Equipment | undefined> {
    const [updatedEquipment] = await db
      .update(equipment)
      .set(data)
      .where(eq(equipment.id, id))
      .returning();
    return updatedEquipment || undefined;
  }

  // Technician operations
  async getTechnician(id: number): Promise<Technician | undefined> {
    const [technician] = await db.select().from(technicians).where(eq(technicians.id, id));
    return technician || undefined;
  }

  async getTechnicianByUserId(userId: number): Promise<Technician | undefined> {
    const [technician] = await db.select().from(technicians).where(eq(technicians.userId, userId));
    return technician || undefined;
  }

  async getTechnicians(): Promise<Technician[]> {
    return db.select().from(technicians);
  }

  async createTechnician(insertTechnician: InsertTechnician): Promise<Technician> {
    const [technician] = await db
      .insert(technicians)
      .values(insertTechnician)
      .returning();
    return technician;
  }

  async updateTechnician(id: number, data: Partial<Technician>): Promise<Technician | undefined> {
    const [updatedTechnician] = await db
      .update(technicians)
      .set(data)
      .where(eq(technicians.id, id))
      .returning();
    return updatedTechnician || undefined;
  }

  // Inventory Category operations
  async getInventoryCategory(id: number): Promise<InventoryCategory | undefined> {
    const [category] = await db.select().from(inventoryCategories).where(eq(inventoryCategories.id, id));
    return category || undefined;
  }

  async getInventoryCategories(): Promise<InventoryCategory[]> {
    return db.select().from(inventoryCategories);
  }

  async createInventoryCategory(category: InsertInventoryCategory): Promise<InventoryCategory> {
    const [newCategory] = await db
      .insert(inventoryCategories)
      .values(category)
      .returning();
    return newCategory;
  }

  // Inventory Item operations
  async getInventoryItem(id: number): Promise<InventoryItem | undefined> {
    const [item] = await db.select().from(inventoryItems).where(eq(inventoryItems.id, id));
    return item || undefined;
  }

  async getInventoryItems(): Promise<InventoryItem[]> {
    return db.select().from(inventoryItems);
  }

  async getInventoryItemsByCategory(categoryId: number): Promise<InventoryItem[]> {
    return db.select().from(inventoryItems).where(eq(inventoryItems.categoryId, categoryId));
  }

  async createInventoryItem(insertItem: InsertInventoryItem): Promise<InventoryItem> {
    const [item] = await db
      .insert(inventoryItems)
      .values({
        ...insertItem,
        createdAt: new Date()
      })
      .returning();
    return item;
  }

  async updateInventoryItem(id: number, data: Partial<InventoryItem>): Promise<InventoryItem | undefined> {
    const [updatedItem] = await db
      .update(inventoryItems)
      .set(data)
      .where(eq(inventoryItems.id, id))
      .returning();
    return updatedItem || undefined;
  }

  async adjustInventoryQuantity(id: number, quantityChange: number): Promise<InventoryItem | undefined> {
    const item = await this.getInventoryItem(id);
    if (!item || item.quantity === null) return undefined;

    const newQuantity = item.quantity + quantityChange;
    if (newQuantity < 0) return undefined; // Can't have negative inventory

    const [updatedItem] = await db
      .update(inventoryItems)
      .set({ quantity: newQuantity })
      .where(eq(inventoryItems.id, id))
      .returning();

    return updatedItem || undefined;
  }

  async getLowStockItems(): Promise<InventoryItem[]> {
    return db.select()
      .from(inventoryItems)
      .where(
        sql`${inventoryItems.quantity} <= ${inventoryItems.minQuantity} AND ${inventoryItems.quantity} IS NOT NULL AND ${inventoryItems.minQuantity} IS NOT NULL`
      );
  }

  // Service Order operations
  async getServiceOrder(id: number): Promise<ServiceOrder | undefined> {
    const [order] = await db.select().from(serviceOrders).where(eq(serviceOrders.id, id));
    return order || undefined;
  }

  async getServiceOrderByNumber(orderNumber: string): Promise<ServiceOrder | undefined> {
    const [order] = await db.select().from(serviceOrders).where(eq(serviceOrders.orderNumber, orderNumber));
    return order || undefined;
  }

  async getServiceOrders(): Promise<ServiceOrder[]> {
    return db.select().from(serviceOrders).orderBy(desc(serviceOrders.createdAt));
  }

  async getServiceOrdersByClient(clientId: number): Promise<ServiceOrder[]> {
    return db.select().from(serviceOrders).where(eq(serviceOrders.clientId, clientId));
  }

  async getServiceOrdersByTechnician(technicianId: number): Promise<ServiceOrder[]> {
    return db.select().from(serviceOrders).where(eq(serviceOrders.technicianId, technicianId));
  }

  async getServiceOrdersByStatus(status: string): Promise<ServiceOrder[]> {
    // Use cast para garantir compatibilidade de tipos
    return db.select().from(serviceOrders).where(sql`${serviceOrders.status} = ${status}`);
  }

  async createServiceOrder(insertOrder: InsertServiceOrder): Promise<ServiceOrder> {
    // Generate order number (prefix + sequential number)
    const result = await db.select({ count: sql<number>`count(*)` }).from(serviceOrders);
    const count = result[0]?.count || 0;
    const orderNumber = `OS-${(count + 1).toString().padStart(6, '0')}`;

    // Preparar os dados da ordem sem a coluna totalAmount
    const orderData = {
      ...insertOrder,
      orderNumber,
      createdAt: new Date(),
      updatedAt: new Date(),
      completedAt: null
    };

    const [order] = await db
      .insert(serviceOrders)
      .values(orderData)
      .returning();
    return order;
  }

  async updateServiceOrder(id: number, data: Partial<ServiceOrder>): Promise<ServiceOrder | undefined> {
    const [updatedOrder] = await db
      .update(serviceOrders)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(serviceOrders.id, id))
      .returning();
    return updatedOrder || undefined;
  }

  async updateServiceOrderStatus(id: number, status: string): Promise<ServiceOrder | undefined> {
    const order = await this.getServiceOrder(id);
    if (!order) return undefined;

    let completedAt = order.completedAt;
    if (status === 'completed' && !completedAt) {
      completedAt = new Date();
    }

    const [updatedOrder] = await db
      .update(serviceOrders)
      .set({
        status: status as any, // Type assertion to handle string input
        updatedAt: new Date(),
        completedAt
      })
      .where(eq(serviceOrders.id, id))
      .returning();
    return updatedOrder || undefined;
  }

  async deleteServiceOrder(id: number): Promise<boolean> {
    try {
      console.log(`Iniciando remoção da ordem de serviço ID: ${id}`);

      // 1. Verificar se a ordem existe
      const order = await this.getServiceOrder(id);
      if (!order) {
        console.log(`Ordem de serviço ID ${id} não encontrada`);
        return true; // Consideramos uma exclusão bem-sucedida se a ordem não existe
      }

      // 2. Remover todos os itens da ordem de serviço
      const items = await this.getServiceOrderItems(id);
      console.log(`Encontrados ${items.length} itens para remover`);

      if (items.length > 0) {
        // Remover todos os itens de uma vez
        await db.delete(serviceOrderItems).where(eq(serviceOrderItems.serviceOrderId, id));
        console.log(`Itens da ordem de serviço removidos`);
      }

      // 3. Remover qualquer orçamento associado a esta ordem
      await db.delete(quotes).where(eq(quotes.serviceOrderId, id));

      // 4. Remover quaisquer faturas associadas
      await db.delete(invoices).where(eq(invoices.serviceOrderId, id));

      // 5. Remover quaisquer pagamentos associados
      await db.delete(payments).where(eq(payments.serviceOrderId, id));

      // 6. Remover quaisquer agendamentos de técnicos associados
      await db.delete(technicianSchedules).where(eq(technicianSchedules.serviceOrderId, id));

      // 7. Finally, excluir a ordem de serviço
      console.log(`Removendo a ordem de serviço ID: ${id}`);
      await db.delete(serviceOrders).where(eq(serviceOrders.id, id));

      // 8. Verificar se a exclusão foi bem-sucedida
      const orderAfterDelete = await this.getServiceOrder(id);
      const wasDeleted = orderAfterDelete === undefined;
      console.log(`Verificação pós-exclusão: ordem ${wasDeleted ? 'foi excluída' : 'ainda existe'}`);

      if (!wasDeleted) {
        console.error(`Falha ao excluir a ordem de serviço ID: ${id}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error("Erro ao excluir ordem de serviço:", error);
      return false;
    }
  }

  // Service Order Item operations
  async getServiceOrderItem(id: number): Promise<ServiceOrderItem | undefined> {
    const [item] = await db.select().from(serviceOrderItems).where(eq(serviceOrderItems.id, id));
    return item || undefined;
  }

  async getServiceOrderItems(serviceOrderId: number): Promise<ServiceOrderItem[]> {
    return db.select().from(serviceOrderItems).where(eq(serviceOrderItems.serviceOrderId, serviceOrderId));
  }

  async createServiceOrderItem(insertItem: InsertServiceOrderItem): Promise<ServiceOrderItem> {
    const [item] = await db
      .insert(serviceOrderItems)
      .values(insertItem)
      .returning();
    return item;
  }

  async updateServiceOrderItem(id: number, data: Partial<ServiceOrderItem>): Promise<ServiceOrderItem | undefined> {
    const [updatedItem] = await db
      .update(serviceOrderItems)
      .set(data)
      .where(eq(serviceOrderItems.id, id))
      .returning();
    return updatedItem || undefined;
  }

  async removeServiceOrderItem(id: number): Promise<boolean> {
    await db
      .delete(serviceOrderItems)
      .where(eq(serviceOrderItems.id, id));

    // Se após a exclusão, não for possível encontrar o item, a exclusão foi bem-sucedida
    const item = await this.getServiceOrderItem(id);
    return item === undefined;
  }

  // Quote operations
  async getQuote(id: number): Promise<Quote | undefined> {
    const [quote] = await db.select().from(quotes).where(eq(quotes.id, id));
    return quote || undefined;
  }

  async getQuoteByNumber(quoteNumber: string): Promise<Quote | undefined> {
    const [quote] = await db.select().from(quotes).where(eq(quotes.quoteNumber, quoteNumber));
    return quote || undefined;
  }

  async getQuotes(): Promise<Quote[]> {
    return db.select().from(quotes);
  }

  async getQuotesByClient(clientId: number): Promise<Quote[]> {
    return db.select().from(quotes).where(eq(quotes.clientId, clientId));
  }

  async getQuotesByServiceOrder(serviceOrderId: number): Promise<Quote[]> {
    return db.select().from(quotes).where(eq(quotes.serviceOrderId, serviceOrderId));
  }

  async createQuote(insertQuote: InsertQuote): Promise<Quote> {
    // Generate quote number (prefix + sequential number)
    const result = await db.select({ count: sql<number>`count(*)` }).from(quotes);
    const count = result[0]?.count || 0;
    const quoteNumber = `ORC-${(count + 1).toString().padStart(6, '0')}`;

    const [quote] = await db
      .insert(quotes)
      .values({
        ...insertQuote,
        quoteNumber,
        createdAt: new Date(),
        approvedAt: null
      })
      .returning();
    return quote;
  }

  async updateQuote(id: number, data: Partial<Quote>): Promise<Quote | undefined> {
    const [updatedQuote] = await db
      .update(quotes)
      .set(data)
      .where(eq(quotes.id, id))
      .returning();
    return updatedQuote || undefined;
  }

  async approveQuote(id: number): Promise<Quote | undefined> {
    const [approvedQuote] = await db
      .update(quotes)
      .set({
        status: "approved",
        approvedAt: new Date()
      })
      .where(eq(quotes.id, id))
      .returning();
    return approvedQuote || undefined;
  }

  async rejectQuote(id: number): Promise<Quote | undefined> {
    const [rejectedQuote] = await db
      .update(quotes)
      .set({
        status: "rejected"
      })
      .where(eq(quotes.id, id))
      .returning();
    return rejectedQuote || undefined;
  }

  // Technician Schedule operations
  async getTechnicianSchedule(id: number): Promise<TechnicianSchedule | undefined> {
    const [schedule] = await db.select().from(technicianSchedules).where(eq(technicianSchedules.id, id));
    return schedule || undefined;
  }

  async getTechnicianSchedules(): Promise<TechnicianSchedule[]> {
    return db.select().from(technicianSchedules);
  }

  async getTechnicianSchedulesByTechnician(technicianId: number): Promise<TechnicianSchedule[]> {
    return db.select().from(technicianSchedules).where(eq(technicianSchedules.technicianId, technicianId));
  }

  async getTechnicianSchedulesByServiceOrder(serviceOrderId: number): Promise<TechnicianSchedule[]> {
    return db.select().from(technicianSchedules).where(eq(technicianSchedules.serviceOrderId, serviceOrderId));
  }

  async getTechnicianSchedulesByClient(clientId: number): Promise<TechnicianSchedule[]> {
    return db.select().from(technicianSchedules).where(eq(technicianSchedules.clientId, clientId));
  }

  async getTechnicianSchedulesByDate(date: Date): Promise<TechnicianSchedule[]> {
    return db.select().from(technicianSchedules).where(eq(technicianSchedules.scheduleDate, date));
  }

  async getTechnicianSchedulesByDateRange(startDate: Date, endDate: Date): Promise<TechnicianSchedule[]> {
    return db.select().from(technicianSchedules).where(
      and(
        lte(technicianSchedules.scheduleDate, endDate),
        gte(technicianSchedules.scheduleDate, startDate)
      )
    );
  }

  async createTechnicianSchedule(insertSchedule: InsertTechnicianSchedule): Promise<TechnicianSchedule> {
    const [schedule] = await db
      .insert(technicianSchedules)
      .values({
        ...insertSchedule,
        createdAt: new Date(),
        updatedAt: new Date(),
        completedAt: null
      })
      .returning();
    return schedule;
  }

  async updateTechnicianSchedule(id: number, data: Partial<TechnicianSchedule>): Promise<TechnicianSchedule | undefined> {
    const [updatedSchedule] = await db
      .update(technicianSchedules)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(technicianSchedules.id, id))
      .returning();
    return updatedSchedule || undefined;
  }

  async deleteTechnicianSchedule(id: number): Promise<boolean> {
    await db
      .delete(technicianSchedules)
      .where(eq(technicianSchedules.id, id));

    // Se após a exclusão, não for possível encontrar o agendamento, a exclusão foi bem-sucedida
    const schedule = await this.getTechnicianSchedule(id);
    return schedule === undefined;
  }

  async updateTechnicianScheduleStatus(id: number, status: "scheduled" | "in_progress" | "completed" | "cancelled"): Promise<TechnicianSchedule | undefined> {
    let completedAt: Date | null = null;
    if (status === 'completed') {
      completedAt = new Date();
    }

    const [updatedSchedule] = await db
      .update(technicianSchedules)
      .set({
        status,
        updatedAt: new Date(),
        completedAt
      })
      .where(eq(technicianSchedules.id, id))
      .returning();
    return updatedSchedule || undefined;
  }

  // Payment Method operations
  async getPaymentMethod(id: number): Promise<PaymentMethod | undefined> {
    const [method] = await db.select().from(paymentMethods).where(eq(paymentMethods.id, id));
    return method || undefined;
  }

  async getPaymentMethods(): Promise<PaymentMethod[]> {
    return db.select().from(paymentMethods);
  }

  async createPaymentMethod(insertMethod: InsertPaymentMethod): Promise<PaymentMethod> {
    const [method] = await db
      .insert(paymentMethods)
      .values({
        ...insertMethod,
        createdAt: new Date()
      })
      .returning();
    return method;
  }

  async updatePaymentMethod(id: number, data: Partial<PaymentMethod>): Promise<PaymentMethod | undefined> {
    const [updatedMethod] = await db
      .update(paymentMethods)
      .set(data)
      .where(eq(paymentMethods.id, id))
      .returning();
    return updatedMethod || undefined;
  }

  async deletePaymentMethod(id: number): Promise<boolean> {
    await db
      .delete(paymentMethods)
      .where(eq(paymentMethods.id, id));

    // Se após a exclusão, não for possível encontrar o método, a exclusão foi bem-sucedida
    const method = await this.getPaymentMethod(id);
    return method === undefined;
  }

  // Payment operations
  async getPayment(id: number): Promise<Payment | undefined> {
    const [payment] = await db.select().from(payments).where(eq(payments.id, id));
    return payment || undefined;
  }

  async getPayments(): Promise<Payment[]> {
    return db.select().from(payments);
  }

  async getPaymentsByInvoice(invoiceId: number): Promise<Payment[]> {
    return db.select().from(payments).where(eq(payments.invoiceId, invoiceId));
  }

  async getPaymentsByClient(clientId: number): Promise<Payment[]> {
    return db.select().from(payments).where(eq(payments.clientId, clientId));
  }

  async createPayment(insertPayment: InsertPayment): Promise<Payment> {
    const [payment] = await db
      .insert(payments)
      .values({
        ...insertPayment,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    // Update invoice if this payment is linked to an invoice
    if (payment.invoiceId) {
      await this.updateInvoiceAfterPayment(payment.invoiceId);
    }

    return payment;
  }

  async updatePayment(id: number, data: Partial<Payment>): Promise<Payment | undefined> {
    const [updatedPayment] = await db
      .update(payments)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(payments.id, id))
      .returning();

    // Update invoice if this payment is linked to an invoice
    if (updatedPayment?.invoiceId) {
      await this.updateInvoiceAfterPayment(updatedPayment.invoiceId);
    }

    return updatedPayment || undefined;
  }

  // Invoice operations
  async getInvoice(id: number): Promise<Invoice | undefined> {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.id, id));
    return invoice || undefined;
  }

  async getInvoiceByNumber(invoiceNumber: string): Promise<Invoice | undefined> {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.invoiceNumber, invoiceNumber));
    return invoice || undefined;
  }

  async getInvoices(): Promise<Invoice[]> {
    return db.select().from(invoices);
  }

  async getInvoicesByClient(clientId: number): Promise<Invoice[]> {
    return db.select().from(invoices).where(eq(invoices.clientId, clientId));
  }

  async getInvoicesByServiceOrder(serviceOrderId: number): Promise<Invoice[]> {
    return db.select().from(invoices).where(eq(invoices.serviceOrderId, serviceOrderId));
  }

  async getUnpaidInvoices(): Promise<Invoice[]> {
    return db.select().from(invoices).where(
      and(
        eq(invoices.status, "issued"),
        gt(invoices.totalAmount, sql`COALESCE(${invoices.paidAmount}, 0)`)
      )
    );
  }

  async getOverdueInvoices(): Promise<Invoice[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return db.select().from(invoices).where(
      and(
        eq(invoices.status, "issued"),
        gt(invoices.totalAmount, sql`COALESCE(${invoices.paidAmount}, 0)`),
        sql`${invoices.dueDate} <= ${today.toISOString().split('T')[0]}`
      )
    );
  }

  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    // Generate invoice number (prefix + sequential number)
    const result = await db.select({ count: sql<number>`count(*)` }).from(invoices);
    const count = result[0]?.count || 0;
    const invoiceNumber = `NF-${(count + 1).toString().padStart(6, '0')}`;

    const [invoice] = await db
      .insert(invoices)
      .values({
        ...insertInvoice,
        invoiceNumber,
        paidAmount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();
    return invoice;
  }

  async updateInvoice(id: number, data: Partial<Invoice>): Promise<Invoice | undefined> {
    // If status is being changed to "paid", update paidAt
    let paidAt = undefined;
    if (data.status === "paid") {
      paidAt = new Date();
    }

    const [updatedInvoice] = await db
      .update(invoices)
      .set({
        ...data,
        paidAt,
        updatedAt: new Date()
      })
      .where(eq(invoices.id, id))
      .returning();
    return updatedInvoice || undefined;
  }

  private async updateInvoiceAfterPayment(invoiceId: number): Promise<void> {
    // Get the invoice and all associated payments
    const invoice = await this.getInvoice(invoiceId);
    if (!invoice) return;

    const payments = await this.getPaymentsByInvoice(invoiceId);

    // Calculate total paid amount
    const paidAmount = payments.reduce((sum, payment) => {
      // Only include payments that are not refunded and are paid
      if (payment.status === "paid" || payment.status === "partially_paid") {
        return sum + payment.amount;
      }
      return sum;
    }, 0);

    // Update invoice status based on payment
    let status = invoice.status;
    if (paidAmount >= invoice.totalAmount) {
      status = "paid";
    } else if (paidAmount > 0) {
      status = "partially_paid";
    }

    // Update the invoice
    await db
      .update(invoices)
      .set({ 
        paidAmount,
        status: status as any, // Type assertion
        paidAt: status === "paid" ? new Date() : invoice.paidAt,
        updatedAt: new Date()
      })
      .where(eq(invoices.id, invoiceId));
  }

  // Invoice Item operations
  async getInvoiceItem(id: number): Promise<InvoiceItem | undefined> {
    const [item] = await db.select().from(invoiceItems).where(eq(invoiceItems.id, id));
    return item || undefined;
  }

  async getInvoiceItems(invoiceId: number): Promise<InvoiceItem[]> {
    return db.select().from(invoiceItems).where(eq(invoiceItems.invoiceId, invoiceId));
  }

  async createInvoiceItem(insertItem: InsertInvoiceItem): Promise<InvoiceItem> {
    const [item] = await db
      .insert(invoiceItems)
      .values(insertItem)
      .returning();

    // Recalculate the invoice total
    await this.recalculateInvoiceTotal(item.invoiceId);

    return item;
  }

  async updateInvoiceItem(id: number, data: Partial<InvoiceItem>): Promise<InvoiceItem | undefined> {
    const [updatedItem] = await db
      .update(invoiceItems)
      .set(data)
      .where(eq(invoiceItems.id, id))
      .returning();

    if (updatedItem) {
      // Recalculate the invoice total
      await this.recalculateInvoiceTotal(updatedItem.invoiceId);
    }

    return updatedItem || undefined;
  }

  async removeInvoiceItem(id: number): Promise<boolean> {
    // Get invoice ID before deletion
    const item = await this.getInvoiceItem(id);
    if (!item) return true; // Item doesn't exist, treat as successful deletion

    const invoiceId = item.invoiceId;

    await db
      .delete(invoiceItems)
      .where(eq(invoiceItems.id, id));

    // Recalculate the invoice total
    await this.recalculateInvoiceTotal(invoiceId);

    // Check if the item was deleted
    const checkItem = await this.getInvoiceItem(id);
    return checkItem === undefined;
  }

  // Recalculate the invoice total after changes to its items
  private async recalculateInvoiceTotal(invoiceId: number): Promise<void> {
    const items = await this.getInvoiceItems(invoiceId);

    // Sum the total of all items
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);

    // Update the invoice
    await db
      .update(invoices)
      .set({ 
        subtotal,
        totalAmount: subtotal, // In a real app, would add tax and subtract discount
        updatedAt: new Date()
      })
      .where(eq(invoices.id, invoiceId));
  }

  // Service operations
  async getService(id: number): Promise<Service | undefined> {
    const [service] = await db.select().from(services).where(eq(services.id, id));
    return service || undefined;
  }

  async getServices(): Promise<Service[]> {
    return db.select().from(services);
  }

  async createService(insertService: InsertService): Promise<Service> {
    const [service] = await db
      .insert(services)
      .values({
        ...insertService,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();
    return service;
  }

  async updateService(id: number, data: Partial<Service>): Promise<Service | undefined> {
    const [updatedService] = await db
      .update(services)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(services.id, id))
      .returning();
    return updatedService || undefined;
  }

  async deleteService(id: number): Promise<boolean>{
    await db
      .delete(services)
      .where(eq(services.id, id));

    // Se após a exclusão, não for possível encontrar o serviço, a exclusão foi bem-sucedida
    const service = await this.getService(id);
    return service === undefined;
  }

  // Implementação dos métodos para Parts (Peças)
  async getPart(id: number): Promise<Part | undefined> {
    const [part] = await db
      .select()
      .from(parts)
      .where(eq(parts.id, id));
    return part || undefined;
  }

  async getParts(): Promise<Part[]> {
    return db.select().from(parts).orderBy(parts.name);
  }

  async createPart(part: InsertPart): Promise<Part> {
    const [newPart] = await db
      .insert(parts)
      .values(part)
      .returning();
    return newPart;
  }

  async updatePart(id: number, data: Partial<Part>): Promise<Part | undefined> {
    const [updatedPart] = await db
      .update(parts)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(parts.id, id))
      .returning();
    return updatedPart || undefined;
  }

  async deletePart(id: number): Promise<boolean> {
    await db
      .delete(parts)
      .where(eq(parts.id, id));

    // Se após a exclusão, não for possível encontrar a peça, a exclusão foi bem-sucedida
    const part = await this.getPart(id);
    return part === undefined;
  }

  // Appointment operations
  async getAppointment(id: number): Promise<Appointment | undefined> {
    const [appointment] = await db.select().from(appointments).where(eq(appointments.id, id));
    return appointment || undefined;
  }

  async getAppointments(): Promise<Appointment[]> {
    return db.select().from(appointments).orderBy(desc(appointments.appointmentDate));
  }

  async getAppointmentsByType(type: string): Promise<Appointment[]> {
    return db.select().from(appointments).where(eq(appointments.type, type)).orderBy(desc(appointments.appointmentDate));
  }

  async getAppointmentsByStatus(status: string): Promise<Appointment[]> {
    return db.select().from(appointments).where(eq(appointments.status, status)).orderBy(desc(appointments.appointmentDate));
  }

  async getAppointmentsByTechnician(technicianId: number): Promise<Appointment[]> {
    return db.select().from(appointments).where(eq(appointments.technicianId, technicianId)).orderBy(desc(appointments.appointmentDate));
  }

  async getAppointmentsByClient(clientId: number): Promise<Appointment[]> {
    return db.select().from(appointments).where(eq(appointments.clientId, clientId)).orderBy(desc(appointments.appointmentDate));
  }

  async getAppointmentsByDate(date: Date): Promise<Appointment[]> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    return db.select().from(appointments)
      .where(and(
        gt(appointments.appointmentDate, startOfDay.toISOString()),
        lte(appointments.appointmentDate, endOfDay.toISOString())
      ))
      .orderBy(appointments.appointmentDate);
  }

  async getAppointmentsByDateRange(startDate: Date, endDate: Date): Promise<Appointment[]> {
    return db.select().from(appointments)
      .where(and(
        lte(startDate.toISOString(), appointments.appointmentDate),
        lte(appointments.appointmentDate, endDate.toISOString())
      ))
      .orderBy(appointments.appointmentDate);
  }

  async createAppointment(insertAppointment: InsertAppointment): Promise<Appointment> {
    const [appointment] = await db
      .insert(appointments)
      .values({
        ...insertAppointment,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();
    return appointment;
  }

  async updateAppointment(id: number, data: Partial<Appointment>): Promise<Appointment | undefined> {
    const [updatedAppointment] = await db
      .update(appointments)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(appointments.id, id))
      .returning();
    return updatedAppointment || undefined;
  }

  async deleteAppointment(id: number): Promise<boolean> {
    await db.delete(appointments).where(eq(appointments.id, id));
    const appointment = await this.getAppointment(id);
    return appointment === undefined;
  }

  async updateAppointmentStatus(id: number, status: string): Promise<Appointment | undefined> {
    const [updatedAppointment] = await db
      .update(appointments)
      .set({
        status: status as any,
        updatedAt: new Date()
      })
      .where(eq(appointments.id, id))
      .returning();
    return updatedAppointment || undefined;
  }

  async completeAppointment(id: number): Promise<Appointment | undefined> {
    const [completedAppointment] = await db
      .update(appointments)
      .set({
        status: "completed",
        completedAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(appointments.id, id))
      .returning();
    return completedAppointment || undefined;
  }

  async cancelAppointment(id: number, reason?: string): Promise<Appointment | undefined> {
    const [cancelledAppointment] = await db
      .update(appointments)
      .set({
        status: "cancelled",
        notes: reason || null,
        cancelledAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(appointments.id, id))
      .returning();
    return cancelledAppointment || undefined;
  }
}

export async function seedDatabase() {
  // Check if we already have users in the database
  const userCount = await db.select({ count: sql<number>`count(*)` }).from(users);
  if (userCount[0]?.count && userCount[0].count > 0) {
    console.log("Database already has users, skipping seed");
    return;
  }

  console.log("Seeding database...");

  // Create admin user
  const adminUser = await db.insert(users).values({
    name: "Admin User",
    email: "<EMAIL>",
    username: "admin",
    password: "admin123", // In a real app, this would be hashed
    role: "admin",
    phone: "************",
    active: true,
    createdAt: new Date()
  }).returning();

  // Create some equipment categories
  const categories = [
    { name: "Computers", description: "Laptops, desktops, and servers" },
    { name: "Mobile Devices", description: "Smartphones and tablets" },
    { name: "Printers", description: "Printers and scanners" },
    { name: "Networking", description: "Routers, switches, and access points" },
    { name: "Audio/Video", description: "TVs, speakers, and audio equipment" }
  ];
  for (const category of categories) {
    await db.insert(equipmentCategories).values(category);
  }

  // Create a client
  const client = await db.insert(clients).values({
    name: "Rafael do Nascimento Silva",
    email: "<EMAIL>",
    phone: "11 98765-4321",
    document: "123.456.789-00",
    address: "Rua das Flores, 123",
    city: "São Paulo",
    state: "SP",
    zipCode: "01234-567",
    notes: "Cliente frequente",
    active: true,
    createdAt: new Date()
  }).returning();

  // Create another client
  const client2 = await db.insert(clients).values({
    name: "TechCorp Ltda",
    email: "<EMAIL>",
    phone: "11 3456-7890",
    document: "12.345.678/0001-90",
    address: "Av. Paulista, 1000",
    city: "São Paulo",
    state: "SP",
    zipCode: "01310-100",
    notes: "Empresa de tecnologia",
    active: true,
    createdAt: new Date()
  }).returning();

  // Create equipment
  const clientEquipment = await db.insert(equipment).values({
    clientId: client2[0].id,
    categoryId: 2, // Mobile Devices
    brand: "Samsung",
    model: "Galaxy S21",
    serialNumber: "S21-123456",
    description: "Smartphone with cracked screen",
    status: "received",
    purchaseDate: new Date("2023-01-15"),
    notes: "Device has water damage too",
    createdAt: new Date()
  }).returning();

  // Create a service order
  const serviceOrder = await db.insert(serviceOrders).values({
    orderNumber: "OS-000011",
    clientId: client2[0].id,
    equipmentId: clientEquipment[0].id,
    status: "in_analysis",
    description: "Repair cracked screen and check for water damage",
    diagnostics: "Screen needs replacement, water damage minimal",
    createdAt: new Date(),
    updatedAt: new Date(),
    estimatedCompletionDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
  }).returning();

  // Create some inventory categories
  const invCategories = [
    { name: "Computer Parts", description: "Parts for computers and laptops" },
    { name: "Mobile Parts", description: "Parts for smartphones and tablets" },
    { name: "Printer Parts", description: "Parts for printers and scanners" },
    { name: "Cables", description: "Various cables and adapters" },
    { name: "Tools", description: "Tools for repairs" }
  ];
  for (const category of invCategories) {
    await db.insert(inventoryCategories).values(category);
  }

  // Create some inventory items
  const storage = new DatabaseStorage();
  const items = [
    { 
      name: "iPhone Display (Model A2111)", 
      categoryId: 2, 
      description: "Replacement display for iPhone models A2111", 
      quantity: 2, 
      minQuantity: 5, 
      price: 8900,
      supplier: "Apple Parts Inc.",
      sku: "IPH-DISP-A2111"
    },
    { 
      name: "MacBook Logic Board", 
      categoryId: 1, 
      description: "Logic board for MacBook Pro 2019-2020", 
      quantity: 1, 
      minQuantity: 3, 
      price: 42000,
      supplier: "Apple Parts Inc.",
      sku: "MLB-PRO-19-20"
    },
    { 
      name: "Samsung Galaxy Battery", 
      categoryId: 2, 
      description: "Replacement battery for Samsung Galaxy S21", 
      quantity: 0, 
      minQuantity: 5, 
      price: 3500,
      supplier: "Samsung Electronics",
      sku: "SAM-BAT-S21"
    },
    { 
      name: "Printer Ink Cartridge", 
      categoryId: 3, 
      description: "Black ink cartridge for HP LaserJet Pro", 
      quantity: 4, 
      minQuantity: 5, 
      price: 2200,
      supplier: "HP Supplies",
      sku: "HP-INK-BLK-01"
    },
    { 
      name: "Laptop Keyboard", 
      categoryId: 1, 
      description: "Keyboard for Dell XPS 13", 
      quantity: 3, 
      minQuantity: 4, 
      price: 4500,
      supplier: "Dell Parts",
      sku: "DELL-KB-XPS13"
    }
  ];
  for (const item of items) {
    await storage.createInventoryItem(item);
  }

  // Create some services
  const servicesList = [
    { 
      name: "Formatação de Computador", 
      description: "Formatação e reinstalação do sistema operacional", 
      price: 12000, 
      active: true 
    },
    { 
      name: "Limpeza de Hardware", 
      description: "Limpeza interna de componentes", 
      price: 8000, 
      active: true 
    },
    { 
      name: "Reparo de Tela", 
      description: "Troca de tela quebrada", 
      price: 25000, 
      active: true 
    },
    { 
      name: "Diagnóstico Técnico", 
      description: "Avaliação e diagnóstico de problemas", 
      price: 5000, 
      active: true 
    },
    { 
      name: "Instalação de Software", 
      description: "Instalação e configuração de software", 
      price: 7000, 
      active: true 
    }
  ];
  for (const service of servicesList) {
    await db.insert(services).values({
      ...service,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  // Create technician record
  await storage.createTechnician({
    userId: adminUser[0].id,
    specialties: "Computers, Mobile",
    status: "available"
  });

  // Create some parts
  const partsList = [
    { 
      name: "Placa de vídeo NVIDIA GeForce GTX 1650", 
      brand: "NVIDIA",
      description: "Placa de vídeo para jogos e edição de vídeo", 
      barcode: "789123456789",
      internalCode: "GPU-1650-01",
      purchaseValue: 120000, 
      saleValue: 150000,
      active: true
    },
    { 
      name: "Processador Intel Core i5 11400F", 
      brand: "Intel",
      description: "Processador 6 núcleos/12 threads", 
      barcode: "123789456123",
      internalCode: "CPU-I5-11400F",
      purchaseValue: 85000, 
      saleValue: 110000,
      active: true
    },
    { 
      name: "Memória RAM DDR4 8GB 3200MHz", 
      brand: "Corsair",
      description: "Memória RAM para desktop", 
      barcode: "456123789456",
      internalCode: "RAM-DDR4-8GB",
      purchaseValue: 18000, 
      saleValue: 25000,
      active: true
    },
    { 
      name: "SSD Kingston 480GB", 
      brand: "Kingston",
      description: "SSD SATA 2.5 polegadas", 
      barcode: "321654987123",
      internalCode: "SSD-KING-480",
      purchaseValue: 25000, 
      saleValue: 32000,
      active: true
    },
    { 
      name: "Tela LCD para Notebook 15.6 polegadas", 
      brand: "Samsung",
      description: "Tela para notebooks de 15.6 polegadas", 
      barcode: "987321654123",
      internalCode: "TELA-NOTE-156",
      purchaseValue: 45000, 
      saleValue: 65000,
      active: true
    }
  ];

  for (const part of partsList) {
    await db.insert(parts).values({
      ...part,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  console.log("Database seeded successfully");
}