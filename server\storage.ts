import {
  users, User, InsertUser,
  clients, Client, InsertClient,
  equipmentCategories, EquipmentCategory, InsertEquipmentCategory,
  equipment, Equipment, InsertEquipment,
  technicians, Technician, InsertTechnician,
  inventoryCategories, InventoryCategory, InsertInventoryCategory,
  inventoryItems, InventoryItem, InsertInventoryItem,
  serviceOrders, ServiceOrder, InsertServiceOrder,
  serviceOrderItems, ServiceOrderItem, InsertServiceOrderItem,
  quotes, Quote, InsertQuote,
  technicianSchedules, TechnicianSchedule, InsertTechnicianSchedule,
  appointments, Appointment, InsertAppointment,
  // Services
  services, Service, InsertService,
  // Parts
  parts, Part, InsertPart,
  // Financial module
  paymentMethods, PaymentMethod, InsertPaymentMethod,
  payments, Payment, InsertPayment,
  invoices, Invoice, InsertInvoice,
  invoiceItems, InvoiceItem, InsertInvoiceItem
} from "@shared/schema";

// Interface for storage operations
export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, data: Partial<User>): Promise<User | undefined>;
  getUsers(): Promise<User[]>;

  // Client operations
  getClient(id: number): Promise<Client | undefined>;
  getClients(): Promise<Client[]>;
  createClient(client: InsertClient): Promise<Client>;
  updateClient(id: number, data: Partial<Client>): Promise<Client | undefined>;
  deleteClient(id: number): Promise<boolean>;

  // Equipment Category operations
  getEquipmentCategory(id: number): Promise<EquipmentCategory | undefined>;
  getEquipmentCategories(): Promise<EquipmentCategory[]>;
  createEquipmentCategory(category: InsertEquipmentCategory): Promise<EquipmentCategory>;

  // Equipment operations
  getEquipment(id: number): Promise<Equipment | undefined>;
  getEquipmentByClient(clientId: number): Promise<Equipment[]>;
  getEquipments(): Promise<Equipment[]>;
  createEquipment(equipment: InsertEquipment): Promise<Equipment>;
  updateEquipment(id: number, data: Partial<Equipment>): Promise<Equipment | undefined>;

  // Technician operations
  getTechnician(id: number): Promise<Technician | undefined>;
  getTechnicianByUserId(userId: number): Promise<Technician | undefined>;
  getTechnicians(): Promise<Technician[]>;
  createTechnician(technician: InsertTechnician): Promise<Technician>;
  updateTechnician(id: number, data: Partial<Technician>): Promise<Technician | undefined>;

  // Inventory Category operations
  getInventoryCategory(id: number): Promise<InventoryCategory | undefined>;
  getInventoryCategories(): Promise<InventoryCategory[]>;
  createInventoryCategory(category: InsertInventoryCategory): Promise<InventoryCategory>;

  // Inventory Item operations
  getInventoryItem(id: number): Promise<InventoryItem | undefined>;
  getInventoryItems(): Promise<InventoryItem[]>;
  getInventoryItemsByCategory(categoryId: number): Promise<InventoryItem[]>;
  createInventoryItem(item: InsertInventoryItem): Promise<InventoryItem>;
  updateInventoryItem(id: number, data: Partial<InventoryItem>): Promise<InventoryItem | undefined>;
  adjustInventoryQuantity(id: number, quantity: number): Promise<InventoryItem | undefined>;
  getLowStockItems(): Promise<InventoryItem[]>;

  // Service Order operations
  getServiceOrder(id: number): Promise<ServiceOrder | undefined>;
  getServiceOrderByNumber(orderNumber: string): Promise<ServiceOrder | undefined>;
  getServiceOrders(): Promise<ServiceOrder[]>;
  getServiceOrdersByClient(clientId: number): Promise<ServiceOrder[]>;
  getServiceOrdersByTechnician(technicianId: number): Promise<ServiceOrder[]>;
  getServiceOrdersByStatus(status: string): Promise<ServiceOrder[]>;
  createServiceOrder(order: InsertServiceOrder): Promise<ServiceOrder>;
  updateServiceOrder(id: number, data: Partial<ServiceOrder>): Promise<ServiceOrder | undefined>;
  updateServiceOrderStatus(id: number, status: string): Promise<ServiceOrder | undefined>;
  deleteServiceOrder(id: number): Promise<boolean>;

  // Service Order Item operations
  getServiceOrderItem(id: number): Promise<ServiceOrderItem | undefined>;
  getServiceOrderItems(serviceOrderId: number): Promise<ServiceOrderItem[]>;
  createServiceOrderItem(item: InsertServiceOrderItem): Promise<ServiceOrderItem>;
  updateServiceOrderItem(id: number, data: Partial<ServiceOrderItem>): Promise<ServiceOrderItem | undefined>;
  removeServiceOrderItem(id: number): Promise<boolean>;

  // Quote operations
  getQuote(id: number): Promise<Quote | undefined>;
  getQuoteByNumber(quoteNumber: string): Promise<Quote | undefined>;
  getQuotes(): Promise<Quote[]>;
  getQuotesByClient(clientId: number): Promise<Quote[]>;
  getQuotesByServiceOrder(serviceOrderId: number): Promise<Quote[]>;
  createQuote(quote: InsertQuote): Promise<Quote>;
  updateQuote(id: number, data: Partial<Quote>): Promise<Quote | undefined>;
  approveQuote(id: number): Promise<Quote | undefined>;
  rejectQuote(id: number): Promise<Quote | undefined>;
  
  // Service operations
  getService(id: number): Promise<Service | undefined>;
  getServices(): Promise<Service[]>;
  createService(service: InsertService): Promise<Service>;
  updateService(id: number, data: Partial<Service>): Promise<Service | undefined>;
  deleteService(id: number): Promise<boolean>;
  
  // Parts operations
  getPart(id: number): Promise<Part | undefined>;
  getParts(): Promise<Part[]>;
  createPart(part: InsertPart): Promise<Part>;
  updatePart(id: number, data: Partial<Part>): Promise<Part | undefined>;
  deletePart(id: number): Promise<boolean>;
  
  // Technician Schedule operations
  getTechnicianSchedule(id: number): Promise<TechnicianSchedule | undefined>;
  getTechnicianSchedules(): Promise<TechnicianSchedule[]>;
  getTechnicianSchedulesByTechnician(technicianId: number): Promise<TechnicianSchedule[]>;
  getTechnicianSchedulesByServiceOrder(serviceOrderId: number): Promise<TechnicianSchedule[]>;
  getTechnicianSchedulesByClient(clientId: number): Promise<TechnicianSchedule[]>;
  getTechnicianSchedulesByDate(date: Date): Promise<TechnicianSchedule[]>;
  getTechnicianSchedulesByDateRange(startDate: Date, endDate: Date): Promise<TechnicianSchedule[]>;
  createTechnicianSchedule(schedule: InsertTechnicianSchedule): Promise<TechnicianSchedule>;
  updateTechnicianSchedule(id: number, data: Partial<TechnicianSchedule>): Promise<TechnicianSchedule | undefined>;
  deleteTechnicianSchedule(id: number): Promise<boolean>;
  updateTechnicianScheduleStatus(id: number, status: string): Promise<TechnicianSchedule | undefined>;

  // Appointment operations
  getAppointment(id: number): Promise<Appointment | undefined>;
  getAppointments(): Promise<Appointment[]>;
  getAppointmentsByType(type: string): Promise<Appointment[]>;
  getAppointmentsByStatus(status: string): Promise<Appointment[]>;
  getAppointmentsByTechnician(technicianId: number): Promise<Appointment[]>;
  getAppointmentsByClient(clientId: number): Promise<Appointment[]>;
  getAppointmentsByDate(date: Date): Promise<Appointment[]>;
  getAppointmentsByDateRange(startDate: Date, endDate: Date): Promise<Appointment[]>;
  createAppointment(appointment: InsertAppointment): Promise<Appointment>;
  updateAppointment(id: number, data: Partial<Appointment>): Promise<Appointment | undefined>;
  deleteAppointment(id: number): Promise<boolean>;
  updateAppointmentStatus(id: number, status: string): Promise<Appointment | undefined>;
  completeAppointment(id: number): Promise<Appointment | undefined>;
  cancelAppointment(id: number, reason?: string): Promise<Appointment | undefined>;
  
  // Payment Methods operations
  getPaymentMethod(id: number): Promise<PaymentMethod | undefined>;
  getPaymentMethods(): Promise<PaymentMethod[]>;
  createPaymentMethod(method: InsertPaymentMethod): Promise<PaymentMethod>;
  updatePaymentMethod(id: number, data: Partial<PaymentMethod>): Promise<PaymentMethod | undefined>;
  deletePaymentMethod(id: number): Promise<boolean>;
  
  // Payment operations
  getPayment(id: number): Promise<Payment | undefined>;
  getPayments(): Promise<Payment[]>;
  getPaymentsByInvoice(invoiceId: number): Promise<Payment[]>;
  getPaymentsByClient(clientId: number): Promise<Payment[]>;
  createPayment(payment: InsertPayment): Promise<Payment>;
  updatePayment(id: number, data: Partial<Payment>): Promise<Payment | undefined>;
  
  // Invoice operations
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoiceByNumber(invoiceNumber: string): Promise<Invoice | undefined>;
  getInvoices(): Promise<Invoice[]>;
  getInvoicesByClient(clientId: number): Promise<Invoice[]>;
  getInvoicesByServiceOrder(serviceOrderId: number): Promise<Invoice[]>;
  getUnpaidInvoices(): Promise<Invoice[]>;
  getOverdueInvoices(): Promise<Invoice[]>;
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  updateInvoice(id: number, data: Partial<Invoice>): Promise<Invoice | undefined>;
  
  // Invoice Item operations
  getInvoiceItem(id: number): Promise<InvoiceItem | undefined>;
  getInvoiceItems(invoiceId: number): Promise<InvoiceItem[]>;
  createInvoiceItem(item: InsertInvoiceItem): Promise<InvoiceItem>;
  updateInvoiceItem(id: number, data: Partial<InvoiceItem>): Promise<InvoiceItem | undefined>;
  removeInvoiceItem(id: number): Promise<boolean>;
}

// Importamos o storage e a função seedDatabase
import { DatabaseStorage } from './database-storage';
export { seedDatabase } from './database-storage';

// Exportar a instância do storage para uso em routes.ts
export const storage = new DatabaseStorage();

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private clients: Map<number, Client>;
  private equipmentCategories: Map<number, EquipmentCategory>;
  private equipment: Map<number, Equipment>;
  private technicians: Map<number, Technician>;
  private inventoryCategories: Map<number, InventoryCategory>;
  private inventoryItems: Map<number, InventoryItem>;
  private serviceOrders: Map<number, ServiceOrder>;
  private serviceOrderItems: Map<number, ServiceOrderItem>;
  private quotes: Map<number, Quote>;
  private technicianSchedules: Map<number, TechnicianSchedule>;
  private appointments: Map<number, Appointment>;
  private services: Map<number, Service>;
  // Financial module
  private paymentMethods: Map<number, PaymentMethod>;
  private payments: Map<number, Payment>;
  private invoices: Map<number, Invoice>;
  private invoiceItems: Map<number, InvoiceItem>;

  private nextUserId: number;
  private nextClientId: number;
  private nextEquipmentCategoryId: number;
  private nextEquipmentId: number;
  private nextTechnicianId: number;
  private nextInventoryCategoryId: number;
  private nextInventoryItemId: number;
  private nextServiceOrderId: number;
  private nextServiceOrderItemId: number;
  private nextQuoteId: number;
  private nextOrderNumber: number;
  private nextQuoteNumber: number;
  private nextTechnicianScheduleId: number;
  private nextAppointmentId: number;
  private nextServiceId: number;
  // Financial module
  private nextPaymentMethodId: number;
  private nextPaymentId: number;
  private nextInvoiceId: number;
  private nextInvoiceItemId: number;
  private nextInvoiceNumber: number;

  constructor() {
    this.users = new Map();
    this.clients = new Map();
    this.equipmentCategories = new Map();
    this.equipment = new Map();
    this.technicians = new Map();
    this.inventoryCategories = new Map();
    this.inventoryItems = new Map();
    this.serviceOrders = new Map();
    this.serviceOrderItems = new Map();
    this.quotes = new Map();
    this.technicianSchedules = new Map();
    this.appointments = new Map();
    this.services = new Map();
    // Financial module
    this.paymentMethods = new Map();
    this.payments = new Map();
    this.invoices = new Map();
    this.invoiceItems = new Map();

    this.nextUserId = 1;
    this.nextClientId = 1;
    this.nextEquipmentCategoryId = 1;
    this.nextEquipmentId = 1;
    this.nextTechnicianId = 1;
    this.nextInventoryCategoryId = 1;
    this.nextInventoryItemId = 1;
    this.nextServiceOrderId = 1;
    this.nextServiceOrderItemId = 1;
    this.nextQuoteId = 1;
    this.nextOrderNumber = 2300;
    this.nextQuoteNumber = 1000;
    this.nextTechnicianScheduleId = 1;
    this.nextAppointmentId = 1;
    this.nextServiceId = 1;
    // Financial module
    this.nextPaymentMethodId = 1;
    this.nextPaymentId = 1;
    this.nextInvoiceId = 1;
    this.nextInvoiceItemId = 1;
    this.nextInvoiceNumber = 1000;

    this.seedData();
  }

  // Seed some initial data for demonstration
  private seedData() {
    // Create default admin user
    const adminUser: InsertUser = {
      name: "Admin User",
      email: "<EMAIL>",
      username: "admin",
      password: "admin123", // In a real app, this would be hashed
      role: "admin",
      phone: "************",
      active: true
    };
    this.createUser(adminUser);

    // Create some equipment categories
    const categories = [
      { name: "Computers", description: "Laptops, desktops, and servers" },
      { name: "Mobile Devices", description: "Smartphones and tablets" },
      { name: "Printers", description: "Printers and scanners" },
      { name: "Networking", description: "Routers, switches, and access points" },
      { name: "Audio/Video", description: "TVs, speakers, and audio equipment" }
    ];
    categories.forEach(cat => this.createEquipmentCategory(cat));

    // Create some inventory categories
    const invCategories = [
      { name: "Computer Parts", description: "Parts for computers and laptops" },
      { name: "Mobile Parts", description: "Parts for smartphones and tablets" },
      { name: "Printer Parts", description: "Parts for printers and scanners" },
      { name: "Cables", description: "Various cables and adapters" },
      { name: "Tools", description: "Tools for repairs" }
    ];
    invCategories.forEach(cat => this.createInventoryCategory(cat));

    // Create some inventory items
    const items = [
      { 
        name: "iPhone Display (Model A2111)", 
        categoryId: 2, 
        description: "Replacement display for iPhone models A2111", 
        quantity: 2, 
        minQuantity: 5, 
        price: 8900,
        supplier: "Apple Parts Inc.",
        sku: "IPH-DISP-A2111"
      },
      { 
        name: "MacBook Logic Board", 
        categoryId: 1, 
        description: "Logic board for MacBook Pro 2019-2020", 
        quantity: 1, 
        minQuantity: 3, 
        price: 42000,
        supplier: "Apple Parts Inc.",
        sku: "MLB-PRO-19-20"
      },
      { 
        name: "Samsung Galaxy Battery", 
        categoryId: 2, 
        description: "Replacement battery for Samsung Galaxy S21", 
        quantity: 0, 
        minQuantity: 5, 
        price: 3500,
        supplier: "Samsung Electronics",
        sku: "SAM-BAT-S21"
      },
      { 
        name: "Printer Ink Cartridge", 
        categoryId: 3, 
        description: "Black ink cartridge for HP LaserJet Pro", 
        quantity: 4, 
        minQuantity: 5, 
        price: 2200,
        supplier: "HP Supplies",
        sku: "HP-INK-BLK-01"
      },
      { 
        name: "Laptop Keyboard", 
        categoryId: 1, 
        description: "Keyboard for Dell XPS 13", 
        quantity: 3, 
        minQuantity: 4, 
        price: 4500,
        supplier: "Dell Parts",
        sku: "DELL-KB-XPS13"
      }
    ];
    items.forEach(item => this.createInventoryItem(item));

    // Create some technicians
    const technicians = [
      { userId: 1, specialties: "Computers, Mobile", status: "available" },
    ];
    technicians.forEach(tech => this.createTechnician(tech));
    
    // Create some services
    const services = [
      { name: "Formatação de Computador", description: "Formatação e reinstalação do sistema operacional", price: 12000, active: true },
      { name: "Limpeza de Hardware", description: "Limpeza interna de componentes", price: 8000, active: true },
      { name: "Reparo de Tela", description: "Troca de tela quebrada", price: 25000, active: true },
      { name: "Diagnóstico Técnico", description: "Avaliação e diagnóstico de problemas", price: 5000, active: true },
      { name: "Instalação de Software", description: "Instalação e configuração de software", price: 7000, active: true }
    ];
    services.forEach(service => this.createService(service));
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username.toLowerCase() === username.toLowerCase()
    );
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.email.toLowerCase() === email.toLowerCase()
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.nextUserId++;
    const createdAt = new Date();
    const user: User = { ...insertUser, id, createdAt };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, data: Partial<User>): Promise<User | undefined> {
    const user = await this.getUser(id);
    if (!user) return undefined;

    const updatedUser = { ...user, ...data };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async getUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }

  // Client operations
  async getClient(id: number): Promise<Client | undefined> {
    return this.clients.get(id);
  }

  async getClients(): Promise<Client[]> {
    return Array.from(this.clients.values());
  }

  async createClient(insertClient: InsertClient): Promise<Client> {
    const id = this.nextClientId++;
    const createdAt = new Date();
    const client: Client = { ...insertClient, id, createdAt };
    this.clients.set(id, client);
    return client;
  }

  async updateClient(id: number, data: Partial<Client>): Promise<Client | undefined> {
    const client = await this.getClient(id);
    if (!client) return undefined;

    const updatedClient = { ...client, ...data };
    this.clients.set(id, updatedClient);
    return updatedClient;
  }

  async deleteClient(id: number): Promise<boolean> {
    return this.clients.delete(id);
  }

  // Equipment Category operations
  async getEquipmentCategory(id: number): Promise<EquipmentCategory | undefined> {
    return this.equipmentCategories.get(id);
  }

  async getEquipmentCategories(): Promise<EquipmentCategory[]> {
    return Array.from(this.equipmentCategories.values());
  }

  async createEquipmentCategory(category: InsertEquipmentCategory): Promise<EquipmentCategory> {
    const id = this.nextEquipmentCategoryId++;
    const newCategory: EquipmentCategory = { ...category, id };
    this.equipmentCategories.set(id, newCategory);
    return newCategory;
  }

  // Equipment operations
  async getEquipment(id: number): Promise<Equipment | undefined> {
    return this.equipment.get(id);
  }

  async getEquipmentByClient(clientId: number): Promise<Equipment[]> {
    return Array.from(this.equipment.values()).filter(
      (equip) => equip.clientId === clientId
    );
  }

  async getEquipments(): Promise<Equipment[]> {
    return Array.from(this.equipment.values());
  }

  async createEquipment(insertEquipment: InsertEquipment): Promise<Equipment> {
    const id = this.nextEquipmentId++;
    const createdAt = new Date();
    const equipment: Equipment = { ...insertEquipment, id, createdAt };
    this.equipment.set(id, equipment);
    return equipment;
  }

  async updateEquipment(id: number, data: Partial<Equipment>): Promise<Equipment | undefined> {
    const equipment = await this.getEquipment(id);
    if (!equipment) return undefined;

    const updatedEquipment = { ...equipment, ...data };
    this.equipment.set(id, updatedEquipment);
    return updatedEquipment;
  }

  // Technician operations
  async getTechnician(id: number): Promise<Technician | undefined> {
    return this.technicians.get(id);
  }

  async getTechnicianByUserId(userId: number): Promise<Technician | undefined> {
    return Array.from(this.technicians.values()).find(
      (tech) => tech.userId === userId
    );
  }

  async getTechnicians(): Promise<Technician[]> {
    return Array.from(this.technicians.values());
  }

  async createTechnician(insertTechnician: InsertTechnician): Promise<Technician> {
    const id = this.nextTechnicianId++;
    const technician: Technician = { ...insertTechnician, id };
    this.technicians.set(id, technician);
    return technician;
  }

  async updateTechnician(id: number, data: Partial<Technician>): Promise<Technician | undefined> {
    const technician = await this.getTechnician(id);
    if (!technician) return undefined;

    const updatedTechnician = { ...technician, ...data };
    this.technicians.set(id, updatedTechnician);
    return updatedTechnician;
  }

  // Inventory Category operations
  async getInventoryCategory(id: number): Promise<InventoryCategory | undefined> {
    return this.inventoryCategories.get(id);
  }

  async getInventoryCategories(): Promise<InventoryCategory[]> {
    return Array.from(this.inventoryCategories.values());
  }

  async createInventoryCategory(category: InsertInventoryCategory): Promise<InventoryCategory> {
    const id = this.nextInventoryCategoryId++;
    const newCategory: InventoryCategory = { ...category, id };
    this.inventoryCategories.set(id, newCategory);
    return newCategory;
  }

  // Inventory Item operations
  async getInventoryItem(id: number): Promise<InventoryItem | undefined> {
    return this.inventoryItems.get(id);
  }

  async getInventoryItems(): Promise<InventoryItem[]> {
    return Array.from(this.inventoryItems.values());
  }

  async getInventoryItemsByCategory(categoryId: number): Promise<InventoryItem[]> {
    return Array.from(this.inventoryItems.values()).filter(
      (item) => item.categoryId === categoryId
    );
  }

  async createInventoryItem(insertItem: InsertInventoryItem): Promise<InventoryItem> {
    const id = this.nextInventoryItemId++;
    const createdAt = new Date();
    const item: InventoryItem = { ...insertItem, id, createdAt };
    this.inventoryItems.set(id, item);
    return item;
  }

  async updateInventoryItem(id: number, data: Partial<InventoryItem>): Promise<InventoryItem | undefined> {
    const item = await this.getInventoryItem(id);
    if (!item) return undefined;

    const updatedItem = { ...item, ...data };
    this.inventoryItems.set(id, updatedItem);
    return updatedItem;
  }

  async adjustInventoryQuantity(id: number, quantityChange: number): Promise<InventoryItem | undefined> {
    const item = await this.getInventoryItem(id);
    if (!item) return undefined;

    const newQuantity = item.quantity + quantityChange;
    if (newQuantity < 0) return undefined; // Can't have negative inventory

    const updatedItem = { ...item, quantity: newQuantity };
    this.inventoryItems.set(id, updatedItem);
    return updatedItem;
  }

  async getLowStockItems(): Promise<InventoryItem[]> {
    return Array.from(this.inventoryItems.values()).filter(
      (item) => item.quantity <= item.minQuantity
    );
  }

  // Service Order operations
  async getServiceOrder(id: number): Promise<ServiceOrder | undefined> {
    return this.serviceOrders.get(id);
  }

  async getServiceOrderByNumber(orderNumber: string): Promise<ServiceOrder | undefined> {
    return Array.from(this.serviceOrders.values()).find(
      (order) => order.orderNumber === orderNumber
    );
  }

  async getServiceOrders(): Promise<ServiceOrder[]> {
    return Array.from(this.serviceOrders.values());
  }

  async getServiceOrdersByClient(clientId: number): Promise<ServiceOrder[]> {
    return Array.from(this.serviceOrders.values()).filter(
      (order) => order.clientId === clientId
    );
  }

  async getServiceOrdersByTechnician(technicianId: number): Promise<ServiceOrder[]> {
    return Array.from(this.serviceOrders.values()).filter(
      (order) => order.technicianId === technicianId
    );
  }

  async getServiceOrdersByStatus(status: string): Promise<ServiceOrder[]> {
    return Array.from(this.serviceOrders.values()).filter(
      (order) => order.status === status
    );
  }

  async createServiceOrder(insertOrder: InsertServiceOrder): Promise<ServiceOrder> {
    const id = this.nextServiceOrderId++;
    const orderNumber = `OS-${this.nextOrderNumber++}`;
    const createdAt = new Date();
    const updatedAt = new Date();
    const order: ServiceOrder = { 
      ...insertOrder, 
      id, 
      orderNumber, 
      createdAt, 
      updatedAt, 
      completedAt: null 
    };
    this.serviceOrders.set(id, order);
    return order;
  }

  async updateServiceOrder(id: number, data: Partial<ServiceOrder>): Promise<ServiceOrder | undefined> {
    const order = await this.getServiceOrder(id);
    if (!order) return undefined;

    const updatedAt = new Date();
    const updatedOrder = { ...order, ...data, updatedAt };
    this.serviceOrders.set(id, updatedOrder);
    return updatedOrder;
  }

  async updateServiceOrderStatus(id: number, status: string): Promise<ServiceOrder | undefined> {
    const order = await this.getServiceOrder(id);
    if (!order) return undefined;

    const updatedAt = new Date();
    let completedAt = order.completedAt;
    if (status === 'completed' && !completedAt) {
      completedAt = new Date();
    }

    const updatedOrder = { ...order, status, updatedAt, completedAt };
    this.serviceOrders.set(id, updatedOrder);
    return updatedOrder;
  }
  
  async deleteServiceOrder(id: number): Promise<boolean> {
    // Check if the service order exists
    const order = await this.getServiceOrder(id);
    if (!order) return false;
    
    // First, delete all associated service order items
    const items = await this.getServiceOrderItems(id);
    for (const item of items) {
      this.serviceOrderItems.delete(item.id);
    }
    
    // Then delete the service order itself
    return this.serviceOrders.delete(id);
  }

  // Service Order Item operations
  async getServiceOrderItem(id: number): Promise<ServiceOrderItem | undefined> {
    return this.serviceOrderItems.get(id);
  }

  async getServiceOrderItems(serviceOrderId: number): Promise<ServiceOrderItem[]> {
    return Array.from(this.serviceOrderItems.values()).filter(
      (item) => item.serviceOrderId === serviceOrderId
    );
  }

  async createServiceOrderItem(insertItem: InsertServiceOrderItem): Promise<ServiceOrderItem> {
    const id = this.nextServiceOrderItemId++;
    const item: ServiceOrderItem = { ...insertItem, id };
    this.serviceOrderItems.set(id, item);
    return item;
  }

  async updateServiceOrderItem(id: number, data: Partial<ServiceOrderItem>): Promise<ServiceOrderItem | undefined> {
    const item = await this.getServiceOrderItem(id);
    if (!item) return undefined;

    const updatedItem = { ...item, ...data };
    this.serviceOrderItems.set(id, updatedItem);
    return updatedItem;
  }

  async removeServiceOrderItem(id: number): Promise<boolean> {
    return this.serviceOrderItems.delete(id);
  }

  // Quote operations
  async getQuote(id: number): Promise<Quote | undefined> {
    return this.quotes.get(id);
  }

  async getQuoteByNumber(quoteNumber: string): Promise<Quote | undefined> {
    return Array.from(this.quotes.values()).find(
      (quote) => quote.quoteNumber === quoteNumber
    );
  }

  async getQuotes(): Promise<Quote[]> {
    return Array.from(this.quotes.values());
  }

  async getQuotesByClient(clientId: number): Promise<Quote[]> {
    return Array.from(this.quotes.values()).filter(
      (quote) => quote.clientId === clientId
    );
  }

  async getQuotesByServiceOrder(serviceOrderId: number): Promise<Quote[]> {
    return Array.from(this.quotes.values()).filter(
      (quote) => quote.serviceOrderId === serviceOrderId
    );
  }

  async createQuote(insertQuote: InsertQuote): Promise<Quote> {
    const id = this.nextQuoteId++;
    const quoteNumber = `Q-${this.nextQuoteNumber++}`;
    const createdAt = new Date();
    const quote: Quote = { 
      ...insertQuote, 
      id, 
      quoteNumber, 
      createdAt, 
      approvedAt: null 
    };
    this.quotes.set(id, quote);
    return quote;
  }

  async updateQuote(id: number, data: Partial<Quote>): Promise<Quote | undefined> {
    const quote = await this.getQuote(id);
    if (!quote) return undefined;

    const updatedQuote = { ...quote, ...data };
    this.quotes.set(id, updatedQuote);
    return updatedQuote;
  }

  async approveQuote(id: number): Promise<Quote | undefined> {
    const quote = await this.getQuote(id);
    if (!quote) return undefined;

    const approvedAt = new Date();
    const updatedQuote = { ...quote, status: 'approved', approvedAt };
    this.quotes.set(id, updatedQuote);
    return updatedQuote;
  }

  async rejectQuote(id: number): Promise<Quote | undefined> {
    const quote = await this.getQuote(id);
    if (!quote) return undefined;

    const updatedQuote = { ...quote, status: 'rejected' };
    this.quotes.set(id, updatedQuote);
    return updatedQuote;
  }

  // Technician Schedule operations
  async getTechnicianSchedule(id: number): Promise<TechnicianSchedule | undefined> {
    return this.technicianSchedules.get(id);
  }

  async getTechnicianSchedules(): Promise<TechnicianSchedule[]> {
    return Array.from(this.technicianSchedules.values());
  }

  async getTechnicianSchedulesByTechnician(technicianId: number): Promise<TechnicianSchedule[]> {
    return Array.from(this.technicianSchedules.values()).filter(
      (schedule) => schedule.technicianId === technicianId
    );
  }

  async getTechnicianSchedulesByServiceOrder(serviceOrderId: number): Promise<TechnicianSchedule[]> {
    return Array.from(this.technicianSchedules.values()).filter(
      (schedule) => schedule.serviceOrderId === serviceOrderId
    );
  }

  async getTechnicianSchedulesByClient(clientId: number): Promise<TechnicianSchedule[]> {
    return Array.from(this.technicianSchedules.values()).filter(
      (schedule) => schedule.clientId === clientId
    );
  }

  async getTechnicianSchedulesByDate(date: Date): Promise<TechnicianSchedule[]> {
    const dateString = date.toISOString().split('T')[0]; // Get YYYY-MM-DD format
    
    return Array.from(this.technicianSchedules.values()).filter((schedule) => {
      return schedule.scheduleDate === dateString;
    });
  }

  async getTechnicianSchedulesByDateRange(startDate: Date, endDate: Date): Promise<TechnicianSchedule[]> {
    const startDateString = startDate.toISOString().split('T')[0];
    const endDateString = endDate.toISOString().split('T')[0];
    
    return Array.from(this.technicianSchedules.values()).filter((schedule) => {
      return schedule.scheduleDate >= startDateString && schedule.scheduleDate <= endDateString;
    });
  }

  async createTechnicianSchedule(insertSchedule: InsertTechnicianSchedule): Promise<TechnicianSchedule> {
    const id = this.nextTechnicianScheduleId++;
    const createdAt = new Date();
    const updatedAt = new Date();
    const completedAt = null;
    
    const schedule: TechnicianSchedule = { 
      ...insertSchedule, 
      id, 
      createdAt,
      updatedAt,
      completedAt
    };
    
    this.technicianSchedules.set(id, schedule);
    return schedule;
  }

  async updateTechnicianSchedule(id: number, data: Partial<TechnicianSchedule>): Promise<TechnicianSchedule | undefined> {
    const schedule = await this.getTechnicianSchedule(id);
    if (!schedule) return undefined;

    const updatedSchedule = { ...schedule, ...data };
    this.technicianSchedules.set(id, updatedSchedule);
    return updatedSchedule;
  }

  async deleteTechnicianSchedule(id: number): Promise<boolean> {
    return this.technicianSchedules.delete(id);
  }

  async updateTechnicianScheduleStatus(id: number, status: "scheduled" | "in_progress" | "completed" | "cancelled"): Promise<TechnicianSchedule | undefined> {
    const schedule = await this.getTechnicianSchedule(id);
    if (!schedule) return undefined;

    const updatedAt = new Date();
    let completedAt = schedule.completedAt;
    
    // Se o status for "completed" e não tiver completedAt, defina-o
    if (status === "completed" && !completedAt) {
      completedAt = new Date();
    }

    const updatedSchedule = { 
      ...schedule, 
      status,
      updatedAt,
      completedAt
    };
    
    this.technicianSchedules.set(id, updatedSchedule);
    return updatedSchedule;
  }

  // Payment Method operations
  async getPaymentMethod(id: number): Promise<PaymentMethod | undefined> {
    return this.paymentMethods.get(id);
  }

  async getPaymentMethods(): Promise<PaymentMethod[]> {
    return Array.from(this.paymentMethods.values());
  }

  async createPaymentMethod(insertPaymentMethod: InsertPaymentMethod): Promise<PaymentMethod> {
    const id = this.nextPaymentMethodId++;
    const createdAt = new Date();
    const paymentMethod: PaymentMethod = { ...insertPaymentMethod, id, createdAt };
    this.paymentMethods.set(id, paymentMethod);
    return paymentMethod;
  }

  async updatePaymentMethod(id: number, data: Partial<PaymentMethod>): Promise<PaymentMethod | undefined> {
    const method = await this.getPaymentMethod(id);
    if (!method) return undefined;

    const updatedMethod = { ...method, ...data };
    this.paymentMethods.set(id, updatedMethod);
    return updatedMethod;
  }

  async deletePaymentMethod(id: number): Promise<boolean> {
    return this.paymentMethods.delete(id);
  }

  // Payment operations
  async getPayment(id: number): Promise<Payment | undefined> {
    return this.payments.get(id);
  }

  async getPayments(): Promise<Payment[]> {
    // Ordenar por data de criação, mais recente primeiro
    return Array.from(this.payments.values()).sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }

  async getPaymentsByInvoice(invoiceId: number): Promise<Payment[]> {
    return Array.from(this.payments.values()).filter(
      (payment) => payment.invoiceId === invoiceId
    );
  }

  async getPaymentsByClient(clientId: number): Promise<Payment[]> {
    return Array.from(this.payments.values()).filter(
      (payment) => payment.clientId === clientId
    );
  }

  async createPayment(insertPayment: InsertPayment): Promise<Payment> {
    const id = this.nextPaymentId++;
    const createdAt = new Date();
    const updatedAt = new Date();
    const paidAt = insertPayment.status === "paid" ? new Date() : null;
    const refundedAt = null;

    const payment: Payment = { 
      ...insertPayment, 
      id, 
      createdAt, 
      updatedAt, 
      paidAt, 
      refundedAt 
    };
    this.payments.set(id, payment);

    // Se o pagamento estiver completo e vinculado a uma fatura, atualize o status da fatura
    if (payment.status === "paid" && payment.invoiceId) {
      await this.updateInvoiceAfterPayment(payment.invoiceId);
    }

    return payment;
  }

  async updatePayment(id: number, data: Partial<Payment>): Promise<Payment | undefined> {
    const payment = await this.getPayment(id);
    if (!payment) return undefined;

    const updatedAt = new Date();
    let paidAt = payment.paidAt;
    let refundedAt = payment.refundedAt;

    // Atualizar paidAt se o status mudar para "paid"
    if (data.status === "paid" && payment.status !== "paid") {
      paidAt = new Date();
    }

    // Atualizar refundedAt se o status mudar para "refunded"
    if (data.status === "refunded" && payment.status !== "refunded") {
      refundedAt = new Date();
    }

    const updatedPayment = { 
      ...payment, 
      ...data, 
      updatedAt,
      paidAt,
      refundedAt
    };
    this.payments.set(id, updatedPayment);

    // Se o pagamento estiver completo e vinculado a uma fatura, atualize o status da fatura
    if (updatedPayment.status === "paid" && updatedPayment.invoiceId) {
      await this.updateInvoiceAfterPayment(updatedPayment.invoiceId);
    }

    return updatedPayment;
  }

  // Invoice operations
  async getInvoice(id: number): Promise<Invoice | undefined> {
    return this.invoices.get(id);
  }

  async getInvoiceByNumber(invoiceNumber: string): Promise<Invoice | undefined> {
    return Array.from(this.invoices.values()).find(
      (invoice) => invoice.invoiceNumber === invoiceNumber
    );
  }

  async getInvoices(): Promise<Invoice[]> {
    // Ordenar por data de criação, mais recente primeiro
    return Array.from(this.invoices.values()).sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }

  async getInvoicesByClient(clientId: number): Promise<Invoice[]> {
    return Array.from(this.invoices.values()).filter(
      (invoice) => invoice.clientId === clientId
    );
  }

  async getInvoicesByServiceOrder(serviceOrderId: number): Promise<Invoice[]> {
    return Array.from(this.invoices.values()).filter(
      (invoice) => invoice.serviceOrderId === serviceOrderId
    );
  }

  async getUnpaidInvoices(): Promise<Invoice[]> {
    return Array.from(this.invoices.values()).filter(
      (invoice) => invoice.status === "pending"
    );
  }

  async getOverdueInvoices(): Promise<Invoice[]> {
    const today = new Date();
    
    return Array.from(this.invoices.values()).filter((invoice) => {
      // Fatura pendente e data de vencimento já passou
      return invoice.status === "pending" && 
             invoice.dueDate && 
             new Date(invoice.dueDate) <= today;
    });
  }

  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    const id = this.nextInvoiceId++;
    const invoiceNumber = `INV-${this.nextInvoiceNumber++.toString().padStart(6, '0')}`;
    const createdAt = new Date();
    const updatedAt = new Date();
    
    const invoice: Invoice = { 
      ...insertInvoice, 
      id,
      invoiceNumber,
      createdAt,
      updatedAt,
      sentAt: null,
      paidAt: null,
      cancelledAt: null,
      paidAmount: 0,
      status: "draft"
    };
    
    this.invoices.set(id, invoice);
    return invoice;
  }

  async updateInvoice(id: number, data: Partial<Invoice>): Promise<Invoice | undefined> {
    const invoice = await this.getInvoice(id);
    if (!invoice) return undefined;

    const updatedAt = new Date();
    let sentAt = invoice.sentAt;
    let paidAt = invoice.paidAt;
    let cancelledAt = invoice.cancelledAt;

    // Atualizar sentAt se o status mudar para "sent"
    if (data.status === "sent" && invoice.status !== "sent" && !sentAt) {
      sentAt = new Date();
    }
    
    // Atualizar paidAt se o status mudar para "paid"
    if (data.status === "paid" && invoice.status !== "paid") {
      paidAt = new Date();
    }
    
    // Atualizar cancelledAt se o status mudar para "cancelled"
    if (data.status === "cancelled" && invoice.status !== "cancelled") {
      cancelledAt = new Date();
    }

    const updatedInvoice = { 
      ...invoice, 
      ...data, 
      updatedAt,
      sentAt,
      paidAt,
      cancelledAt
    };
    
    this.invoices.set(id, updatedInvoice);
    return updatedInvoice;
  }

  // Helper method to update invoice after payment
  private async updateInvoiceAfterPayment(invoiceId: number): Promise<void> {
    const invoice = await this.getInvoice(invoiceId);
    if (!invoice) return;
    
    // Obter todos os pagamentos relacionados a esta fatura
    const invoicePayments = await this.getPaymentsByInvoice(invoiceId);
    
    // Calcular o total pago
    const paidAmount = invoicePayments
      .filter(p => p.status === "paid")
      .reduce((sum, payment) => sum + payment.amount, 0);
    
    // Determinar o novo status
    let newStatus = invoice.status;
    if (paidAmount >= invoice.totalAmount) {
      newStatus = "paid";
    } else if (paidAmount > 0) {
      newStatus = "partial";
    }
    
    // Atualizar a fatura
    const paidAt = newStatus === "paid" ? new Date() : invoice.paidAt;
    const updatedInvoice = { 
      ...invoice, 
      paidAmount, 
      status: newStatus, 
      paidAt,
      updatedAt: new Date() 
    };
    
    this.invoices.set(invoiceId, updatedInvoice);
  }

  // Service operations
  async getService(id: number): Promise<Service | undefined> {
    return this.services.get(id);
  }

  async getServices(): Promise<Service[]> {
    return Array.from(this.services.values());
  }

  async createService(service: InsertService): Promise<Service> {
    const id = this.nextServiceId++;
    const newService: Service = { ...service, id };
    this.services.set(id, newService);
    return newService;
  }

  async updateService(id: number, data: Partial<Service>): Promise<Service | undefined> {
    const service = await this.getService(id);
    if (!service) return undefined;
    
    const updatedService = { ...service, ...data };
    this.services.set(id, updatedService);
    return updatedService;
  }

  async deleteService(id: number): Promise<boolean> {
    return this.services.delete(id);
  }
  
  // Invoice Item operations
  async getInvoiceItem(id: number): Promise<InvoiceItem | undefined> {
    return this.invoiceItems.get(id);
  }

  async getInvoiceItems(invoiceId: number): Promise<InvoiceItem[]> {
    return Array.from(this.invoiceItems.values()).filter(
      (item) => item.invoiceId === invoiceId
    );
  }

  async createInvoiceItem(insertItem: InsertInvoiceItem): Promise<InvoiceItem> {
    const id = this.nextInvoiceItemId++;
    const item: InvoiceItem = { ...insertItem, id };
    this.invoiceItems.set(id, item);
    
    // Recalcular o total da fatura após adicionar um item
    await this.recalculateInvoiceTotal(insertItem.invoiceId);
    
    return item;
  }

  async updateInvoiceItem(id: number, data: Partial<InvoiceItem>): Promise<InvoiceItem | undefined> {
    const item = await this.getInvoiceItem(id);
    if (!item) return undefined;

    const updatedItem = { ...item, ...data };
    this.invoiceItems.set(id, updatedItem);
    
    // Recalcular o total da fatura após atualizar um item
    await this.recalculateInvoiceTotal(item.invoiceId);
    
    return updatedItem;
  }

  async removeInvoiceItem(id: number): Promise<boolean> {
    const item = await this.getInvoiceItem(id);
    if (!item) return false;
    
    const invoiceId = item.invoiceId;
    const result = this.invoiceItems.delete(id);
    
    if (result) {
      // Recalcular o total da fatura após remover um item
      await this.recalculateInvoiceTotal(invoiceId);
    }
    
    return result;
  }

  // Helper method to recalculate invoice total
  private async recalculateInvoiceTotal(invoiceId: number): Promise<void> {
    const invoice = await this.getInvoice(invoiceId);
    if (!invoice) return;
    
    const items = await this.getInvoiceItems(invoiceId);
    
    // Calcular o total da fatura com base nos itens
    const totalAmount = items.reduce((sum, item) => {
      return sum + (item.quantity * item.unitPrice);
    }, 0);
    
    // Atualizar o total da fatura
    const updatedInvoice = { 
      ...invoice, 
      totalAmount, 
      updatedAt: new Date() 
    };
    
    this.invoices.set(invoiceId, updatedInvoice);
  }

  // Appointment operations
  async getAppointment(id: number): Promise<Appointment | undefined> {
    return this.appointments.get(id);
  }

  async getAppointments(): Promise<Appointment[]> {
    return Array.from(this.appointments.values()).sort((a, b) => 
      new Date(a.appointmentDate).getTime() - new Date(b.appointmentDate).getTime()
    );
  }

  async getAppointmentsByType(type: string): Promise<Appointment[]> {
    return Array.from(this.appointments.values()).filter(
      (appointment) => appointment.type === type
    );
  }

  async getAppointmentsByStatus(status: string): Promise<Appointment[]> {
    return Array.from(this.appointments.values()).filter(
      (appointment) => appointment.status === status
    );
  }

  async getAppointmentsByTechnician(technicianId: number): Promise<Appointment[]> {
    return Array.from(this.appointments.values()).filter(
      (appointment) => appointment.technicianId === technicianId
    );
  }

  async getAppointmentsByClient(clientId: number): Promise<Appointment[]> {
    return Array.from(this.appointments.values()).filter(
      (appointment) => appointment.clientId === clientId
    );
  }

  async getAppointmentsByDate(date: Date): Promise<Appointment[]> {
    const targetDate = new Date(date);
    targetDate.setHours(0, 0, 0, 0);
    const nextDay = new Date(targetDate);
    nextDay.setDate(nextDay.getDate() + 1);

    return Array.from(this.appointments.values()).filter((appointment) => {
      const appointmentDate = new Date(appointment.appointmentDate);
      return appointmentDate >= targetDate && appointmentDate < nextDay;
    });
  }

  async getAppointmentsByDateRange(startDate: Date, endDate: Date): Promise<Appointment[]> {
    return Array.from(this.appointments.values()).filter((appointment) => {
      const appointmentDate = new Date(appointment.appointmentDate);
      return appointmentDate >= startDate && appointmentDate <= endDate;
    });
  }

  async createAppointment(insertAppointment: InsertAppointment): Promise<Appointment> {
    const id = this.nextAppointmentId++;
    const createdAt = new Date();
    const updatedAt = new Date();
    
    const appointment: Appointment = { 
      ...insertAppointment, 
      id,
      createdAt,
      updatedAt,
      completedAt: null,
      cancelledAt: null,
      cancelReason: null
    };
    
    this.appointments.set(id, appointment);
    return appointment;
  }

  async updateAppointment(id: number, data: Partial<Appointment>): Promise<Appointment | undefined> {
    const appointment = await this.getAppointment(id);
    if (!appointment) return undefined;

    const updatedAt = new Date();
    const updatedAppointment = { ...appointment, ...data, updatedAt };
    this.appointments.set(id, updatedAppointment);
    return updatedAppointment;
  }

  async deleteAppointment(id: number): Promise<boolean> {
    return this.appointments.delete(id);
  }

  async updateAppointmentStatus(id: number, status: string): Promise<Appointment | undefined> {
    const appointment = await this.getAppointment(id);
    if (!appointment) return undefined;

    const updatedAt = new Date();
    const updatedAppointment = { ...appointment, status, updatedAt };
    this.appointments.set(id, updatedAppointment);
    return updatedAppointment;
  }

  async completeAppointment(id: number): Promise<Appointment | undefined> {
    const appointment = await this.getAppointment(id);
    if (!appointment) return undefined;

    const completedAt = new Date();
    const updatedAt = new Date();
    const updatedAppointment = { 
      ...appointment, 
      status: 'completed', 
      completedAt, 
      updatedAt 
    };
    this.appointments.set(id, updatedAppointment);
    return updatedAppointment;
  }

  async updateAppointmentStatus(id: number, status: string): Promise<Appointment | undefined> {
    const appointment = await this.getAppointment(id);
    if (!appointment) return undefined;

    const updatedAt = new Date();
    const updatedAppointment = { ...appointment, status, updatedAt };
    this.appointments.set(id, updatedAppointment);
    return updatedAppointment;
  }

  async completeAppointment(id: number): Promise<Appointment | undefined> {
    const appointment = await this.getAppointment(id);
    if (!appointment) return undefined;

    const completedAt = new Date();
    const updatedAt = new Date();
    const updatedAppointment = { 
      ...appointment, 
      status: 'completed', 
      completedAt, 
      updatedAt 
    };
    this.appointments.set(id, updatedAppointment);
    return updatedAppointment;
  }

  async cancelAppointment(id: number, reason?: string): Promise<Appointment | undefined> {
    const appointment = await this.getAppointment(id);
    if (!appointment) return undefined;

    const cancelledAt = new Date();
    const updatedAt = new Date();
    const updatedAppointment = { 
      ...appointment, 
      status: 'cancelled', 
      cancelledAt, 
      cancelReason: reason || null,
      updatedAt 
    };
    this.appointments.set(id, updatedAppointment);
    return updatedAppointment;
  }
}

// Fim da implementação de MemStorage
