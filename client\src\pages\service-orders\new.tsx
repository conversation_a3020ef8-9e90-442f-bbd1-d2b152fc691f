import { useState, useEffect } from "react";
import { useLoc<PERSON>, Link } from "wouter";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { insertServiceOrderSchema, insertClientSchema } from "@shared/schema";
import {
  Client,
  Equipment,
  Technician,
  User,
  Service,
  Part,
} from "@/lib/types";
import { SERVICE_ORDER_STATUSES } from "@/lib/constants";
import { generateOrderNumber } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Esquema para serviços dentro da ordem de serviço
const serviceItemSchema = z.object({
  serviceId: z.coerce.number().min(1, "Serviço é obrigatório"),
  quantity: z.coerce.number().min(1, "Quantidade deve ser maior que zero"),
  description: z.string().optional(),
  unitPrice: z.coerce.number(), // preço em centavos
});

// Esquema para peças dentro da ordem de serviço
const partItemSchema = z.object({
  partId: z.coerce.number().min(1, "Peça é obrigatória"),
  quantity: z.coerce.number().min(1, "Quantidade deve ser maior que zero"),
  description: z.string().optional(),
  unitPrice: z.coerce.number(), // preço em centavos
});

// Não podemos usar .extend() em um schema com transform, então criamos um novo schema
const createServiceOrderSchema = z.object({
  clientId: z.coerce.number().min(1, "Cliente é obrigatório"),
  description: z.string().optional().default(""),
  status: z.enum(
    SERVICE_ORDER_STATUSES.map((s) => s.value) as [string, ...string[]],
  ),
  orderNumber: z.string().optional(),
  equipmentId: z.coerce.number().optional().nullable(),
  technicianId: z.coerce.number().optional().nullable(),
  serviceItems: z.array(serviceItemSchema).optional().default([]),
  partItems: z.array(partItemSchema).optional().default([]),
  diagnostics: z.string().optional().nullable(),
  solution: z.string().optional().nullable(),
  internalNotes: z.string().optional().nullable(),
  estimatedCompletionDate: z
    .union([z.date(), z.string(), z.null()])
    .optional()
    .nullable(),
});

type FormValues = z.infer<typeof createServiceOrderSchema>;

// Esquema de formulário para novo cliente
const clientFormSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido").optional().default(""),
  phone: z.string().min(1, "Telefone é obrigatório"),
  document: z.string().optional().default(""),
  address: z.string().optional().default(""),
  city: z.string().optional().default(""),
  state: z.string().optional().default(""),
  postalCode: z.string().optional().default(""),
  notes: z.string().optional().default(""),
});

// Esquema de formulário para novo equipamento
const equipmentFormSchema = z.object({
  clientId: z.coerce.number().min(1, "Cliente é obrigatório"),
  brand: z.string().min(1, "Marca é obrigatória"),
  model: z.string().min(1, "Modelo é obrigatório"),
  serialNumber: z.string().optional().default(""),
  description: z.string().optional().default(""),
  status: z.string().default("active"),
  categoryId: z.coerce.number().optional().nullable(),
});

// Esquema de formulário para novo serviço
const serviceFormSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().optional().default(""),
  price: z.coerce.number().min(0, "Preço deve ser maior ou igual a zero"),
  active: z.boolean().default(true),
});

// Esquema de formulário para nova peça
const partFormSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  brand: z.string().min(1, "Marca é obrigatória"),
  description: z.string().optional().default(""),
  barcode: z.string().optional().default(""),
  sku: z.string().optional().default(""),
  purchaseValue: z.coerce.number().min(0, "Valor de compra deve ser maior ou igual a zero"),
  saleValue: z.coerce.number().min(0, "Valor de venda deve ser maior ou igual a zero"),
  categoryId: z.coerce.number().optional().nullable(),
});

type ClientFormValues = z.infer<typeof clientFormSchema>;
type EquipmentFormValues = z.infer<typeof equipmentFormSchema>;
type ServiceFormValues = z.infer<typeof serviceFormSchema>;
type PartFormValues = z.infer<typeof partFormSchema>;

export default function NewServiceOrder() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showNewClientDialog, setShowNewClientDialog] = useState(false);
  const [showNewEquipmentDialog, setShowNewEquipmentDialog] = useState(false);
  const [showNewServiceDialog, setShowNewServiceDialog] = useState(false);
  const [showNewPartDialog, setShowNewPartDialog] = useState(false);

  // Load clients for dropdown
  const { data: clients = [] } = useQuery<Client[]>({
    queryKey: ["/api/clients"],
  });

  // Load equipment for dropdown (will be filtered based on selected client)
  const { data: allEquipment = [] } = useQuery<Equipment[]>({
    queryKey: ["/api/equipment"],
  });

  // Load technicians for dropdown
  const { data: technicians = [] } = useQuery<Technician[]>({
    queryKey: ["/api/technicians"],
  });

  // Load users to get technician names
  const { data: users = [] } = useQuery<User[]>({
    queryKey: ["/api/users"],
  });

  // Load services for dropdown
  const { data: services = [] } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  // Load parts for dropdown
  const { data: parts = [] } = useQuery<Part[]>({
    queryKey: ["/api/parts"],
  });

  // Log para depuração
  useEffect(() => {
    console.log("Peças carregadas:", parts);
    if (parts.length === 0) {
      console.log("Nenhuma peça encontrada no sistema");
    }
  }, [parts]);

  // Estados para gerenciar serviços e peças em edição temporária
  const [tempService, setTempService] = useState<{
    serviceId: number | null;
    quantity: number;
    unitPrice: number;
  }>({
    serviceId: null,
    quantity: 1,
    unitPrice: 0,
  });

  const [tempPart, setTempPart] = useState<{
    partId: number | null;
    quantity: number;
    unitPrice: number;
  }>({
    partId: null,
    quantity: 1,
    unitPrice: 0,
  });

  // Estado para armazenar o valor total calculado
  const [totalValue, setTotalValue] = useState(0);

  // Recuperar dados salvos no localStorage, se existirem
  const getSavedFormData = () => {
    if (typeof window === "undefined") return null;

    const savedData = localStorage.getItem("newServiceOrderFormData");
    if (savedData) {
      try {
        return JSON.parse(savedData);
      } catch (e) {
        console.error("Erro ao processar dados salvos:", e);
        return null;
      }
    }
    return null;
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(createServiceOrderSchema),
    defaultValues: getSavedFormData() || {
      status: "received",
      description: "",
      internalNotes: "",
      estimatedCompletionDate: undefined,
      serviceItems: [],
      partItems: [],
    },
  });

  const watchedClientId = form.watch("clientId");
  const watchedServiceItems = form.watch("serviceItems");
  const watchedPartItems = form.watch("partItems");

  // Filter equipment based on selected client
  const clientEquipment = watchedClientId
    ? allEquipment.filter((e) => e.clientId === Number(watchedClientId))
    : [];

  // Get technician full name for display
  const getTechnicianName = (technicianId: number) => {
    const technician = technicians.find((t) => t.id === technicianId);
    if (!technician) return "Unknown";

    const user = users.find((u) => u.id === technician.userId);
    return user ? user.name : "Unknown";
  };

  // Método para calcular o valor total da ordem de serviço
  const calculateTotal = () => {
    let total = 0;

    // Somar valores dos serviços
    if (watchedServiceItems && watchedServiceItems.length > 0) {
      total += watchedServiceItems.reduce((sum, item) => {
        return sum + item.quantity * item.unitPrice;
      }, 0);
    }

    // Somar valores das peças
    if (watchedPartItems && watchedPartItems.length > 0) {
      total += watchedPartItems.reduce((sum, item) => {
        return sum + item.quantity * item.unitPrice;
      }, 0);
    }

    return total;
  };

  // Atualizar valor total quando os itens mudam
  useEffect(() => {
    setTotalValue(calculateTotal());
  }, [watchedServiceItems, watchedPartItems]);

  // Método para adicionar um serviço à lista
  const handleAddService = () => {
    if (!tempService.serviceId) return;

    const selectedService = services.find(
      (s) => s.id === tempService.serviceId,
    );
    if (!selectedService) return;

    const serviceItem = {
      serviceId: tempService.serviceId,
      quantity: tempService.quantity,
      description: selectedService.name,
      unitPrice: selectedService.price,
    };

    const currentItems = form.getValues("serviceItems") || [];
    form.setValue("serviceItems", [...currentItems, serviceItem]);

    // Limpar o form temporário
    setTempService({
      serviceId: null,
      quantity: 1,
      unitPrice: 0,
    });
  };

  // Método para adicionar uma peça à lista
  const handleAddPart = () => {
    if (!tempPart.partId) return;

    const selectedPart = parts.find((p) => p.id === tempPart.partId);
    if (!selectedPart) return;

    // Verificar se a quantidade solicitada está disponível
    const partItem = {
      partId: tempPart.partId,
      quantity: tempPart.quantity,
      description: `${selectedPart.brand} ${selectedPart.name}`,
      unitPrice: selectedPart.saleValue,
    };

    console.log("Adicionando peça:", selectedPart);

    const currentItems = form.getValues("partItems") || [];
    form.setValue("partItems", [...currentItems, partItem]);

    // Limpar o form temporário
    setTempPart({
      partId: null,
      quantity: 1,
      unitPrice: 0,
    });

    toast({
      title: "Peça adicionada",
      description: `${selectedPart.name} foi adicionada à ordem de serviço`,
    });
  };

  // Salvar formulário no localStorage quando ele for atualizado
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (typeof window !== "undefined") {
        localStorage.setItem("newServiceOrderFormData", JSON.stringify(value));
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Método para remover um serviço da lista
  const handleRemoveService = (index: number) => {
    const currentItems = form.getValues("serviceItems") || [];
    form.setValue(
      "serviceItems",
      currentItems.filter((_, i) => i !== index),
    );
  };

  // Método para remover uma peça da lista
  const handleRemovePart = (index: number) => {
    const currentItems = form.getValues("partItems") || [];
    form.setValue(
      "partItems",
      currentItems.filter((_, i) => i !== index),
    );
  };

  // Formatação de moeda brasileira
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value / 100);
  };

  const createServiceOrderMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      return apiRequest("POST", "/api/service-orders", data);
    },
    onSuccess: () => {
      // Limpar os dados salvos após criação bem-sucedida
      if (typeof window !== "undefined") {
        localStorage.removeItem("newServiceOrderFormData");
      }

      queryClient.invalidateQueries({ queryKey: ["/api/service-orders"] });
      toast({
        title: "Ordem de serviço criada",
        description: "A ordem de serviço foi criada com sucesso",
      });
      navigate("/service-orders");
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar ordem de serviço",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: FormValues) => {
    console.log("Form submitted with values:", values);

    // Calcular o valor total antes de enviar
    const totalAmountValue = calculateTotal();

    // Garantir que a data esteja no formato correto antes de enviar
    const formattedValues = {
      ...values,
      // Adicionar orderNumber se não existir
      orderNumber: values.orderNumber || generateOrderNumber(),
      // Certifique-se de que estimatedCompletionDate seja uma data válida
      // Se for uma string, converta para Date
      estimatedCompletionDate: values.estimatedCompletionDate
        ? new Date(values.estimatedCompletionDate as any)
        : undefined,
      // Adicionar o valor total calculado
      totalAmount: totalAmountValue,
    };

    console.log("Sending formatted values:", formattedValues);
    try {
      createServiceOrderMutation.mutate(formattedValues);
    } catch (error) {
      console.error("Error in mutation:", error);
    }
  };

  const handleCancel = () => {
    setShowCancelDialog(true);
  };

  const confirmCancel = () => {
    // Limpar os dados salvos ao cancelar
    if (typeof window !== "undefined") {
      localStorage.removeItem("newServiceOrderFormData");
    }

    setShowCancelDialog(false);
    navigate("/service-orders");
  };

  const dismissCancel = () => {
    setShowCancelDialog(false);
  };

  // Formulário para criar um novo cliente
  const clientForm = useForm<ClientFormValues>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      document: "",
      address: "",
      city: "",
      state: "",
      postalCode: "",
      notes: "",
    },
    shouldUseNativeValidation: false,
  });
  
  // Formulário para criar um novo equipamento
  const equipmentForm = useForm<EquipmentFormValues>({
    resolver: zodResolver(equipmentFormSchema),
    defaultValues: {
      clientId: form.getValues("clientId") || 0,
      brand: "",
      model: "",
      serialNumber: "",
      description: "",
      status: "active",
      categoryId: null,
    },
    shouldUseNativeValidation: false,
  });
  
  // Formulário para criar um novo serviço
  const serviceForm = useForm<ServiceFormValues>({
    resolver: zodResolver(serviceFormSchema),
    defaultValues: {
      name: "",
      description: "",
      price: 0,
      active: true,
    },
    shouldUseNativeValidation: false,
  });
  
  // Formulário para criar uma nova peça
  const partForm = useForm<PartFormValues>({
    resolver: zodResolver(partFormSchema),
    defaultValues: {
      name: "",
      brand: "",
      description: "",
      barcode: "",
      sku: "",
      purchaseValue: 0,
      saleValue: 0,
      categoryId: null,
    },
    shouldUseNativeValidation: false,
  });

  // Mutação para criar um novo cliente
  const createClientMutation = useMutation({
    mutationFn: async (data: ClientFormValues) => {
      return apiRequest("POST", "/api/clients", data);
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["/api/clients"] });

      // Obter o ID do novo cliente e selecioná-lo no formulário de OS
      response.json().then((newClient) => {
        form.setValue("clientId", newClient.id);
        setShowNewClientDialog(false);

        toast({
          title: "Cliente criado",
          description: `${newClient.name} foi adicionado com sucesso`,
        });

        // Resetar o formulário de cliente
        clientForm.reset();
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar cliente",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });

  // Função para submeter o formulário de novo cliente
  const onClientSubmit = (values: ClientFormValues) => {
    createClientMutation.mutate(values);
  };
  
  // Mutação para criar um novo equipamento
  const createEquipmentMutation = useMutation({
    mutationFn: async (data: EquipmentFormValues) => {
      return apiRequest("POST", "/api/equipment", data);
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["/api/equipment"] });

      // Obter o ID do novo equipamento e selecioná-lo no formulário de OS
      response.json().then((newEquipment) => {
        form.setValue("equipmentId", newEquipment.id);
        setShowNewEquipmentDialog(false);

        toast({
          title: "Equipamento criado",
          description: `${newEquipment.brand} ${newEquipment.model} foi adicionado com sucesso`,
        });

        // Resetar o formulário de equipamento
        equipmentForm.reset();
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar equipamento",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });
  
  // Função para submeter o formulário de novo equipamento
  const onEquipmentSubmit = (values: EquipmentFormValues) => {
    createEquipmentMutation.mutate(values);
  };
  
  // Mutação para criar um novo serviço
  const createServiceMutation = useMutation({
    mutationFn: async (data: ServiceFormValues) => {
      return apiRequest("POST", "/api/services", data);
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["/api/services"] });

      response.json().then((newService) => {
        setShowNewServiceDialog(false);

        toast({
          title: "Serviço criado",
          description: `${newService.name} foi adicionado com sucesso`,
        });

        // Resetar o formulário de serviço
        serviceForm.reset();
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar serviço",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });
  
  // Função para submeter o formulário de novo serviço
  const onServiceSubmit = (values: ServiceFormValues) => {
    createServiceMutation.mutate(values);
  };
  
  // Mutação para criar uma nova peça
  const createPartMutation = useMutation({
    mutationFn: async (data: PartFormValues) => {
      return apiRequest("POST", "/api/parts", data);
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["/api/parts"] });

      response.json().then((newPart) => {
        setShowNewPartDialog(false);

        toast({
          title: "Peça criada",
          description: `${newPart.name} foi adicionada com sucesso`,
        });

        // Resetar o formulário de peça
        partForm.reset();
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar peça",
        description: error.message || "Algo deu errado",
        variant: "destructive",
      });
    },
  });
  
  // Função para submeter o formulário de nova peça
  const onPartSubmit = (values: PartFormValues) => {
    createPartMutation.mutate(values);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />

      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header
          title="Nova Ordem de Serviço"
          onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        <div className="flex-1 overflow-y-auto bg-slate-100 p-2">
          <Card>
            <CardHeader className="space-y-2 py-4">
              <CardTitle>Criar Nova Ordem de Serviço</CardTitle>
              <CardDescription>
                Registre uma nova ordem de serviço para um cliente.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-3"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <FormField
                      control={form.control}
                      name="clientId"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex justify-between items-center">
                            <FormLabel>Cliente*</FormLabel>
                            <Button
                              type="button"
                              variant="ghost"
                              className="text-xs px-2 py-1 h-7 text-primary"
                              onClick={() => setShowNewClientDialog(true)}
                            >
                              + Novo Cliente
                            </Button>
                          </div>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o cliente" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {clients.map((client) => (
                                <SelectItem
                                  key={client.id}
                                  value={client.id.toString()}
                                >
                                  {client.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="equipmentId"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex justify-between items-center">
                            <FormLabel>Equipamento</FormLabel>
                            <Button
                              type="button"
                              variant="ghost"
                              className="text-xs px-2 py-1 h-7 text-primary"
                              onClick={() => {
                                // Atualizar o clientId no formulário de equipamento
                                equipmentForm.setValue("clientId", form.getValues("clientId") || 0);
                                setShowNewEquipmentDialog(true);
                              }}
                              disabled={!watchedClientId}
                            >
                              + Novo Equipamento
                            </Button>
                          </div>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value?.toString()}
                            disabled={
                              !watchedClientId || clientEquipment.length === 0
                            }
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={
                                    !watchedClientId
                                      ? "Selecione o cliente primeiro"
                                      : clientEquipment.length === 0
                                        ? "Sem equipamentos para este cliente"
                                        : "Selecione o equipamento"
                                  }
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {clientEquipment.map((equipment) => (
                                <SelectItem
                                  key={equipment.id}
                                  value={equipment.id.toString()}
                                >
                                  {equipment.brand} {equipment.model}{" "}
                                  {equipment.serialNumber &&
                                    `(${equipment.serialNumber})`}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="technicianId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Técnico</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Atribuir técnico (opcional)" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {technicians.map((technician) => (
                                <SelectItem
                                  key={technician.id}
                                  value={technician.id.toString()}
                                >
                                  {getTechnicianName(technician.id)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="col-span-1 md:col-span-2">
                      <div className="flex justify-between items-center">
                        <h3 className="text-sm font-medium mb-1">Serviços</h3>
                        <Button
                          type="button"
                          variant="ghost"
                          className="text-xs px-2 py-1 h-7 text-primary"
                          onClick={() => setShowNewServiceDialog(true)}
                        >
                          + Novo Serviço
                        </Button>
                      </div>

                      <div className="mb-1 p-2 border rounded-md bg-white">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-2">
                          <div className="md:col-span-2">
                            <Select
                              onValueChange={(value) => {
                                const serviceId = parseInt(value);
                                const selectedService = services.find(
                                  (s) => s.id === serviceId,
                                );
                                setTempService({
                                  serviceId,
                                  quantity: 1,
                                  unitPrice: selectedService?.price || 0,
                                });
                              }}
                              value={tempService.serviceId?.toString() || ""}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o serviço" />
                              </SelectTrigger>
                              <SelectContent>
                                {services
                                  .filter((service) => service.active)
                                  .map((service) => (
                                    <SelectItem
                                      key={service.id}
                                      value={service.id.toString()}
                                    >
                                      {service.name} -{" "}
                                      {formatCurrency(service.price)}
                                    </SelectItem>
                                  ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Input
                              type="number"
                              placeholder="Quantidade"
                              value={tempService.quantity}
                              min={1}
                              onChange={(e) =>
                                setTempService({
                                  ...tempService,
                                  quantity: parseInt(e.target.value) || 1,
                                })
                              }
                            />
                          </div>

                          <div>
                            <Button
                              type="button"
                              onClick={handleAddService}
                              disabled={!tempService.serviceId}
                              className="w-full"
                            >
                              Adicionar Serviço
                            </Button>
                          </div>
                        </div>

                        {/* Espaçador - O botão "Novo Serviço" foi removido para evitar duplicação */}

                        {watchedServiceItems &&
                        watchedServiceItems.length > 0 ? (
                          <div className="mt-2 border rounded overflow-hidden">
                            <table className="w-full text-sm">
                              <thead className="bg-muted">
                                <tr>
                                  <th className="p-2 text-left">Serviço</th>
                                  <th className="p-2 text-center">Qtd</th>
                                  <th className="p-2 text-right">Valor Un.</th>
                                  <th className="p-2 text-right">Subtotal</th>
                                  <th className="p-2 text-center">Ações</th>
                                </tr>
                              </thead>
                              <tbody>
                                {watchedServiceItems.map((item, index) => (
                                  <tr key={index} className="border-t">
                                    <td className="p-2">{item.description}</td>
                                    <td className="p-2 text-center">
                                      {item.quantity}
                                    </td>
                                    <td className="p-2 text-right">
                                      {formatCurrency(item.unitPrice)}
                                    </td>
                                    <td className="p-2 text-right">
                                      {formatCurrency(
                                        item.quantity * item.unitPrice,
                                      )}
                                    </td>
                                    <td className="p-2 text-center">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                          handleRemoveService(index)
                                        }
                                        className="h-7 px-2 text-destructive hover:text-destructive"
                                      >
                                        Remover
                                      </Button>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground italic">
                            Nenhum serviço adicionado
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="col-span-1 md:col-span-2">
                      <div className="flex justify-between items-center">
                        <h3 className="text-sm font-medium mb-2">Peças</h3>
                        <Button
                          type="button"
                          variant="ghost"
                          className="text-xs px-2 py-1 h-7 text-primary"
                          onClick={() => setShowNewPartDialog(true)}
                        >
                          + Nova Peça
                        </Button>
                      </div>

                      <div className="mb-3 p-2 border rounded-md bg-white">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-3">
                          <div className="md:col-span-2">
                            <Select
                              onValueChange={(value) => {
                                const partId = parseInt(value);
                                const selectedPart = parts.find(
                                  (p) => p.id === partId,
                                );
                                if (selectedPart) {
                                  console.log(
                                    "Selecionando peça:",
                                    selectedPart,
                                  );
                                  setTempPart({
                                    partId,
                                    quantity: 1,
                                    unitPrice: selectedPart.saleValue || 0,
                                  });
                                }
                              }}
                              value={tempPart.partId?.toString() || ""}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione a peça" />
                              </SelectTrigger>
                              <SelectContent>
                                {parts && parts.length > 0 ? (
                                  parts.map((part) => (
                                    <SelectItem
                                      key={part.id}
                                      value={part.id.toString()}
                                    >
                                      {part.name} ({part.brand || "Sem marca"})
                                      {/* Somente exibe estoque se a propriedade existir */}
                                      {(part as any).quantity !== undefined &&
                                        ` - Estoque: ${(part as any).quantity}`}
                                      {/* Somente exibe [Inventário] se a propriedade existir */}
                                      {(part as any).isInventoryItem &&
                                        " [Inventário]"}
                                      - {formatCurrency(part.saleValue)}
                                    </SelectItem>
                                  ))
                                ) : (
                                  <SelectItem value="empty" disabled>
                                    Nenhuma peça disponível
                                  </SelectItem>
                                )}
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Input
                              type="number"
                              placeholder="Quantidade"
                              value={tempPart.quantity}
                              min={1}
                              onChange={(e) =>
                                setTempPart({
                                  ...tempPart,
                                  quantity: parseInt(e.target.value) || 1,
                                })
                              }
                            />
                          </div>

                          <div>
                            <Button
                              type="button"
                              onClick={handleAddPart}
                              disabled={!tempPart.partId}
                              className="w-full"
                            >
                              Adicionar Peça
                            </Button>
                          </div>
                        </div>

                        {/* Espaçador - O botão "Nova Peça" foi removido para evitar duplicação */}

                        {watchedPartItems && watchedPartItems.length > 0 ? (
                          <div className="mt-2 border rounded overflow-hidden">
                            <table className="w-full text-sm">
                              <thead className="bg-muted">
                                <tr>
                                  <th className="p-2 text-left">Peça</th>
                                  <th className="p-2 text-center">Qtd</th>
                                  <th className="p-2 text-right">Valor Un.</th>
                                  <th className="p-2 text-right">Subtotal</th>
                                  <th className="p-2 text-center">Ações</th>
                                </tr>
                              </thead>
                              <tbody>
                                {watchedPartItems.map((item, index) => (
                                  <tr key={index} className="border-t">
                                    <td className="p-2">{item.description}</td>
                                    <td className="p-2 text-center">
                                      {item.quantity}
                                    </td>
                                    <td className="p-2 text-right">
                                      {formatCurrency(item.unitPrice)}
                                    </td>
                                    <td className="p-2 text-right">
                                      {formatCurrency(
                                        item.quantity * item.unitPrice,
                                      )}
                                    </td>
                                    <td className="p-2 text-center">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleRemovePart(index)}
                                        className="h-7 px-2 text-destructive hover:text-destructive"
                                      >
                                        Remover
                                      </Button>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground italic">
                            Nenhuma peça adicionada
                          </p>
                        )}
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {SERVICE_ORDER_STATUSES.map((status) => (
                                <SelectItem
                                  key={status.value}
                                  value={status.value}
                                >
                                  {status.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Descrição</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Descreva a solicitação de serviço ou problema"
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="internalNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Anotações Internas</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Anotações visíveis apenas para a equipe (opcional)"
                            className="min-h-[80px]"
                            {...field}
                            value={field.value ?? ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="estimatedCompletionDate"
                    render={({ field }) => {
                      // Função para garantir o formato string do valor
                      let valueFormatted = "";

                      if (field.value) {
                        if (typeof field.value === "string") {
                          valueFormatted = field.value;
                        } else if (field.value instanceof Date) {
                          valueFormatted = field.value
                            .toISOString()
                            .split("T")[0];
                        }
                      }

                      return (
                        <FormItem>
                          <FormLabel>Data Estimada de Conclusão</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              value={valueFormatted}
                              onChange={(e) => {
                                // Apenas armazenamos o valor como string
                                field.onChange(e.target.value || "");
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />

                  <div className="col-span-1 md:col-span-2 mt-3">
                    <div className="p-2 border rounded-md bg-white">
                      <h3 className="text-lg font-medium mb-2">
                        Resumo da Ordem de Serviço
                      </h3>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Total de Serviços:</span>
                          <span className="font-medium">
                            {formatCurrency(
                              watchedServiceItems?.reduce(
                                (sum, item) =>
                                  sum + item.quantity * item.unitPrice,
                                0,
                              ) || 0,
                            )}
                          </span>
                        </div>

                        <div className="flex justify-between text-sm">
                          <span>Total de Peças:</span>
                          <span className="font-medium">
                            {formatCurrency(
                              watchedPartItems?.reduce(
                                (sum, item) =>
                                  sum + item.quantity * item.unitPrice,
                                0,
                              ) || 0,
                            )}
                          </span>
                        </div>

                        <div className="border-t pt-1 mt-1">
                          <div className="flex justify-between font-semibold text-lg">
                            <span>Valor Total:</span>
                            <span className="text-primary">
                              {formatCurrency(totalValue)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 mt-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      disabled={createServiceOrderMutation.isPending}
                      className="bg-primary hover:bg-primary-dark"
                    >
                      {createServiceOrderMutation.isPending ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                          Salvando...
                        </div>
                      ) : (
                        "Criar Ordem de Serviço"
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Cancelar Criação da Ordem de Serviço
            </AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja cancelar? Todas as informações inseridas
              serão perdidas.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={dismissCancel}>
              Não, continuar editando
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmCancel}>
              Sim, cancelar criação
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Diálogo para criar um novo cliente */}
      <Dialog open={showNewClientDialog} onOpenChange={setShowNewClientDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Adicionar Novo Cliente</DialogTitle>
            <DialogDescription>
              Preencha as informações do cliente para registrá-lo no sistema.
            </DialogDescription>
          </DialogHeader>

          <Form {...clientForm}>
            <form
              onSubmit={clientForm.handleSubmit(onClientSubmit)}
              className="space-y-4"
            >
              <FormField
                control={clientForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome*</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Nome completo do cliente"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={clientForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={clientForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefone*</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="(00) 00000-0000" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={clientForm.control}
                name="document"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Documento (CPF/CNPJ)</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="000.000.000-00" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={clientForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Endereço</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Rua, número, complemento"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <FormField
                  control={clientForm.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cidade</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={clientForm.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estado</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={clientForm.control}
                  name="postalCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CEP</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="00000-000" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter className="mt-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowNewClientDialog(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={createClientMutation.isPending}>
                  {createClientMutation.isPending
                    ? "Salvando..."
                    : "Salvar Cliente"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Diálogo para criar um novo equipamento */}
      <Dialog open={showNewEquipmentDialog} onOpenChange={setShowNewEquipmentDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Adicionar Novo Equipamento</DialogTitle>
            <DialogDescription>
              Preencha as informações do equipamento para registrá-lo no sistema.
            </DialogDescription>
          </DialogHeader>

          <Form {...equipmentForm}>
            <form onSubmit={equipmentForm.handleSubmit(onEquipmentSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  control={equipmentForm.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Marca*</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Marca do equipamento" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={equipmentForm.control}
                  name="model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Modelo*</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Modelo do equipamento" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={equipmentForm.control}
                  name="serialNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número de Série</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Número de série" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={equipmentForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Descrição</FormLabel>
                      <FormControl>
                        <Textarea {...field} placeholder="Descrição do equipamento" rows={3} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowNewEquipmentDialog(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={createEquipmentMutation.isPending}>
                  {createEquipmentMutation.isPending
                    ? "Salvando..."
                    : "Salvar Equipamento"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Diálogo para criar um novo serviço */}
      <Dialog open={showNewServiceDialog} onOpenChange={setShowNewServiceDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Adicionar Novo Serviço</DialogTitle>
            <DialogDescription>
              Preencha as informações do serviço para registrá-lo no sistema.
            </DialogDescription>
          </DialogHeader>

          <Form {...serviceForm}>
            <form onSubmit={serviceForm.handleSubmit(onServiceSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 gap-3">
                <FormField
                  control={serviceForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome*</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nome do serviço" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={serviceForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição</FormLabel>
                      <FormControl>
                        <Textarea {...field} placeholder="Descrição do serviço" rows={3} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={serviceForm.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preço (em centavos)*</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="number"
                          min="0"
                          placeholder="Preço em centavos (ex: 10000 para R$ 100,00)" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowNewServiceDialog(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={createServiceMutation.isPending}>
                  {createServiceMutation.isPending
                    ? "Salvando..."
                    : "Salvar Serviço"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Diálogo para criar uma nova peça */}
      <Dialog open={showNewPartDialog} onOpenChange={setShowNewPartDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Adicionar Nova Peça</DialogTitle>
            <DialogDescription>
              Preencha as informações da peça para registrá-la no sistema.
            </DialogDescription>
          </DialogHeader>

          <Form {...partForm}>
            <form onSubmit={partForm.handleSubmit(onPartSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  control={partForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome*</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nome da peça" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={partForm.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Marca*</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Marca da peça" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={partForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Descrição</FormLabel>
                      <FormControl>
                        <Textarea {...field} placeholder="Descrição da peça" rows={2} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={partForm.control}
                  name="purchaseValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor de Compra (em centavos)*</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="number"
                          min="0"
                          placeholder="Valor em centavos" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={partForm.control}
                  name="saleValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor de Venda (em centavos)*</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="number"
                          min="0"
                          placeholder="Valor em centavos" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowNewPartDialog(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={createPartMutation.isPending}>
                  {createPartMutation.isPending ? "Salvando..." : "Salvar Peça"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
