import React, { useState, useCallback, useMemo } from 'react';
import { Calendar, momentLocalizer, Views } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import './calendar-styles.css';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import type { TechnicianSchedule, Client, Technician, User } from '@shared/schema';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, Clock, MapPin, User as UserIcon } from 'lucide-react';

const locales = {
  'pt-BR': ptBR,
};

const localizer = momentLocalizer({
  format: (date: Date, formatStr: string) => format(date, formatStr, { locale: ptBR }),
  parse: (dateStr: string, formatStr: string) => parse(dateStr, formatStr, new Date(), { locale: ptBR }),
  startOfWeek: () => startOfWeek(new Date(), { locale: ptBR }),
  getDay: (date: Date) => getDay(date),
  locales,
});

interface ScheduleEvent {
  id: number;
  title: string;
  start: Date;
  end: Date;
  resource: TechnicianSchedule;
}

interface TechnicianScheduleCalendarProps {
  technicianId?: number;
  onSelectEvent?: (event: ScheduleEvent) => void;
  onSelectSlot?: (slotInfo: { start: Date; end: Date }) => void;
  className?: string;
}

const statusColors = {
  scheduled: '#3B82F6',
  in_progress: '#F59E0B',
  completed: '#10B981',
  cancelled: '#EF4444'
};

const messages = {
  allDay: 'Todo o dia',
  previous: 'Anterior',
  next: 'Próximo',
  today: 'Hoje',
  month: 'Mês',
  week: 'Semana',
  day: 'Dia',
  agenda: 'Agenda',
  date: 'Data',
  time: 'Hora',
  event: 'Evento',
  noEventsInRange: 'Não há eventos neste período',
  showMore: (total: number) => `+${total} mais`
};

export default function TechnicianScheduleCalendar({ 
  technicianId,
  onSelectEvent, 
  onSelectSlot,
  className = ""
}: TechnicianScheduleCalendarProps) {
  const [currentView, setCurrentView] = useState(Views.MONTH);
  const [currentDate, setCurrentDate] = useState(new Date());

  const { data: schedules = [], isLoading } = useQuery({
    queryKey: ["/api/technician-schedules", technicianId],
    queryFn: async () => {
      const url = technicianId 
        ? `/api/technician-schedules?technicianId=${technicianId}`
        : "/api/technician-schedules";
      const response = await apiRequest("GET", url);
      return response.json();
    }
  });

  const { data: clients = [] } = useQuery({
    queryKey: ["/api/clients"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/clients");
      return response.json();
    }
  });

  const { data: technicians = [] } = useQuery({
    queryKey: ["/api/technicians"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/technicians");
      return response.json();
    }
  });

  const { data: users = [] } = useQuery({
    queryKey: ["/api/users"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/users");
      return response.json();
    }
  });

  const events: ScheduleEvent[] = useMemo(() => {
    return schedules.map((schedule: TechnicianSchedule) => {
      const scheduleDate = new Date(schedule.scheduleDate);
      const startTime = schedule.startTime.split(':');
      const endTime = schedule.endTime.split(':');
      
      const start = new Date(scheduleDate);
      start.setHours(parseInt(startTime[0]), parseInt(startTime[1]));
      
      const end = new Date(scheduleDate);
      end.setHours(parseInt(endTime[0]), parseInt(endTime[1]));

      return {
        id: schedule.id,
        title: schedule.title,
        start,
        end,
        resource: schedule
      };
    });
  }, [schedules]);

  const handleSelectEvent = useCallback((event: ScheduleEvent) => {
    if (onSelectEvent) {
      onSelectEvent(event);
    }
  }, [onSelectEvent]);

  const handleSelectSlot = useCallback((slotInfo: { start: Date; end: Date }) => {
    if (onSelectSlot) {
      onSelectSlot(slotInfo);
    }
  }, [onSelectSlot]);

  const eventStyleGetter = useCallback((event: ScheduleEvent) => {
    const schedule = event.resource;
    const backgroundColor = statusColors[schedule.status as keyof typeof statusColors] || '#6B7280';
    
    return {
      style: {
        backgroundColor,
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block',
        fontSize: '13px',
        fontWeight: '500'
      }
    };
  }, []);

  const CustomEvent = ({ event }: { event: ScheduleEvent }) => {
    const schedule = event.resource;
    const client = clients.find((c: Client) => c.id === schedule.clientId);
    const technician = technicians.find((t: Technician) => t.id === schedule.technicianId);
    const user = technician ? users.find((u: User) => u.id === technician.userId) : null;
    
    return (
      <div className="p-1">
        <div className="font-medium text-xs truncate">{event.title}</div>
        {!technicianId && user && (
          <div className="text-xs opacity-90 truncate">{user.name}</div>
        )}
        {client && (
          <div className="text-xs opacity-90 truncate">{client.name}</div>
        )}
        {schedule.location && (
          <div className="text-xs opacity-90 truncate">{schedule.location}</div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="w-5 h-5" />
            Calendário de Agendamentos do Técnico
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarIcon className="w-5 h-5" />
          {technicianId ? 'Calendário do Técnico' : 'Calendário de Agendamentos dos Técnicos'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex flex-wrap gap-2">
          <div className="flex items-center gap-2 text-sm">
            <div className="w-3 h-3 rounded bg-blue-500"></div>
            <span>Agendado</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <div className="w-3 h-3 rounded bg-yellow-500"></div>
            <span>Em Andamento</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <div className="w-3 h-3 rounded bg-green-500"></div>
            <span>Concluído</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <div className="w-3 h-3 rounded bg-red-500"></div>
            <span>Cancelado</span>
          </div>
        </div>
        
        <div style={{ height: '600px' }}>
          <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: '100%' }}
            onSelectEvent={handleSelectEvent}
            onSelectSlot={handleSelectSlot}
            selectable
            views={[Views.MONTH, Views.WEEK, Views.DAY, Views.AGENDA]}
            view={currentView}
            onView={setCurrentView}
            date={currentDate}
            onNavigate={setCurrentDate}
            messages={messages}
            eventPropGetter={eventStyleGetter}
            components={{
              event: CustomEvent
            }}
            formats={{
              dateFormat: 'dd',
              dayFormat: (date: Date) => format(date, 'dd/MM', { locale: ptBR }),
              weekdayFormat: (date: Date) => format(date, 'EEEE', { locale: ptBR }),
              monthHeaderFormat: (date: Date) => format(date, 'MMMM yyyy', { locale: ptBR }),
              dayHeaderFormat: (date: Date) => format(date, 'EEEE, dd MMMM yyyy', { locale: ptBR }),
              dayRangeHeaderFormat: ({ start, end }: { start: Date; end: Date }) => 
                `${format(start, 'dd MMM', { locale: ptBR })} - ${format(end, 'dd MMM yyyy', { locale: ptBR })}`,
              agendaHeaderFormat: ({ start, end }: { start: Date; end: Date }) => 
                `${format(start, 'dd MMM', { locale: ptBR })} - ${format(end, 'dd MMM yyyy', { locale: ptBR })}`,
              agendaDateFormat: (date: Date) => format(date, 'EEE dd/MM', { locale: ptBR }),
              agendaTimeFormat: (date: Date) => format(date, 'HH:mm', { locale: ptBR }),
              agendaTimeRangeFormat: ({ start, end }: { start: Date; end: Date }) => 
                `${format(start, 'HH:mm', { locale: ptBR })} - ${format(end, 'HH:mm', { locale: ptBR })}`
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}