import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, Download, Calendar, Filter, BarChart3, TrendingUp, Users, Wrench } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { ServiceOrder, Client, Technician, User } from "@/lib/types";
import { SERVICE_ORDER_STATUSES } from "@/lib/constants";

export default function Reports() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [dateRange, setDateRange] = useState({ start: "", end: "" });
  const [statusFilter, setStatusFilter] = useState("all");
  const [technicianFilter, setTechnicianFilter] = useState("all");

  const { data: serviceOrders = [] } = useQuery<ServiceOrder[]>({
    queryKey: ["/api/service-orders"],
  });

  const { data: clients = [] } = useQuery<Client[]>({
    queryKey: ["/api/clients"],
  });

  const { data: technicians = [] } = useQuery<Technician[]>({
    queryKey: ["/api/technicians"],
  });

  const { data: users = [] } = useQuery<User[]>({
    queryKey: ["/api/users"],
  });

  // Filtrar ordens baseado nos critérios
  const filteredOrders = serviceOrders.filter(order => {
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    const matchesTechnician = technicianFilter === "all" || order.technicianId?.toString() === technicianFilter;

    let matchesDate = true;
    if (dateRange.start && dateRange.end) {
      const orderDate = new Date(order.createdAt);
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);
      matchesDate = orderDate >= startDate && orderDate <= endDate;
    }

    return matchesStatus && matchesTechnician && matchesDate;
  });

  // Estatísticas
  const stats = {
    total: filteredOrders.length,
    completed: filteredOrders.filter(o => o.status === "completed").length,
    pending: filteredOrders.filter(o => o.status === "received" || o.status === "in_analysis").length,
    inProgress: filteredOrders.filter(o => o.status === "in_execution").length,
  };

  // Ordens por status
  const ordersByStatus = SERVICE_ORDER_STATUSES.map(status => ({
    status: status.label,
    count: filteredOrders.filter(o => o.status === status.value).length,
  }));

  // Ordens por técnico
  const ordersByTechnician = technicians.map(tech => {
    const user = users.find(u => u.id === tech.userId);
    return {
      technician: user?.name || "Desconhecido",
      count: filteredOrders.filter(o => o.technicianId === tech.id).length,
    };
  });

  // Função para exportar dados
  const exportToCSV = () => {
    const headers = ["Número OS", "Cliente", "Status", "Técnico", "Data Criação", "Data Conclusão"];
    const csvData = filteredOrders.map(order => {
      const client = clients.find(c => c.id === order.clientId);
      const technician = technicians.find(t => t.id === order.technicianId);
      const user = technician ? users.find(u => u.id === technician.userId) : null;

      return [
        order.orderNumber,
        client?.name || "N/A",
        SERVICE_ORDER_STATUSES.find(s => s.value === order.status)?.label || order.status,
        user?.name || "Não atribuído",
        format(new Date(order.createdAt), "dd/MM/yyyy", { locale: ptBR }),
        order.completedAt ? format(new Date(order.completedAt), "dd/MM/yyyy", { locale: ptBR }) : "Pendente"
      ];
    });

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `relatorio-ordens-servico-${format(new Date(), "yyyy-MM-dd")}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />

      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header
          title="Relatórios"
          onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="container mx-auto py-6 space-y-6">
            {/* Filtros */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filtros do Relatório
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Data Início</label>
                    <Input
                      type="date"
                      value={dateRange.start}
                      onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Data Fim</label>
                    <Input
                      type="date"
                      value={dateRange.end}
                      onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Status</label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos</SelectItem>
                        {SERVICE_ORDER_STATUSES.map(status => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Técnico</label>
                    <Select value={technicianFilter} onValueChange={setTechnicianFilter}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos</SelectItem>
                        {technicians.map(tech => {
                          const user = users.find(u => u.id === tech.userId);
                          return (
                            <SelectItem key={tech.id} value={tech.id.toString()}>
                              {user?.name || `Técnico ${tech.id}`}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="mt-4">
                  <Button onClick={exportToCSV} className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Exportar CSV
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Estatísticas Gerais */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total de Ordens</p>
                      <p className="text-2xl font-bold">{stats.total}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Concluídas</p>
                      <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Em Andamento</p>
                      <p className="text-2xl font-bold text-orange-600">{stats.inProgress}</p>
                    </div>
                    <Wrench className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pendentes</p>
                      <p className="text-2xl font-bold text-red-600">{stats.pending}</p>
                    </div>
                    <Users className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Distribuição por Status */}
            <Card>
              <CardHeader>
                <CardTitle>Distribuição por Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {ordersByStatus.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.status}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${stats.total > 0 ? (item.count / stats.total) * 100 : 0}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-bold w-8 text-right">{item.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Produtividade por Técnico */}
            <Card>
              <CardHeader>
                <CardTitle>Ordens por Técnico</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {ordersByTechnician.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.technician}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: `${stats.total > 0 ? (item.count / stats.total) * 100 : 0}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-bold w-8 text-right">{item.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Lista Detalhada */}
            <Card>
              <CardHeader>
                <CardTitle>Lista Detalhada de Ordens</CardTitle>
                <CardDescription>
                  {filteredOrders.length} ordens encontradas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Número OS</th>
                        <th className="text-left p-2">Cliente</th>
                        <th className="text-left p-2">Status</th>
                        <th className="text-left p-2">Técnico</th>
                        <th className="text-left p-2">Data Criação</th>
                        <th className="text-left p-2">Data Conclusão</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredOrders.map(order => {
                        const client = clients.find(c => c.id === order.clientId);
                        const technician = technicians.find(t => t.id === order.technicianId);
                        const user = technician ? users.find(u => u.id === technician.userId) : null;

                        return (
                          <tr key={order.id} className="border-b hover:bg-gray-50">
                            <td className="p-2 font-medium">{order.orderNumber}</td>
                            <td className="p-2">{client?.name || "N/A"}</td>
                            <td className="p-2">
                              <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                                {SERVICE_ORDER_STATUSES.find(s => s.value === order.status)?.label || order.status}
                              </span>
                            </td>
                            <td className="p-2">{user?.name || "Não atribuído"}</td>
                            <td className="p-2">{format(new Date(order.createdAt), "dd/MM/yyyy", { locale: ptBR })}</td>
                            <td className="p-2">
                              {order.completedAt ? format(new Date(order.completedAt), "dd/MM/yyyy", { locale: ptBR }) : "Pendente"}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}