import { useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Equipment, Client, EquipmentCategory } from "@/lib/types";
import { Eye, Plus, Search, ArrowUpDown } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { formatDate } from "@/lib/utils";

export default function EquipmentPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<string>("brand");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const { data: equipment = [], isLoading: isLoadingEquipment } = useQuery<Equipment[]>({
    queryKey: ['/api/equipment'],
  });

  const { data: clients = [], isLoading: isLoadingClients } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
  });

  const { data: categories = [], isLoading: isLoadingCategories } = useQuery<EquipmentCategory[]>({
    queryKey: ['/api/equipment-categories'],
  });

  const isLoading = isLoadingEquipment || isLoadingClients || isLoadingCategories;

  const getClientName = (clientId: number) => {
    const client = clients.find(c => c.id === clientId);
    return client?.name || 'Unknown Client';
  };

  const getCategoryName = (categoryId?: number) => {
    if (!categoryId) return 'Uncategorized';
    const category = categories.find(c => c.id === categoryId);
    return category?.name || 'Unknown Category';
  };

  // Filter equipment based on search, status, and category
  const filteredEquipment = equipment.filter(item => {
    const matchesSearch = 
      (item.brand || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.model || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.serialNumber || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      getClientName(item.clientId).toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = 
      statusFilter === "all" || 
      item.status === statusFilter;

    const matchesCategory =
      categoryFilter === "all" ||
      (categoryFilter === "uncategorized" && !item.categoryId) ||
      (item.categoryId?.toString() === categoryFilter);
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  // Sort equipment
  const sortedEquipment = [...filteredEquipment].sort((a, b) => {
    let result = 0;
    
    switch (sortBy) {
      case "brand":
        result = (a.brand || '').localeCompare(b.brand || '');
        break;
      case "model":
        result = (a.model || '').localeCompare(b.model || '');
        break;
      case "serialNumber":
        result = (a.serialNumber || '').localeCompare(b.serialNumber || '');
        break;
      case "client":
        result = getClientName(a.clientId).localeCompare(getClientName(b.clientId));
        break;
      case "category":
        result = getCategoryName(a.categoryId).localeCompare(getCategoryName(b.categoryId));
        break;
      case "status":
        result = (a.status || '').localeCompare(b.status || '');
        break;
      case "purchaseDate":
        const dateA = a.purchaseDate ? new Date(a.purchaseDate).getTime() : 0;
        const dateB = b.purchaseDate ? new Date(b.purchaseDate).getTime() : 0;
        result = dateA - dateB;
        break;
      default:
        result = (a.brand || '').localeCompare(b.brand || '');
    }
    
    return sortOrder === "asc" ? result : -result;
  });

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Equipamentos" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-medium text-gray-800 mb-2 md:mb-0">Todos os Equipamentos</h3>
              <div className="flex flex-col md:flex-row gap-2">
                <Link href="/equipment/new">
                  <Button className="bg-primary hover:bg-primary-dark">
                    <Plus className="h-4 w-4 mr-1" />
                    Novo Equipamento
                  </Button>
                </Link>
              </div>
            </div>
            
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar equipamentos..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filtrar por status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Status</SelectItem>
                  <SelectItem value="active">Ativo</SelectItem>
                  <SelectItem value="inactive">Inativo</SelectItem>
                  <SelectItem value="maintenance">Em Manutenção</SelectItem>
                  <SelectItem value="retired">Descontinuado</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filtrar por categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as Categorias</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                  <SelectItem value="uncategorized">Sem Categoria</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="brand">Marca</SelectItem>
                  <SelectItem value="model">Modelo</SelectItem>
                  <SelectItem value="serialNumber">Número de Série</SelectItem>
                  <SelectItem value="client">Cliente</SelectItem>
                  <SelectItem value="category">Categoria</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                  <SelectItem value="purchaseDate">Data de Compra</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="overflow-x-auto">
              {isLoading ? (
                <div className="p-8 flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : filteredEquipment.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  Nenhum equipamento encontrado. Adicione seu primeiro equipamento!
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]"></TableHead>
                      <TableHead className="w-[180px]">
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("brand");
                            toggleSortOrder();
                          }}
                        >
                          Marca
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("model");
                            toggleSortOrder();
                          }}
                        >
                          Modelo
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("serialNumber");
                            toggleSortOrder();
                          }}
                        >
                          Número de Série
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("client");
                            toggleSortOrder();
                          }}
                        >
                          Cliente
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("category");
                            toggleSortOrder();
                          }}
                        >
                          Categoria
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div
                          className="flex items-center cursor-pointer"
                          onClick={() => {
                            setSortBy("status");
                            toggleSortOrder();
                          }}
                        >
                          Status
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedEquipment.map((item) => (
                      <TableRow key={item.id} className="hover:bg-gray-50">
                        <TableCell className="text-center">
                          <Link href={`/equipment/${item.id}`}>
                            <Button variant="ghost" size="sm" className="text-primary hover:text-primary-dark">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                        </TableCell>
                        <TableCell className="font-medium">{item.brand || '-'}</TableCell>
                        <TableCell>{item.model || '-'}</TableCell>
                        <TableCell>{item.serialNumber || '-'}</TableCell>
                        <TableCell>{getClientName(item.clientId)}</TableCell>
                        <TableCell>{getCategoryName(item.categoryId)}</TableCell>
                        <TableCell>
                          <Badge
                            variant={item.status === "active" ? "default" : "secondary"}
                            className={
                              item.status === "active"
                                ? "bg-green-100 text-green-800"
                                : item.status === "inactive"
                                ? "bg-gray-100 text-gray-800"
                                : item.status === "maintenance"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }
                          >
                            {item.status === "active" ? "Ativo" : 
                             item.status === "inactive" ? "Inativo" : 
                             item.status === "maintenance" ? "Em Manutenção" : 
                             item.status === "retired" ? "Descontinuado" : 
                             'Desconhecido'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
            
            <div className="px-6 py-3 border-t border-gray-200 bg-gray-50 text-sm text-gray-500">
              Mostrando {filteredEquipment.length} de {equipment.length} equipamentos no total
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
