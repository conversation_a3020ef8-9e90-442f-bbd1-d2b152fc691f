import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { CreditCard, AlertCircle, Loader2, CheckCircle, BarChart3, Users, Package, Wrench } from "lucide-react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { useToast } from "@/hooks/use-toast";

export default function BillingPortal() {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  
  const { data: systemInfo, isLoading: isLoadingSystem, error } = useQuery({
    queryKey: ["/api/system-info"],
    retry: false,
  });

  const handleSubscribe = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      
      if (!response.ok) {
        throw new Error("Erro ao criar sessão de checkout");
      }
      
      const { url } = await response.json();
      window.location.href = url;
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast({
        title: "Erro",
        description: "Não foi possível iniciar o processo de assinatura. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleManageSubscription = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/create-portal-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      
      if (!response.ok) {
        throw new Error("Erro ao criar portal de gerenciamento");
      }
      
      const { url } = await response.json();
      window.location.href = url;
    } catch (error) {
      console.error("Error creating portal session:", error);
      toast({
        title: "Erro",
        description: "Não foi possível acessar o portal de gerenciamento. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingSystem) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Informações do Sistema" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Informações do Sistema" />
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erro ao carregar informações do sistema</AlertTitle>
              <AlertDescription>
                Não foi possível carregar as informações do sistema. Verifique sua conexão ou entre em contato com o suporte.
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Informações do Sistema" />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-medium text-gray-800">Informações do Sistema</h3>
                <p className="text-sm text-gray-500">Gerencie seu sistema e método de pagamento</p>
              </div>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Status do Sistema */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-lg font-medium">Status do Sistema</CardTitle>
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Plano Atual</span>
                          <Badge variant="default">Sistema Local de Gestão</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Versão</span>
                          <span className="text-sm text-gray-600">1.0.0</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Status</span>
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            Operacional
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Stripe Configurado</span>
                          <Badge variant="default">Sim</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Estatísticas do Sistema */}
                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle className="text-lg font-medium">Estatísticas do Sistema</CardTitle>
                      <CardDescription>Resumo dos dados no seu sistema</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-blue-100 rounded-md">
                            <Wrench className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Ordens de Serviço</p>
                            <p className="text-2xl font-bold">22</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-green-100 rounded-md">
                            <Users className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Clientes</p>
                            <p className="text-2xl font-bold">16</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-purple-100 rounded-md">
                            <Package className="h-4 w-4 text-purple-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Equipamentos</p>
                            <p className="text-2xl font-bold">13</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-orange-100 rounded-md">
                            <BarChart3 className="h-4 w-4 text-orange-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Itens de Estoque</p>
                            <p className="text-2xl font-bold">11</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Recursos do Sistema */}
                <div>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg font-medium">Recursos Disponíveis</CardTitle>
                      <CardDescription>Funcionalidades incluídas no seu sistema</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {[
                          "Gestão de Ordens de Serviço",
                          "Controle de Clientes", 
                          "Gestão de Estoque",
                          "Relatórios Avançados",
                          "Sistema de Pagamentos"
                        ].map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-sm">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Pagamentos */}
                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle className="text-lg font-medium">Gerenciar Pagamentos</CardTitle>
                      <CardDescription>Configure e gerencie métodos de pagamento</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <Button
                        onClick={handleSubscribe}
                        disabled={isLoading}
                        className="w-full"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <CreditCard className="h-4 w-4 mr-2" />
                        )}
                        Assinar Plano Pro
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={handleManageSubscription}
                        disabled={isLoading}
                        className="w-full"
                      >
                        Gerenciar Assinatura
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}