import { Router, Request, Response } from 'express';
import { whatsappService } from '../services/whatsapp';

// Criar o router para manipular todas as rotas relacionadas ao WhatsApp
const whatsappRouter = Router();

/**
 * Rota para verificar o status da configuração do WhatsApp
 * GET /api/whatsapp/status
 */
whatsappRouter.get('/status', (req: Request, res: Response) => {
  const isConfigured = whatsappService.checkConfiguration();
  res.json({
    isConfigured,
    status: isConfigured ? 'configured' : 'not_configured'
  });
});

/**
 * Rota para atualizar as configurações do WhatsApp
 * POST /api/whatsapp/config
 */
whatsappRouter.post('/config', (req: Request, res: Response) => {
  try {
    const { apiToken, phoneNumberId } = req.body;
    
    if (!apiToken || !phoneNumberId) {
      return res.status(400).json({
        success: false,
        message: 'API token and phone number ID are required'
      });
    }
    
    whatsappService.updateConfiguration(apiToken, phoneNumberId);
    
    // Se chegamos aqui, a configuração foi bem-sucedida
    res.json({
      success: true,
      message: 'WhatsApp configuration updated successfully'
    });
  } catch (error) {
    console.error('Error updating WhatsApp configuration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update WhatsApp configuration',
      error: (error as Error).message
    });
  }
});

/**
 * Rota para enviar uma mensagem de teste
 * POST /api/whatsapp/test-message
 */
whatsappRouter.post('/test-message', async (req: Request, res: Response) => {
  try {
    const { to, message } = req.body;
    
    if (!to || !message) {
      return res.status(400).json({
        success: false,
        message: 'Phone number and message are required'
      });
    }
    
    // Verificar se o serviço está configurado
    if (!whatsappService.checkConfiguration()) {
      return res.status(400).json({
        success: false,
        message: 'WhatsApp service is not configured'
      });
    }
    
    // Tentar enviar a mensagem
    const result = await whatsappService.sendTextMessage(to, message);
    
    res.json({
      success: true,
      message: 'Test message sent successfully',
      data: result
    });
  } catch (error) {
    console.error('Error sending test message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test message',
      error: (error as Error).message
    });
  }
});

/**
 * Webhook para receber atualizações do WhatsApp
 * GET /api/whatsapp/webhook (para verificação do webhook)
 * POST /api/whatsapp/webhook (para receber notificações)
 */
whatsappRouter.get('/webhook', (req: Request, res: Response) => {
  // Lógica para verificação de webhook da API do WhatsApp
  // Esta é a implementação padrão de verificação de webhook do Facebook/WhatsApp
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  // Você definirá esta verificação de token no painel do WhatsApp
  // e deve ser armazenada como uma variável de ambiente
  const verifyToken = process.env.WHATSAPP_VERIFY_TOKEN || 'default_verify_token';

  if (mode === 'subscribe' && token === verifyToken) {
    // Webhook verificado com sucesso
    console.log('WhatsApp webhook verified successfully');
    res.status(200).send(challenge);
  } else {
    // Verificação falhou
    console.error('WhatsApp webhook verification failed');
    res.sendStatus(403);
  }
});

whatsappRouter.post('/webhook', (req: Request, res: Response) => {
  // Lógica para processar notificações do WhatsApp
  try {
    const body = req.body;

    console.log('Received webhook data:', JSON.stringify(body, null, 2));

    // Verificar se é uma notificação válida do WhatsApp
    if (body.object === 'whatsapp_business_account') {
      // Processar as mensagens recebidas
      if (body.entry && body.entry.length > 0) {
        body.entry.forEach((entry: any) => {
          const changes = entry.changes;
          if (changes && changes.length > 0) {
            const value = changes[0].value;
            if (value.messages && value.messages.length > 0) {
              // Processar cada mensagem recebida
              value.messages.forEach((message: any) => {
                console.log('Received message:', message);
                // Aqui você pode adicionar lógica para processar diferentes tipos de mensagens
                // como texto, mídia, botões interativos, etc.
              });
            }
          }
        });
      }

      // Sempre retornar um código 200 para o webhook
      res.sendStatus(200);
    } else {
      // Não é uma notificação do WhatsApp
      res.sendStatus(404);
    }
  } catch (error) {
    console.error('Error processing webhook:', error);
    // Ainda retornamos 200 para evitar reenvios
    res.sendStatus(200);
  }
});

/**
 * Rota para enviar notificação de status de ordem de serviço
 * POST /api/whatsapp/notify/service-order-status
 */
whatsappRouter.post('/notify/service-order-status', async (req: Request, res: Response) => {
  try {
    const { to, orderNumber, status, clientName } = req.body;
    
    if (!to || !orderNumber || !status || !clientName) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters'
      });
    }
    
    // Verificar se o serviço está configurado
    if (!whatsappService.checkConfiguration()) {
      return res.status(400).json({
        success: false,
        message: 'WhatsApp service is not configured'
      });
    }
    
    // Enviar a notificação
    const result = await whatsappService.sendServiceOrderStatusNotification(
      to, orderNumber, status, clientName
    );
    
    res.json({
      success: true,
      message: 'Service order status notification sent successfully',
      data: result
    });
  } catch (error) {
    console.error('Error sending service order status notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send service order status notification',
      error: (error as Error).message
    });
  }
});

/**
 * Rota para enviar notificação de agendamento
 * POST /api/whatsapp/notify/appointment
 */
whatsappRouter.post('/notify/appointment', async (req: Request, res: Response) => {
  try {
    const { to, clientName, date, time, service } = req.body;
    
    if (!to || !clientName || !date || !time || !service) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters'
      });
    }
    
    // Verificar se o serviço está configurado
    if (!whatsappService.checkConfiguration()) {
      return res.status(400).json({
        success: false,
        message: 'WhatsApp service is not configured'
      });
    }
    
    // Enviar a notificação
    const result = await whatsappService.sendAppointmentNotification(
      to, clientName, date, time, service
    );
    
    res.json({
      success: true,
      message: 'Appointment notification sent successfully',
      data: result
    });
  } catch (error) {
    console.error('Error sending appointment notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send appointment notification',
      error: (error as Error).message
    });
  }
});

/**
 * Rota para enviar notificação de orçamento pronto
 * POST /api/whatsapp/notify/quote-ready
 */
whatsappRouter.post('/notify/quote-ready', async (req: Request, res: Response) => {
  try {
    const { to, clientName, quoteNumber, totalAmount, validUntil } = req.body;
    
    if (!to || !clientName || !quoteNumber || !totalAmount || !validUntil) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters'
      });
    }
    
    // Verificar se o serviço está configurado
    if (!whatsappService.checkConfiguration()) {
      return res.status(400).json({
        success: false,
        message: 'WhatsApp service is not configured'
      });
    }
    
    // Enviar a notificação
    const result = await whatsappService.sendQuoteReadyNotification(
      to, clientName, quoteNumber, totalAmount, validUntil
    );
    
    res.json({
      success: true,
      message: 'Quote ready notification sent successfully',
      data: result
    });
  } catch (error) {
    console.error('Error sending quote ready notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send quote ready notification',
      error: (error as Error).message
    });
  }
});

export default whatsappRouter;