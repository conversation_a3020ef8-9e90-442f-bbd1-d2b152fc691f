import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useLocation } from 'wouter';
import { Plus, Pencil, Trash2, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { formatCurrency } from '@/lib/utils';

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from '@/components/ui/skeleton';
import Sidebar from '@/components/layout/sidebar';
import Header from '@/components/layout/header';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface Part {
  id: number;
  name: string;
  brand: string;
  description: string | null;
  barcode: string;
  internalCode: string | null;
  sku: string | null;
  categoryId: number | null;
  purchaseValue: number;
  saleValue: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function PartsPage() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [partToDelete, setPartToDelete] = useState<Part | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentPart, setCurrentPart] = useState<Part | null>(null);

  // Query para buscar todas as peças
  const { data: parts, isLoading, error } = useQuery({
    queryKey: ['/api/parts'],
    select: (data: any) => data as Part[],
  });

  // Mutação para excluir uma peça
  const deletePartMutation = useMutation({
    mutationFn: (id: number) => apiRequest('DELETE', `/api/parts/${id}`),
    onSuccess: () => {
      toast({
        title: 'Peça excluída',
        description: 'A peça foi excluída com sucesso.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/parts'] });
      setDeleteConfirmOpen(false);
      setPartToDelete(null);
    },
    onError: (error) => {
      toast({
        title: 'Erro ao excluir peça',
        description: 'Ocorreu um erro ao excluir a peça. Tente novamente.',
        variant: 'destructive',
      });
    },
  });

  // Mutação para atualizar status de uma peça
  const updatePartStatusMutation = useMutation({
    mutationFn: ({ id, active }: { id: number; active: boolean }) => 
      apiRequest('PATCH', `/api/parts/${id}`, { active }),
    onSuccess: () => {
      toast({
        title: 'Status atualizado',
        description: 'O status da peça foi atualizado com sucesso.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/parts'] });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao atualizar status',
        description: 'Ocorreu um erro ao atualizar o status da peça. Tente novamente.',
        variant: 'destructive',
      });
    },
  });

  // Filtrar peças pelo termo de busca
  const filteredParts = parts?.filter(part => {
    const search = searchTerm.toLowerCase();
    return (
      part.name.toLowerCase().includes(search) ||
      (part.brand ? part.brand.toLowerCase().includes(search) : false) ||
      (part.description ? part.description.toLowerCase().includes(search) : false) ||
      (part.barcode ? part.barcode.toLowerCase().includes(search) : false) ||
      (part.internalCode ? part.internalCode.toLowerCase().includes(search) : false) ||
      (part.sku ? part.sku.toLowerCase().includes(search) : false)
    );
  });

  // Manipuladores de eventos
  const handleDeleteClick = (part: Part) => {
    setPartToDelete(part);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (partToDelete) {
      deletePartMutation.mutate(partToDelete.id);
    }
  };

  const handleToggleActive = (part: Part) => {
    updatePartStatusMutation.mutate({
      id: part.id,
      active: !part.active
    });
  };

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const isMobile = window.innerWidth < 768; // Simplificação para detectar dispositivos móveis
  
  // Função para formatar moeda em Reais
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value / 100);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Peças" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-medium text-gray-800">Todas as Peças</h3>
                <p className="text-sm text-gray-500">Gerenciamento de peças e componentes</p>
              </div>
              <div className="flex flex-col md:flex-row gap-2 mt-2 md:mt-0">
                <Button onClick={() => setLocation('/parts/new')} className="bg-primary hover:bg-primary-dark">
                  <Plus className="h-4 w-4 mr-1" />
                  {isMobile ? 'Nova' : 'Nova Peça'}
                </Button>
              </div>
            </div>
            
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <div className="relative w-full md:w-64">
                <Input
                  placeholder="Buscar peças..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div className="p-6">
              {isLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : error ? (
                <div className="text-center p-6 text-red-500">
                  <AlertTriangle className="mx-auto h-12 w-12 mb-4" />
                  <p>Erro ao carregar peças. Tente novamente mais tarde.</p>
                </div>
              ) : (
                <div className="overflow-auto">
                  <Table>
                    <TableCaption>Lista de peças cadastradas</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Nome</TableHead>
                        <TableHead>Marca</TableHead>
                        <TableHead>Descrição</TableHead>
                        <TableHead>Código de Barras</TableHead>
                        <TableHead>Código Interno</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>Valor de Compra</TableHead>
                        <TableHead>Valor de Venda</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredParts?.length ? (
                        filteredParts.map((part) => (
                          <TableRow key={part.id}>
                            <TableCell>{part.id}</TableCell>
                            <TableCell>{part.name}</TableCell>
                            <TableCell>{part.brand || '-'}</TableCell>
                            <TableCell>{part.description || '-'}</TableCell>
                            <TableCell>{part.barcode || '-'}</TableCell>
                            <TableCell>{part.internalCode || '-'}</TableCell>
                            <TableCell>{part.sku || '-'}</TableCell>
                            <TableCell>{formatCurrency(part.purchaseValue)}</TableCell>
                            <TableCell>{formatCurrency(part.saleValue)}</TableCell>
                            <TableCell>
                              <Badge 
                                className="cursor-pointer"
                                variant={part.active ? "default" : "outline"}
                                onClick={() => handleToggleActive(part)}
                              >
                                {part.active ? 'Ativo' : 'Inativo'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right space-x-2">
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/parts/${part.id}`}>
                                  <Pencil className="h-4 w-4" />
                                </Link>
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => handleDeleteClick(part)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={10} className="text-center">
                            Nenhuma peça encontrada.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Diálogo de confirmação de exclusão */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Você tem certeza?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta ação não pode ser desfeita. Isto irá excluir permanentemente a peça{' '}
              <strong>{partToDelete?.name}</strong> do sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}