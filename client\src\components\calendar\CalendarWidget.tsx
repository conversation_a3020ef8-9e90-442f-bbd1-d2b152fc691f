import React, { useMemo } from 'react';
import { Calendar, momentLocalizer, Views } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay, addDays, startOfDay, endOfDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import './calendar-styles.css';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import type { Appointment, TechnicianSchedule } from '@shared/schema';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CalendarIcon } from 'lucide-react';

const locales = {
  'pt-BR': ptBR,
};

const localizer = momentLocalizer({
  format: (date: Date, formatStr: string) => format(date, formatStr, { locale: ptBR }),
  parse: (dateStr: string, formatStr: string) => parse(dateStr, formatStr, new Date(), { locale: ptBR }),
  startOfWeek: () => startOfWeek(new Date(), { locale: ptBR }),
  getDay: (date: Date) => getDay(date),
  locales,
});

interface CalendarEvent {
  id: number;
  title: string;
  start: Date;
  end: Date;
  type: 'appointment' | 'schedule';
  resource: Appointment | TechnicianSchedule;
}

interface CalendarWidgetProps {
  className?: string;
  daysToShow?: number;
}

const messages = {
  allDay: 'Todo o dia',
  previous: 'Anterior',
  next: 'Próximo',
  today: 'Hoje',
  month: 'Mês',
  week: 'Semana',
  day: 'Dia',
  agenda: 'Agenda',
  date: 'Data',
  time: 'Hora',
  event: 'Evento',
  noEventsInRange: 'Não há eventos neste período',
  showMore: (total: number) => `+${total} mais`
};

export default function CalendarWidget({ 
  className = "",
  daysToShow = 7
}: CalendarWidgetProps) {
  const today = new Date();
  const endDate = addDays(today, daysToShow);

  const { data: appointments = [] } = useQuery({
    queryKey: ["/api/appointments"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/appointments");
      return response.json();
    }
  });

  const { data: schedules = [] } = useQuery({
    queryKey: ["/api/technician-schedules"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/technician-schedules");
      return response.json();
    }
  });

  const events: CalendarEvent[] = useMemo(() => {
    const appointmentEvents = appointments
      .filter((appointment: Appointment) => {
        const appointmentDate = new Date(appointment.appointmentDate);
        return appointmentDate >= startOfDay(today) && appointmentDate <= endOfDay(endDate);
      })
      .map((appointment: Appointment) => {
        const appointmentDate = new Date(appointment.appointmentDate);
        const startTime = appointment.startTime ? appointment.startTime.split(':') : ['09', '00'];
        const endTime = appointment.endTime ? appointment.endTime.split(':') : ['10', '00'];
        
        const start = new Date(appointmentDate);
        start.setHours(parseInt(startTime[0]), parseInt(startTime[1]));
        
        const end = new Date(appointmentDate);
        end.setHours(parseInt(endTime[0]), parseInt(endTime[1]));

        return {
          id: appointment.id,
          title: appointment.title,
          start,
          end,
          type: 'appointment' as const,
          resource: appointment
        };
      });

    const scheduleEvents = schedules
      .filter((schedule: TechnicianSchedule) => {
        const scheduleDate = new Date(schedule.scheduleDate);
        return scheduleDate >= startOfDay(today) && scheduleDate <= endOfDay(endDate);
      })
      .map((schedule: TechnicianSchedule) => {
        const scheduleDate = new Date(schedule.scheduleDate);
        const startTime = schedule.startTime.split(':');
        const endTime = schedule.endTime.split(':');
        
        const start = new Date(scheduleDate);
        start.setHours(parseInt(startTime[0]), parseInt(startTime[1]));
        
        const end = new Date(scheduleDate);
        end.setHours(parseInt(endTime[0]), parseInt(endTime[1]));

        return {
          id: schedule.id,
          title: `${schedule.title} (Técnico)`,
          start,
          end,
          type: 'schedule' as const,
          resource: schedule
        };
      });

    return [...appointmentEvents, ...scheduleEvents];
  }, [appointments, schedules, today, endDate]);

  const eventStyleGetter = React.useCallback((event: CalendarEvent) => {
    const backgroundColor = event.type === 'appointment' ? '#3B82F6' : '#10B981';
    
    return {
      style: {
        backgroundColor,
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block',
        fontSize: '12px',
        fontWeight: '500'
      }
    };
  }, []);

  const CustomEvent = ({ event }: { event: CalendarEvent }) => {
    return (
      <div className="p-1">
        <div className="font-medium text-xs truncate">{event.title}</div>
        <div className="text-xs opacity-90">
          {format(event.start, 'HH:mm', { locale: ptBR })}
        </div>
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <CalendarIcon className="w-5 h-5" />
          Próximos {daysToShow} dias
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-3 flex flex-wrap gap-2 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded bg-blue-500"></div>
            <span>Agendamentos</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded bg-green-500"></div>
            <span>Técnicos</span>
          </div>
        </div>
        
        <div style={{ height: '400px' }}>
          <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: '100%' }}
            views={[Views.AGENDA]}
            view={Views.AGENDA}
            date={today}
            length={daysToShow}
            messages={messages}
            eventPropGetter={eventStyleGetter}
            components={{
              event: CustomEvent
            }}
            toolbar={false}
            formats={{
              agendaDateFormat: (date: Date) => format(date, 'EEE dd/MM', { locale: ptBR }),
              agendaTimeFormat: (date: Date) => format(date, 'HH:mm', { locale: ptBR }),
              agendaTimeRangeFormat: ({ start, end }: { start: Date; end: Date }) => 
                `${format(start, 'HH:mm', { locale: ptBR })} - ${format(end, 'HH:mm', { locale: ptBR })}`
            }}
          />
        </div>
        
        {events.length === 0 && (
          <div className="text-center py-8 text-gray-500 text-sm">
            Nenhum evento nos próximos {daysToShow} dias
          </div>
        )}
      </CardContent>
    </Card>
  );
}