import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number | undefined | null): string {
  if (amount === undefined || amount === null) return "N/A";
  
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(amount / 100);
}

export function formatDateTime(
  date: string | Date | undefined | null,
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric', 
    hour: '2-digit', 
    minute: '2-digit' 
  }
): string {
  if (!date) return '';
  return new Intl.DateTimeFormat('pt-BR', options).format(new Date(date));
}

export function formatDate(
  date: string | Date | undefined | null,
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric'
  }
): string {
  if (!date) return '';
  return new Intl.DateTimeFormat('pt-BR', options).format(new Date(date));
}

export function getStatusColor(status: string): {
  bg: string;
  text: string;
} {
  switch (status.toLowerCase()) {
    case 'received':
      return { bg: 'bg-gray-200', text: 'text-gray-800' };
    case 'in_analysis':
      return { bg: 'bg-yellow-100', text: 'text-yellow-800' };
    case 'waiting_parts':
      return { bg: 'bg-blue-100', text: 'text-blue-800' };
    case 'waiting_approval':
      return { bg: 'bg-red-100', text: 'text-red-800' };
    case 'in_execution':
    case 'in_repair':
      return { bg: 'bg-indigo-100', text: 'text-indigo-800' };
    case 'completed':
      return { bg: 'bg-green-100', text: 'text-green-800' };
    case 'delivered':
      return { bg: 'bg-purple-100', text: 'text-purple-800' };
    default:
      return { bg: 'bg-gray-100', text: 'text-gray-800' };
  }
}

export function getStatusLabel(status: string): string {
  switch (status.toLowerCase()) {
    case 'received':
      return 'Recebido';
    case 'in_analysis':
      return 'Em Análise';
    case 'waiting_parts':
      return 'Aguardando Peças';
    case 'waiting_approval':
      return 'Aguardando Aprovação';
    case 'in_execution':
    case 'in_repair':
      return 'Em Execução';
    case 'completed':
      return 'Concluído';
    case 'delivered':
      return 'Entregue';
    default:
      return status;
  }
}

export function generateOrderNumber(): string {
  // Formato: OS-NUMERO-DDMMAAAA
  const today = new Date();
  const day = String(today.getDate()).padStart(2, '0');
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const year = today.getFullYear();
  const formattedDate = `${day}${month}${year}`;
  
  return `OS-${String(Math.floor(1000 + Math.random() * 9000)).padStart(6, '0')}-${formattedDate}`;
}

export function truncateText(text: string, maxLength: number): string {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout>;
  
  return function(...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      fn(...args);
    }, delay);
  };
}

export function calculateTotal(items: { quantity: number; unitPrice: number }[]): number {
  return items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
}
