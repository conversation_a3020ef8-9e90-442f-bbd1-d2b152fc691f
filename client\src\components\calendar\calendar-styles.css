/* Custom styles for React Big Calendar to match the application theme */

.rbc-calendar {
  font-family: inherit;
  font-size: 14px;
}

.rbc-header {
  background-color: #f8fafc;
  border-color: #e2e8f0;
  font-weight: 600;
  color: #374151;
  padding: 8px 12px;
}

.rbc-month-view {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.rbc-day-bg {
  background-color: #ffffff;
}

.rbc-day-bg.rbc-off-range-bg {
  background-color: #f9fafb;
}

.rbc-today {
  background-color: #eff6ff !important;
}

.rbc-event {
  border-radius: 4px;
  border: none !important;
  outline: none;
  padding: 2px 5px;
  font-weight: 500;
  font-size: 12px;
}

.rbc-event:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 1px;
}

.rbc-event.rbc-selected {
  box-shadow: 0 0 0 2px #1d4ed8;
}

.rbc-show-more {
  background-color: #6b7280;
  color: white;
  border-radius: 3px;
  padding: 1px 4px;
  font-size: 11px;
  font-weight: 500;
}

.rbc-date-cell {
  padding: 5px 7px;
  text-align: right;
}

.rbc-date-cell > a {
  color: #374151;
}

.rbc-date-cell > a:hover {
  color: #1f2937;
}

.rbc-off-range {
  color: #9ca3af;
}

.rbc-current {
  color: #3b82f6 !important;
  font-weight: 700;
}

.rbc-toolbar {
  margin-bottom: 20px;
  padding: 0 10px;
}

.rbc-toolbar button {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.rbc-toolbar button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.rbc-toolbar button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.rbc-toolbar button.rbc-active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.rbc-toolbar button.rbc-active:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

.rbc-toolbar-label {
  font-weight: 600;
  color: #111827;
  font-size: 18px;
}

/* Week and Day view styles */
.rbc-time-view {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.rbc-time-header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.rbc-time-content {
  background-color: #ffffff;
}

.rbc-time-slot {
  border-top: 1px solid #f1f5f9;
}

.rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-time-gutter .rbc-timeslot-group {
  border-right: 1px solid #e2e8f0;
}

.rbc-current-time-indicator {
  background-color: #ef4444;
  height: 2px;
}

/* Agenda view styles */
.rbc-agenda-view {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.rbc-agenda-view table {
  width: 100%;
}

.rbc-agenda-view .rbc-agenda-date-cell,
.rbc-agenda-view .rbc-agenda-time-cell {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 8px 12px;
  font-weight: 600;
  color: #374151;
}

.rbc-agenda-view .rbc-agenda-event-cell {
  padding: 8px 12px;
  border-bottom: 1px solid #f1f5f9;
}

.rbc-agenda-view tbody > tr:last-child .rbc-agenda-date-cell,
.rbc-agenda-view tbody > tr:last-child .rbc-agenda-time-cell,
.rbc-agenda-view tbody > tr:last-child .rbc-agenda-event-cell {
  border-bottom: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-toolbar {
    flex-direction: column;
    gap: 10px;
  }
  
  .rbc-toolbar .rbc-btn-group {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  
  .rbc-toolbar-label {
    text-align: center;
    font-size: 16px;
  }
  
  .rbc-event {
    font-size: 11px;
    padding: 1px 3px;
  }
}