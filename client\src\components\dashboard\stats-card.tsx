import { cn } from "@/lib/utils";
import { StatCard } from "@/lib/types";

interface StatsCardProps {
  card: StatCard;
}

export default function StatsCard({ card }: StatsCardProps) {
  const getChangeColorClass = () => {
    switch (card.changeColor) {
      case 'success':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'danger':
        return 'text-red-600';
      default:
        return 'text-green-600';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center">
        <div className={cn("rounded-md p-3 bg-opacity-10", card.iconBgColor)}>
          {card.icon}
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">{card.title}</h3>
          <p className="text-2xl font-semibold text-gray-800">{card.value}</p>
        </div>
      </div>
      {(card.change || card.changeText) && (
        <div className="mt-3 flex items-center text-sm">
          {card.change && (
            <span className={cn("font-medium", getChangeColorClass())}>
              {card.change}
            </span>
          )}
          {card.changeText && (
            <span className="text-gray-500 ml-2">{card.changeText}</span>
          )}
        </div>
      )}
    </div>
  );
}