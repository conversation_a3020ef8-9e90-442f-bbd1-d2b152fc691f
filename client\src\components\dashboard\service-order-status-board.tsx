import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Link } from "wouter";
import { 
  ClipboardList, 
  Clock, 
  Wrench, 
  Package, 
  CheckCircle, 
  Truck,
  AlertTriangle,
  Plus,
  Eye,
  Search
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { ServiceOrder } from "@/lib/types";
import { formatDateTime } from "@/lib/utils";

export default function ServiceOrderStatusBoard() {
  const { data: serviceOrders = [] } = useQuery<ServiceOrder[]>({
    queryKey: ['/api/service-orders'],
  });

  const statusConfig = {
    received: {
      label: 'Recebidas',
      icon: ClipboardList,
      color: 'bg-blue-500',
      textColor: 'text-blue-700',
      bgColor: 'bg-blue-50'
    },
    in_analysis: {
      label: '<PERSON>',
      icon: Clock,
      color: 'bg-purple-500',
      textColor: 'text-purple-700',
      bgColor: 'bg-purple-50'
    },
    waiting_parts: {
      label: 'Aguardando Peças',
      icon: Package,
      color: 'bg-orange-500',
      textColor: 'text-orange-700',
      bgColor: 'bg-orange-50'
    },
    waiting_approval: {
      label: 'Aguardando Aprovação',
      icon: Clock,
      color: 'bg-amber-500',
      textColor: 'text-amber-700',
      bgColor: 'bg-amber-50'
    },
    in_execution: {
      label: 'Em Execução',
      icon: Wrench,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-700',
      bgColor: 'bg-yellow-50'
    },
    completed: {
      label: 'Concluídas',
      icon: CheckCircle,
      color: 'bg-green-500',
      textColor: 'text-green-700',
      bgColor: 'bg-green-50'
    },
    delivered: {
      label: 'Entregues',
      icon: Truck,
      color: 'bg-gray-500',
      textColor: 'text-gray-700',
      bgColor: 'bg-gray-50'
    }
  };

  const groupedOrders = Object.keys(statusConfig).reduce((acc, status) => {
    acc[status] = serviceOrders.filter(order => order.status === status);
    return acc;
  }, {} as Record<string, ServiceOrder[]>);

  const getUrgencyBadge = (order: ServiceOrder) => {
    const createdDate = new Date(order.createdAt);
    const daysDiff = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff > 7) {
      return <Badge variant="destructive" className="text-xs">Urgente</Badge>;
    } else if (daysDiff > 3) {
      return <Badge variant="outline" className="text-xs border-yellow-500 text-yellow-700">Atenção</Badge>;
    }
    return null;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <ClipboardList className="mr-2 h-5 w-5" />
          Board de Ordens de Serviço
        </CardTitle>
        <div className="flex gap-2">
          <Link href="/service-orders/new">
            <Button size="sm">
              <Plus className="h-4 w-4 mr-1" />
              Nova Ordem
            </Button>
          </Link>
          <Link href="/service-orders">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-1" />
              Ver Todas
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4">
          {/* Primeira linha - 4 colunas */}
          {Object.entries(statusConfig).slice(0, 4).map(([status, config]) => {
            const orders = groupedOrders[status] || [];
            const Icon = config.icon;

            return (
              <div key={status} className={`rounded-lg border-2 border-dashed ${config.bgColor} p-4`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Icon className={`h-4 w-4 mr-2 ${config.textColor}`} />
                    <h3 className={`font-medium ${config.textColor}`}>
                      {config.label}
                    </h3>
                  </div>
                  <Badge 
                    className={`${config.color} text-white border-0`}
                  >
                    {orders.length}
                  </Badge>
                </div>

                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {orders.slice(0, 5).map((order) => (
                    <Link key={order.id} href={`/service-orders/${order.id}`}>
                      <div className="bg-white rounded-md p-3 shadow-sm hover:shadow-md transition-shadow cursor-pointer border">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium text-sm">
                            #{order.orderNumber}
                          </span>
                          {getUrgencyBadge(order)}
                        </div>

                        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                          {order.description}
                        </p>

                        <div className="flex justify-between items-center text-xs text-gray-500">
                          <span>{order.clientId}</span>
                          <span>{formatDateTime(order.createdAt)}</span>
                        </div>
                      </div>
                    </Link>
                  ))}
                  
                  {orders.length === 0 && (
                    <div className="text-center py-8 text-gray-400">
                      <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Nenhuma ordem</p>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Segunda linha - 3 colunas restantes */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 mt-4">
          {Object.entries(statusConfig).slice(4).map(([status, config]) => {
            const orders = groupedOrders[status] || [];
            const Icon = config.icon;

            return (
              <div key={status} className={`rounded-lg border-2 border-dashed ${config.bgColor} p-4`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Icon className={`h-4 w-4 mr-2 ${config.textColor}`} />
                    <h3 className={`font-medium ${config.textColor}`}>
                      {config.label}
                    </h3>
                  </div>
                  <Badge 
                    className={`${config.color} text-white border-0`}
                  >
                    {orders.length}
                  </Badge>
                </div>

                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {orders.slice(0, 5).map((order) => (
                    <Link key={order.id} href={`/service-orders/${order.id}`}>
                      <div className="bg-white rounded-md p-3 shadow-sm hover:shadow-md transition-shadow cursor-pointer border">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium text-sm">
                            #{order.orderNumber}
                          </span>
                          {getUrgencyBadge(order)}
                        </div>

                        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                          {order.description}
                        </p>

                        <div className="flex justify-between items-center text-xs text-gray-500">
                          <span>{order.client?.name}</span>
                          <span>{formatDateTime(order.updatedAt)}</span>
                        </div>
                      </div>
                    </Link>
                  ))}

                  {orders.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Icon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Nenhuma ordem</p>
                    </div>
                  )}

                  {orders.length > 5 && (
                    <Link href={`/service-orders?status=${status}`}>
                      <Button variant="ghost" size="sm" className="w-full">
                        Ver mais {orders.length - 5} ordens
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Resumo Total */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold">{serviceOrders.length}</div>
              <div className="text-sm text-muted-foreground">Total de Ordens</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {(groupedOrders.received || []).length}
              </div>
              <div className="text-sm text-muted-foreground">Novas</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {(groupedOrders.in_analysis || []).length + 
                 (groupedOrders.waiting_parts || []).length + 
                 (groupedOrders.waiting_approval || []).length + 
                 (groupedOrders.in_execution || []).length}
              </div>
              <div className="text-sm text-muted-foreground">Em Andamento</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {(groupedOrders.completed || []).length + (groupedOrders.delivered || []).length}
              </div>
              <div className="text-sm text-muted-foreground">Finalizadas</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}