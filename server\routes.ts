import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import session from "express-session";
import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import createMemoryStore from "memorystore";
import { z } from "zod";
import { 
  insertUserSchema, 
  insertClientSchema,
  insertEquipmentSchema,
  insertInventoryItemSchema,
  insertServiceOrderSchema,
  insertQuoteSchema,
  insertPaymentMethodSchema,
  insertPaymentSchema,
  insertInvoiceSchema,
  insertInvoiceItemSchema,
  insertTechnicianSchema,
  insertTechnicianScheduleSchema,
  insertAppointmentSchema,
  insertServiceSchema,
  insertPartSchema
} from "@shared/schema";
import whatsappRouter from "./api/whatsapp";
import Stripe from "stripe";

const MemoryStore = createMemoryStore(session);

export async function registerRoutes(app: Express): Promise<Server> {
  // Session setup
  app.use(
    session({
      cookie: { maxAge: 7200000 }, // 2 horas em milissegundos
      store: new MemoryStore({
        checkPeriod: 86400000, // prune expired entries every 24h
      }),
      resave: false,
      saveUninitialized: false,
      secret: process.env.SESSION_SECRET || "simplesmed-secret"
    })
  );

  // Passport initialization
  app.use(passport.initialize());
  app.use(passport.session());

  // Configure passport local strategy
  passport.use(
    new LocalStrategy(async (username, password, done) => {
      try {
        const user = await storage.getUserByUsername(username);
        if (!user) {
          return done(null, false, { message: "Incorrect username." });
        }

        // In a real app, we would hash and compare passwords
        if (user.password !== password) {
          return done(null, false, { message: "Incorrect password." });
        }

        return done(null, user);
      } catch (error) {
        return done(error);
      }
    })
  );

  // Serialize and deserialize user
  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id: number, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (error) {
      done(error);
    }
  });

  // Middleware to check if user is authenticated
  const isAuthenticated = (req: Request, res: Response, next: Function) => {
    if (req.isAuthenticated()) {
      return next();
    }
    res.status(401).json({ message: "Unauthorized" });
  };

  // Auto-login middleware for development - automatically logs in user for API routes
  const autoLoginForDev = async (req: Request, res: Response, next: Function) => {
    if (req.isAuthenticated()) {
      return next();
    }
    
    // Auto-login with first user for development
    try {
      const users = await storage.getUsers();
      if (users.length > 0) {
        const user = users[0];
        (req as any).login(user, (err: any) => {
          if (err) {
            console.log("Auto-login failed:", err);
            return res.status(401).json({ message: "Authentication required" });
          }
          console.log("Auto-logged in user:", user.username);
          return next();
        });
      } else {
        return res.status(401).json({ message: "No users found for auto-login" });
      }
    } catch (error) {
      console.error("Auto-login error:", error);
      return res.status(401).json({ message: "Authentication error" });
    }
  };

  // Auth routes
  app.post("/api/auth/login", passport.authenticate("local"), (req, res) => {
    res.json({ user: req.user });
  });

  app.get("/api/auth/logout", (req, res) => {
    req.logout(() => {
      res.json({ success: true });
    });
  });

  app.get("/api/auth/session", (req, res) => {
    res.json({
      user: req.user || null,
      isAuthenticated: req.isAuthenticated(),
    });
  });

  // User routes
  app.get("/api/users", isAuthenticated, async (req, res) => {
    const users = await storage.getUsers();
    res.json(users);
  });

  app.post("/api/users", isAuthenticated, async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      const user = await storage.createUser(userData);
      res.status(201).json(user);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/users/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const user = await storage.getUser(id);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.json(user);
  });

  app.patch("/api/users/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedUser = await storage.updateUser(id, req.body);
      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(updatedUser);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/users/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      // Verificar se o usuário existe
      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Não permitir excluir o próprio usuário logado
      if (req.user && req.user.id === id) {
        return res.status(400).json({ message: "Cannot delete your own user account" });
      }

      // Verificar se o usuário está associado a um técnico
      const technician = await storage.getTechnicianByUserId(id);
      if (technician) {
        return res.status(400).json({ 
          message: "User is associated with a technician and cannot be deleted. Deactivate the user instead."
        });
      }

      // Em vez de excluir completamente, vamos apenas desativar o usuário
      const deactivatedUser = await storage.updateUser(id, { active: false });
      res.json({ success: true, user: deactivatedUser });
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Client routes
  app.get("/api/clients", isAuthenticated, async (req, res) => {
    const clients = await storage.getClients();
    res.json(clients);
  });

  app.post("/api/clients", isAuthenticated, async (req, res) => {
    try {
      const clientData = insertClientSchema.parse(req.body);
      const client = await storage.createClient(clientData);
      res.status(201).json(client);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/clients/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const client = await storage.getClient(id);
    if (!client) {
      return res.status(404).json({ message: "Client not found" });
    }
    res.json(client);
  });

  app.patch("/api/clients/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedClient = await storage.updateClient(id, req.body);
      if (!updatedClient) {
        return res.status(404).json({ message: "Client not found" });
      }
      res.json(updatedClient);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Equipment routes
  app.get("/api/equipment", isAuthenticated, async (req, res) => {
    const equipment = await storage.getEquipments();
    res.json(equipment);
  });

  app.get("/api/equipment/client/:clientId", isAuthenticated, async (req, res) => {
    const clientId = parseInt(req.params.clientId);
    const equipment = await storage.getEquipmentByClient(clientId);
    res.json(equipment);
  });

  app.post("/api/equipment", isAuthenticated, async (req, res) => {
    try {
      console.log("Recebendo dados de equipamento:", req.body);

      // Garantir que purchaseDate seja uma data ou null
      let equipmentData = { ...req.body };

      // Se purchaseDate é uma string válida, converte para Date
      if (equipmentData.purchaseDate && !(equipmentData.purchaseDate instanceof Date)) {
        equipmentData.purchaseDate = new Date(equipmentData.purchaseDate);
      }

      // Validar dados
      const validatedData = insertEquipmentSchema.parse(equipmentData);

      // Criar equipamento
      const equipment = await storage.createEquipment(validatedData);
      res.status(201).json(equipment);
    } catch (error) {
      console.error("Erro ao criar equipamento:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/equipment/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const equipment = await storage.getEquipment(id);
    if (!equipment) {
      return res.status(404).json({ message: "Equipment not found" });
    }
    res.json(equipment);
  });

  // Equipment categories routes
  app.get("/api/equipment-categories", isAuthenticated, async (req, res) => {
    const categories = await storage.getEquipmentCategories();
    res.json(categories);
  });

  // Services routes
  app.get("/api/services", isAuthenticated, async (req, res) => {
    const services = await storage.getServices();
    res.json(services);
  });

  app.post("/api/services", isAuthenticated, async (req, res) => {
    try {
      const serviceData = insertServiceSchema.parse(req.body);
      const service = await storage.createService(serviceData);
      res.status(201).json(service);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/services/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const service = await storage.getService(id);
    if (!service) {
      return res.status(404).json({ message: "Serviço não encontrado" });
    }
    res.json(service);
  });

  app.patch("/api/services/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedService = await storage.updateService(id, req.body);
      if (!updatedService) {
        return res.status(404).json({ message: "Serviço não encontrado" });
      }
      res.json(updatedService);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/services/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteService(id);
      if (!success) {
        return res.status(404).json({ message: "Serviço não encontrado" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Parts routes
  app.get("/api/parts", isAuthenticated, async (req, res) => {
    try {
      // Obter todas as peças cadastradas
      const parts = await storage.getParts();

      // Obter todos os itens de inventário
      const inventoryItems = await storage.getInventoryItems();

      // Converter itens de inventário para o formato de peças
      const inventoryAsParts = inventoryItems.map(item => {
        // Usar o ID do item de inventário como partId para não conflitar com IDs de peças reais
        return {
          id: item.id,
          name: item.name,
          description: item.description || "",
          brand: item.supplier || "Não especificado",
          sku: item.sku || "",
          barcode: "",
          internalCode: "",
          categoryId: item.categoryId,
          purchaseValue: item.price || 0,
          saleValue: item.price || 0,
          active: true,
          createdAt: item.createdAt,
          updatedAt: new Date(),
          // Adicionar marcador para identificar que é um item de inventário
          isInventoryItem: true,
          // Incluir a quantidade disponível no inventário
          quantity: item.quantity || 0
        };
      });

      // Combinar as peças reais com os itens de inventário
      const combinedParts = [
        ...parts,
        ...inventoryAsParts
      ];

      // Remover duplicatas (peças que já existem no inventário)
      // Priorize manter os itens de inventário e remover as peças "puras", 
      // já que o inventário tem informações adicionais como quantidade
      const uniqueParts = combinedParts.filter((part, index, self) => {
        // Se a peça é do inventário, sempre mantém
        if (part.isInventoryItem) {
          return true;
        }

        // Se não é do inventário, verifica se existe uma versão no inventário
        // com o mesmo nome ou mesmo SKU (quando o SKU existe)
        const inventoryVersion = self.find(p => 
          p.isInventoryItem && (
            (part.name === p.name) || 
            (part.sku && part.sku === p.sku && part.sku !== "")
          )
        );

        // Se não encontrou no inventário, mantém
        return !inventoryVersion;
      });

      // Log para depuração
      console.log(`Encontrados ${parts.length} peças cadastradas e ${inventoryItems.length} itens no inventário. Total combinado: ${uniqueParts.length}`);

      res.json(uniqueParts);
    } catch (error) {
      console.error('Erro ao obter peças:', error);
      res.status(500).json({ error: 'Falha ao buscar peças' });
    }
  });

  app.post("/api/parts", isAuthenticated, async (req, res) => {
    try {
      console.log('Creating part with data:', req.body);
      const partData = insertPartSchema.parse(req.body);
      const part = await storage.createPart(partData);

      // Adicionar automaticamente ao inventário com quantidade 1
      try {
        const inventoryItem = {
          name: part.name,
          sku: part.sku,
          categoryId: part.categoryId ? part.categoryId : undefined,
          description: part.description,
          price: part.saleValue, // Usar o valor de venda como preço no inventário
          quantity: 1, // Iniciar com quantidade 1
          minQuantity: req.body.minQuantity || 0, // Usar a quantidade mínima informada no formulário
          supplier: part.brand // Usar a marca como fornecedor
        };

        await storage.createInventoryItem(inventoryItem);
      } catch (inventoryError) {
        console.error('Erro ao adicionar peça ao inventário:', inventoryError);
        // Não falhar a requisição se não conseguir adicionar ao inventário
      }

      res.status(201).json(part);
    } catch (error) {
      console.error('Erro ao criar peça:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/parts/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const part = await storage.getPart(id);
    if (!part) {
      return res.status(404).json({ message: "Peça não encontrada" });
    }
    res.json(part);
  });

  app.patch("/api/parts/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedPart = await storage.updatePart(id, req.body);
      if (!updatedPart) {
        return res.status(404).json({ message: "Peça não encontrada" });
      }
      res.json(updatedPart);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/parts/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deletePart(id);
      if (!success) {
        return res.status(404).json({ message: "Peça não encontrada" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Technician routes
  app.get("/api/technicians", isAuthenticated, async (req, res) => {
    const technicians = await storage.getTechnicians();
    res.json(technicians);
  });

  app.post("/api/technicians", isAuthenticated, async (req, res) => {
    try {
      const technicianData = insertTechnicianSchema.parse(req.body);
      const technician = await storage.createTechnician(technicianData);
      res.status(201).json(technician);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/technicians/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const technician = await storage.getTechnician(id);
    if (!technician) {
      return res.status(404).json({ message: "Técnico não encontrado" });
    }
    res.json(technician);
  });

  app.patch("/api/technicians/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedTechnician = await storage.updateTechnician(id, req.body);
      if (!updatedTechnician) {
        return res.status(404).json({ message: "Técnico não encontrado" });
      }
      res.json(updatedTechnician);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Technician schedules routes
  app.get("/api/technician-schedules", isAuthenticated, async (req, res) => {
    const schedules = await storage.getTechnicianSchedules();
    res.json(schedules);
  });

  app.get("/api/technician-schedules/technician/:technicianId", isAuthenticated, async (req, res) => {
    try {
      const technicianId = parseInt(req.params.technicianId);
      if (isNaN(technicianId)) {
        return res.status(400).json({ message: "ID do técnico inválido" });
      }
      const schedules = await storage.getTechnicianSchedulesByTechnician(technicianId);
      res.json(schedules);
    } catch (error) {
      console.error("Error fetching technician schedules:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  app.get("/api/technician-schedules/date/:date", isAuthenticated, async (req, res) => {
    try {
      const date = new Date(req.params.date);
      const schedules = await storage.getTechnicianSchedulesByDate(date);
      res.json(schedules);
    } catch (error) {
      res.status(400).json({ message: "Data inválida" });
    }
  });

  app.get("/api/technician-schedules/date-range", isAuthenticated, async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      if (!startDate || !endDate) {
        return res.status(400).json({ message: "startDate e endDate são obrigatórios" });
      }

      const start = new Date(startDate as string);
      const end = new Date(endDate as string);

      const schedules = await storage.getTechnicianSchedulesByDateRange(start, end);
      res.json(schedules);
    } catch (error) {
      res.status(400).json({ message: "Parâmetros de data inválidos" });
    }
  });

  app.post("/api/technician-schedules", isAuthenticated, async (req, res) => {
    try {
      const scheduleData = insertTechnicianScheduleSchema.parse(req.body);
      const schedule = await storage.createTechnicianSchedule(scheduleData);
      res.status(201).json(schedule);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/technician-schedules/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "ID do agendamento inválido" });
      }
      const schedule = await storage.getTechnicianSchedule(id);
      if (!schedule) {
        return res.status(404).json({ message: "Agendamento não encontrado" });
      }
      res.json(schedule);
    } catch (error) {
      console.error("Error fetching technician schedule:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  app.patch("/api/technician-schedules/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedSchedule = await storage.updateTechnicianSchedule(id, req.body);
      if (!updatedSchedule) {
        return res.status(404).json({ message: "Agendamento não encontrado" });
      }
      res.json(updatedSchedule);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  app.patch("/api/technician-schedules/:id/status", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status } = req.body;

      if (!status || !['scheduled', 'in_progress', 'completed', 'cancelled'].includes(status)) {
        return res.status(400).json({ message: "Status inválido" });
      }

      const updatedSchedule = await storage.updateTechnicianScheduleStatus(id, status);
      if (!updatedSchedule) {
        return res.status(404).json({ message: "Agendamento não encontrado" });
      }

      res.json(updatedSchedule);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/technician-schedules/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteTechnicianSchedule(id);
      if (!success) {
        return res.status(404).json({ message: "Agendamento não encontrado" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Inventory routes
  app.get("/api/inventory", isAuthenticated, async (req, res) => {
    const items = await storage.getInventoryItems();
    res.json(items);
  });

  app.get("/api/inventory/low-stock", isAuthenticated, async (req, res) => {
    const items = await storage.getLowStockItems();
    res.json(items);
  });

  app.post("/api/inventory", isAuthenticated, async (req, res) => {
    try {
      const itemData = insertInventoryItemSchema.parse(req.body);
      const item = await storage.createInventoryItem(itemData);
      res.status(201).json(item);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.patch("/api/inventory/:id/adjust", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { quantity } = req.body;

      if (typeof quantity !== 'number') {
        return res.status(400).json({ message: "Quantity must be a number" });
      }

      const updatedItem = await storage.adjustInventoryQuantity(id, quantity);
      if (!updatedItem) {
        return res.status(404).json({ message: "Item not found or invalid quantity" });
      }

      res.json(updatedItem);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Inventory categories routes
  app.get("/api/inventory-categories", isAuthenticated, async (req, res) => {
    const categories = await storage.getInventoryCategories();
    res.json(categories);
  });

  // Service Orders routes
  app.get("/api/service-orders", isAuthenticated, async (req, res) => {
    const orders = await storage.getServiceOrders();
    res.json(orders);
  });

  app.get("/api/service-orders/status/:status", isAuthenticated, async (req, res) => {
    const { status } = req.params;
    const orders = await storage.getServiceOrdersByStatus(status);
    res.json(orders);
  });

  app.post("/api/service-orders", isAuthenticated, async (req, res) => {
    try {
      console.log("Recebendo dados de ordem de serviço:", req.body);

      // Garantir que datas estejam no formato correto
      let serviceOrderData = { ...req.body };

      // Se estimatedCompletionDate é uma string válida, converte para Date
      if (serviceOrderData.estimatedCompletionDate && 
          !(serviceOrderData.estimatedCompletionDate instanceof Date)) {
        serviceOrderData.estimatedCompletionDate = new Date(serviceOrderData.estimatedCompletionDate);
      }

      // Extrair itens de serviço e peças antes da validação
      const serviceItems = serviceOrderData.serviceItems || [];
      const partItems = serviceOrderData.partItems || [];

      // Remover itens do objeto principal para validação
      const { serviceItems: _, partItems: __, ...orderDataOnly } = serviceOrderData;

      // Validar dados
      const validatedData = insertServiceOrderSchema.parse(orderDataOnly);

      // Gerar automaticamente o número da ordem de serviço se não for fornecido
      if (!serviceOrderData.orderNumber) {
        // Gerar um número sequencial baseado no maior número existente + 1
        const serviceOrders = await storage.getServiceOrders();
        let maxNumber = 0;

        // Encontrar o maior número já usado
        serviceOrders.forEach(order => {
          const match = order.orderNumber.match(/^OS-(\d+)$/);
          if (match) {
            const number = parseInt(match[1], 10);
            if (number > maxNumber) {
              maxNumber = number;
            }
          }
        });

        const nextNumber = maxNumber + 1;
        serviceOrderData.orderNumber = `OS-${nextNumber.toString().padStart(6, '0')}`;
      }

      // Criar ordem de serviço
      const order = await storage.createServiceOrder(validatedData);

      // Adicionar itens de serviço à ordem
      for (const serviceItem of serviceItems) {
        try {
          if (serviceItem.serviceId && serviceItem.quantity) {
            await storage.createServiceOrderItem({
              serviceOrderId: order.id,
              inventoryItemId: null,
              description: serviceItem.description || "",
              quantity: serviceItem.quantity,
              unitPrice: serviceItem.unitPrice || 0,
            });
            console.log(`Item de serviço adicionado à ordem de serviço ${order.id}`);
          }
        } catch (error) {
          console.error(`Erro ao adicionar item de serviço à ordem:`, error);
        }
      }

      // Adicionar itens de peças à ordem e atualizar o estoque
      for (const partItem of partItems) {
        if (partItem.partId && partItem.quantity) {
          try {
            // Primeiro, adicionar o item à ordem de serviço
            await storage.createServiceOrderItem({
              serviceOrderId: order.id,
              inventoryItemId: partItem.partId,
              description: partItem.description || "",
              quantity: partItem.quantity,
              unitPrice: partItem.unitPrice || 0,
            });
            console.log(`Item de peça adicionado à ordem de serviço ${order.id}`);

            // Depois, ajustar o estoque (reduzir a quantidade)
            await storage.adjustInventoryQuantity(partItem.partId, -partItem.quantity);
            console.log(`Quantidade de inventário ${partItem.partId} reduzida em ${partItem.quantity} unidades`);
          } catch (error) {
            console.error(`Erro ao processar peça ${partItem.partId}:`, error);
            // Não interrompe o processo se houver erro
          }
        }
      }

      res.status(201).json(order);
    } catch (error) {
      console.error("Erro ao criar ordem de serviço:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/service-orders/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const order = await storage.getServiceOrder(id);
    if (!order) {
      return res.status(404).json({ message: "Service order not found" });
    }
    res.json(order);
  });

  app.put("/api/service-orders/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log('Atualizando ordem de serviço:', id, req.body);
      
      // Validar dados de entrada
      if (!req.body || typeof req.body !== 'object') {
        return res.status(400).json({ message: "Invalid request body" });
      }

      // Garantir que estimatedCompletionDate seja uma data ou null
      let updateData = { ...req.body };
      if (updateData.estimatedCompletionDate && typeof updateData.estimatedCompletionDate === 'string') {
        try {
          updateData.estimatedCompletionDate = new Date(updateData.estimatedCompletionDate);
          // Verificar se a data é válida
          if (isNaN(updateData.estimatedCompletionDate.getTime())) {
            updateData.estimatedCompletionDate = null;
          }
        } catch (dateError) {
          console.error('Erro ao converter data:', dateError);
          updateData.estimatedCompletionDate = null;
        }
      }

      // Limpar campos undefined/null opcionais
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined || updateData[key] === '') {
          if (['equipmentId', 'technicianId', 'estimatedCompletionDate', 'diagnostics', 'solution'].includes(key)) {
            updateData[key] = null;
          }
        }
      });
      
      console.log('Dados processados para atualização:', updateData);
      
      const updatedOrder = await storage.updateServiceOrder(id, updateData);
      if (!updatedOrder) {
        return res.status(404).json({ message: "Service order not found" });
      }

      res.json(updatedOrder);
    } catch (error) {
      console.error('Erro detalhado ao atualizar ordem de serviço:', error);
      if (error instanceof Error) {
        console.error('Stack trace:', error.stack);
      }
      res.status(500).json({ 
        message: "Server error", 
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.patch("/api/service-orders/:id/status", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status } = req.body;

      if (!status) {
        return res.status(400).json({ message: "Status is required" });
      }

      const updatedOrder = await storage.updateServiceOrderStatus(id, status);
      if (!updatedOrder) {
        return res.status(404).json({ message: "Service order not found" });
      }

      res.json(updatedOrder);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // DELETE endpoint for removing a service order
  app.delete("/api/service-orders/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      // First check if the service order exists
      const order = await storage.getServiceOrder(id);
      if (!order) {
        return res.status(404).json({ message: "Service order not found" });
      }

      // Attempt to delete the service order
      const success = await storage.deleteServiceOrder(id);

      if (success) {
        res.json({ success: true, message: "Service order deleted successfully" });
      } else {
        res.status(500).json({ success: false, message: "Failed to delete service order" });
      }
    } catch (error) {
      console.error("Error deleting service order:", error);
      res.status(500).json({ success: false, message: "Server error" });
    }
  });

  // Service Order Items routes
  app.get("/api/service-orders/:orderId/items", isAuthenticated, async (req, res) => {
    const orderId = parseInt(req.params.orderId);
    const items = await storage.getServiceOrderItems(orderId);
    res.json(items);
  });

  // Quotes routes
  app.get("/api/quotes", isAuthenticated, async (req, res) => {
    const quotes = await storage.getQuotes();
    res.json(quotes);
  });

  app.post("/api/quotes", isAuthenticated, async (req, res) => {
    try {
      console.log("Recebendo dados para orçamento:", req.body);

      // Gerar automaticamente o número do orçamento se não for fornecido
      let quoteData = { ...req.body };
      if (!quoteData.quoteNumber) {
        // Gerar um número sequencial para o orçamento (baseado na contagem atual + 1)
        const quotes = await storage.getQuotes();
        const nextNumber = quotes.length + 1;
        quoteData.quoteNumber = `QT-${nextNumber.toString().padStart(6, '0')}`;
      }

      // Converter validUntil para Date se for string
      if (quoteData.validUntil && typeof quoteData.validUntil === 'string') {
        quoteData.validUntil = new Date(quoteData.validUntil);
      }

      console.log("Dados processados para orçamento:", quoteData);

      // Validar com o schema
      const validatedData = insertQuoteSchema.parse(quoteData);

      // Criar o orçamento
      const quote = await storage.createQuote(validatedData);
      res.status(201).json(quote);
    } catch (error) {
      console.error("Erro ao criar orçamento:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/quotes/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const quote = await storage.getQuote(id);
    if (!quote) {
      return res.status(404).json({ message: "Quote not found" });
    }
    res.json(quote);
  });

  app.patch("/api/quotes/:id/approve", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const approvedQuote = await storage.approveQuote(id);
      if (!approvedQuote) {
        return res.status(404).json({ message: "Quote not found" });
      }
      res.json(approvedQuote);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  app.patch("/api/quotes/:id/reject", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const rejectedQuote = await storage.rejectQuote(id);
      if (!rejectedQuote) {
        return res.status(404).json({ message: "Quote not found" });
      }
      res.json(rejectedQuote);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // WhatsApp routes
  app.use("/api/whatsapp", whatsappRouter);

  // Esta seção foi removida pois há uma definição posterior mais completa

  app.get("/api/subscription", isAuthenticated, async (req, res) => {
    try {
      if (!process.env.STRIPE_SECRET_KEY) {
        return res.status(500).json({ 
          active: false,
          message: "Stripe não configurado"
        });
      }

      const Stripe = await import("stripe");
      const stripe = new Stripe.default(process.env.STRIPE_SECRET_KEY!, {
        apiVersion: "2025-03-31.basil",
      });
      const { customerId } = req.user as any;

      if (!customerId) {
        return res.json({
          active: false,
          message: "Usuário sem assinatura"
        });
      }

      const subscriptions = await stripe.subscriptions.list({
        customer: customerId,
        status: 'active',
        limit: 1,
      });

      if (subscriptions.data.length === 0) {
        return res.json({
          active: false,
          message: "Nenhuma assinatura ativa"
        });
      }

      const subscription = subscriptions.data[0];

      return res.json({
        active: true,
        currentPeriodEnd: subscription.current_period_end * 1000,
        status: subscription.status
      });
    } catch (error) {
      console.error("Erro ao buscar assinatura:", error);
      res.status(500).json({ error: "Erro ao buscar assinatura" });
    }
  });

  app.post("/api/create-portal-session", isAuthenticated, async (req, res) => {
    try {
      if (!process.env.STRIPE_SECRET_KEY) {
        return res.status(500).json({ error: "Stripe not configured" });
      }

      const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
      const { customerId } = req.user;

      const session = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: `${req.headers.origin}/settings/billing`,
      });

      res.json({ url: session.url });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  app.post("/api/stripe/webhook", async (req, res) => {
    const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
    const signature = req.headers["stripe-signature"];

    try {
      const event = stripe.webhooks.constructEvent(
        req.body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );

      switch (event.type) {
        case "customer.subscription.updated":
        case "customer.subscription.deleted":
          const subscription = event.data.object;
          // Atualizar status da assinatura no banco de dados
          await storage.updateSubscriptionStatus(subscription);
          break;
      }

      res.json({ received: true });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  });

  // Subscription routes
  app.get("/api/subscription", async (req, res) => {
    try {
      // Mock data para demonstração - substituir pela integração real com Stripe
      const mockSubscription = {
        active: false,
        status: "inactive",
        currentPeriodEnd: null,
        plan: null,
        invoices: []
      };
      
      res.json(mockSubscription);
    } catch (error) {
      console.error("Error fetching subscription:", error);
      res.status(500).json({ error: "Erro ao buscar assinatura" });
    }
  });

  // Esta definição foi removida pois há uma definição posterior mais completa com autenticação

  app.post("/api/create-portal-session", async (req, res) => {
    try {
      if (!process.env.STRIPE_SECRET_KEY) {
        return res.status(500).json({ error: "Stripe not configured" });
      }

      const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
      
      // Em uma implementação real, você obteria o customer_id do usuário logado
      const mockCustomerId = "cus_mock_customer_id";
      
      const session = await stripe.billingPortal.sessions.create({
        customer: mockCustomerId,
        return_url: `${req.headers.origin}/settings/billing`,
      });

      res.json({ url: session.url });
    } catch (error) {
      console.error("Error creating portal session:", error);
      res.status(500).json({ error: "Erro ao criar sessão do portal" });
    }
  });

  // Payment Method routes
  app.get("/api/payment-methods", isAuthenticated, async (req, res) => {
    const paymentMethods = await storage.getPaymentMethods();
    res.json(paymentMethods);
  });

  app.post("/api/payment-methods", isAuthenticated, async (req, res) => {
    try {
      const methodData = insertPaymentMethodSchema.parse(req.body);
      const paymentMethod = await storage.createPaymentMethod(methodData);
      res.status(201).json(paymentMethod);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/payment-methods/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const paymentMethod = await storage.getPaymentMethod(id);
    if (!paymentMethod) {
      return res.status(404).json({ message: "Payment method not found" });
    }
    res.json(paymentMethod);
  });

  app.patch("/api/payment-methods/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedMethod = await storage.updatePaymentMethod(id, req.body);
      if (!updatedMethod) {
        return res.status(404).json({ message: "Payment method not found" });
      }
      res.json(updatedMethod);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/payment-methods/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deletePaymentMethod(id);
      if (!success) {
        return res.status(404).json({ message: "Payment method not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Payment routes
  app.get("/api/payments", isAuthenticated, async (req, res) => {
    const payments = await storage.getPayments();
    res.json(payments);
  });

  app.get("/api/payments/client/:clientId", isAuthenticated, async (req, res) => {
    const clientId = parseInt(req.params.clientId);
    const payments = await storage.getPaymentsByClient(clientId);
    res.json(payments);
  });

  app.get("/api/payments/invoice/:invoiceId", isAuthenticated, async (req, res) => {
    const invoiceId = parseInt(req.params.invoiceId);
    const payments = await storage.getPaymentsByInvoice(invoiceId);
    res.json(payments);
  });

  app.post("/api/payments", isAuthenticated, async (req, res) => {
    try {
      console.log("Recebendo dados de pagamento:", req.body);
      const paymentData = insertPaymentSchema.parse(req.body);
      console.log("Dados validados de pagamento:", paymentData);
      const payment = await storage.createPayment(paymentData);
      res.status(201).json(payment);
    } catch (error) {
      console.error("Erro ao criar pagamento:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/payments/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const payment = await storage.getPayment(id);
    if (!payment) {
      return res.status(404).json({ message: "Payment not found" });
    }
    res.json(payment);
  });

  app.patch("/api/payments/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedPayment = await storage.updatePayment(id, req.body);
      if (!updatedPayment) {
        return res.status(404).json({ message: "Payment not found" });
      }
      res.json(updatedPayment);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Invoice routes
  app.get("/api/invoices", isAuthenticated, async (req, res) => {
    const invoices = await storage.getInvoices();
    res.json(invoices);
  });

  app.get("/api/invoices/unpaid", isAuthenticated, async (req, res) => {
    const invoices = await storage.getUnpaidInvoices();
    res.json(invoices);
  });

  app.get("/api/invoices/overdue", isAuthenticated, async (req, res) => {
    const invoices = await storage.getOverdueInvoices();
    res.json(invoices);
  });

  app.get("/api/invoices/client/:clientId", isAuthenticated, async (req, res) => {
    const clientId = parseInt(req.params.clientId);
    const invoices = await storage.getInvoicesByClient(clientId);
    res.json(invoices);
  });

  app.get("/api/invoices/service-order/:serviceOrderId", isAuthenticated, async (req, res) => {
    const serviceOrderId = parseInt(req.params.serviceOrderId);
    const invoices = await storage.getInvoicesByServiceOrder(serviceOrderId);
    res.json(invoices);
  });

  app.post("/api/invoices", isAuthenticated, async (req, res) => {
    try {
      console.log("Recebendo dados de fatura:", req.body);
      const invoiceData = insertInvoiceSchema.parse(req.body);
      
      // Gerar automaticamente o número da fatura se não for fornecido
      if (!req.body.invoiceNumber) {
        const invoices = await storage.getInvoices();
        const maxInvoiceNumber = Math.max(
          0,
          ...invoices.map(invoice => {
            const match = invoice.invoiceNumber?.match(/INV-(\d+)/);
            return match ? parseInt(match[1]) : 0;
          })
        );
        const nextNumber = maxInvoiceNumber + 1;
        (invoiceData as any).invoiceNumber = `INV-${nextNumber.toString().padStart(6, '0')}`;
      }
      
      console.log("Dados processados de fatura:", invoiceData);
      const invoice = await storage.createInvoice(invoiceData);
      res.status(201).json(invoice);
    } catch (error) {
      console.error("Erro ao criar fatura:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/invoices/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const invoice = await storage.getInvoice(id);
    if (!invoice) {
      return res.status(404).json({ message: "Invoice not found" });
    }
    res.json(invoice);
  });

  app.patch("/api/invoices/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedInvoice = await storage.updateInvoice(id, req.body);
      if (!updatedInvoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }
      res.json(updatedInvoice);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Invoice Items routes
  app.get("/api/invoices/:invoiceId/items", isAuthenticated, async (req, res) => {
    const invoiceId = parseInt(req.params.invoiceId);
    const items = await storage.getInvoiceItems(invoiceId);
    res.json(items);
  });

  app.post("/api/invoice-items", isAuthenticated, async (req, res) => {
    try {
      const itemData = insertInvoiceItemSchema.parse(req.body);
      const item = await storage.createInvoiceItem(itemData);
      res.status(201).json(item);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: "Server error" });
    }
  });

  app.get("/api/invoice-items/:id", isAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const item = await storage.getInvoiceItem(id);
    if (!item) {
      return res.status(404).json({ message: "Invoice item not found" });
    }
    res.json(item);
  });

  app.patch("/api/invoice-items/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedItem = await storage.updateInvoiceItem(id, req.body);
      if (!updatedItem) {
        return res.status(404).json({ message: "Invoice item not found" });
      }
      res.json(updatedItem);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  app.delete("/api/invoice-items/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.removeInvoiceItem(id);
      if (!success) {
        return res.status(404).json({ message: "Invoice item not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  });

  // Stripe payment integration
  app.post("/api/create-payment-intent", isAuthenticated, async (req, res) => {
    try {
      // Verificar se o Stripe está configurado
      if (!process.env.STRIPE_SECRET_KEY) {
        return res.status(500).json({ 
          error: "Missing Stripe configuration. Please configure Stripe API keys." 
        });
      }

      const { amount, invoiceId } = req.body;

      if (!amount || amount <= 0) {
        return res.status(400).json({ error: "Invalid amount" });
      }

      // Aqui teríamos a criação do paymentIntent usando o Stripe
      // Para implementar quando tivermos o Stripe configurado

      res.json({ 
        success: true, 
        message: "Stripe integration pending API key configuration" 
      });
    } catch (error) {
      res.status(500).json({ 
        error: "Error processing payment",
        details: error.message
      });
    }
  });

  // Esta seção foi removida pois já existe uma definição anterior destas rotas

  // Subscription and Stripe routes
  app.post("/api/create-checkout-session", isAuthenticated, async (req, res) => {
    try {
      if (!process.env.STRIPE_SECRET_KEY) {
        return res.status(500).json({ error: "Stripe not configured" });
      }

      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
        apiVersion: "2023-10-16",
      });
      
      console.log("Creating checkout session with user:", req.user);
      
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        mode: 'subscription',
        line_items: [
          {
            price_data: {
              currency: 'brl',
              product_data: {
                name: 'Plano Pro - Sistema de Gestão',
                description: 'Acesso completo ao sistema de gestão de assistência técnica',
              },
              unit_amount: 9900, // R$ 99,00
              recurring: {
                interval: 'month',
              },
            },
            quantity: 1,
          },
        ],
        success_url: `${req.protocol}://${req.get('host')}/settings/billing?success=true`,
        cancel_url: `${req.protocol}://${req.get('host')}/settings/billing?canceled=true`,
        customer_email: (req.user as any)?.email || '<EMAIL>',
      });

      res.json({ url: session.url });
    } catch (error) {
      console.error("Error creating checkout session:", error);
      res.status(500).json({ error: "Erro ao criar sessão de checkout" });
    }
  });

  app.post("/api/create-portal-session", isAuthenticated, async (req, res) => {
    try {
      if (!process.env.STRIPE_SECRET_KEY) {
        return res.status(500).json({ error: "Stripe not configured" });
      }

      const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
      
      // Em uma implementação real, você obteria o customer_id do usuário logado
      const mockCustomerId = "cus_mock_customer_id";
      
      const session = await stripe.billingPortal.sessions.create({
        customer: mockCustomerId,
        return_url: `${req.headers.origin}/settings/billing`,
      });

      res.json({ url: session.url });
    } catch (error) {
      console.error("Error creating portal session:", error);
      res.status(500).json({ error: "Erro ao criar sessão do portal" });
    }
  });

  app.post("/api/stripe/webhook", async (req, res) => {
    try {
      if (!process.env.STRIPE_SECRET_KEY || !process.env.STRIPE_WEBHOOK_SECRET) {
        return res.status(500).json({ error: "Stripe not configured" });
      }

      const Stripe = await import("stripe");
      const stripe = new Stripe.default(process.env.STRIPE_SECRET_KEY!, {
        apiVersion: "2024-06-20",
      });
      const signature = req.headers["stripe-signature"];

      const event = stripe.webhooks.constructEvent(
        req.body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );

      switch (event.type) {
        case "customer.subscription.updated":
        case "customer.subscription.deleted":
        case "customer.subscription.created":
          const subscription = event.data.object;
          console.log("Subscription event:", subscription);
          // Aqui você atualizaria o status da assinatura no banco de dados
          break;
        case "invoice.payment_succeeded":
        case "invoice.payment_failed":
          const invoice = event.data.object;
          console.log("Invoice event:", invoice);
          // Aqui você atualizaria o status do pagamento no banco de dados
          break;
      }

      res.json({ received: true });
    } catch (error) {
      console.error("Webhook error:", error);
      res.status(400).json({ error: error instanceof Error ? error.message : "Webhook error" });
    }
  });

  // Appointment endpoints
  app.get("/api/appointments", autoLoginForDev, async (req, res) => {
    try {
      const { type, status, technicianId, clientId, date, startDate, endDate } = req.query;
      
      console.log("=== GET /api/appointments ===");
      console.log("Query params:", req.query);
      
      let appointments;
      if (type) {
        appointments = await storage.getAppointmentsByType(type as string);
      } else if (status) {
        appointments = await storage.getAppointmentsByStatus(status as string);
      } else if (technicianId && date) {
        console.log("Buscando agendamentos por técnico E data:", { technicianId, date });
        const techAppointments = await storage.getAppointmentsByTechnician(parseInt(technicianId as string));
        const dateAppointments = await storage.getAppointmentsByDate(new Date(date as string));
        // Filtrar agendamentos que são do técnico E na data especificada
        appointments = techAppointments.filter(appt => 
          dateAppointments.some(dateAppt => dateAppt.id === appt.id)
        );
        console.log("Agendamentos encontrados por técnico E data:", appointments.length);
      } else if (technicianId) {
        console.log("Buscando agendamentos por técnico:", technicianId);
        appointments = await storage.getAppointmentsByTechnician(parseInt(technicianId as string));
        console.log("Agendamentos encontrados por técnico:", appointments.length);
      } else if (clientId) {
        appointments = await storage.getAppointmentsByClient(parseInt(clientId as string));
      } else if (date) {
        console.log("Buscando agendamentos por data:", date);
        appointments = await storage.getAppointmentsByDate(new Date(date as string));
        console.log("Agendamentos encontrados por data:", appointments.length);
      } else if (startDate && endDate) {
        appointments = await storage.getAppointmentsByDateRange(new Date(startDate as string), new Date(endDate as string));
      } else {
        appointments = await storage.getAppointments();
      }
      
      res.json(appointments);
    } catch (error) {
      console.error("Error fetching appointments:", error);
      res.status(500).json({ message: "Error fetching appointments" });
    }
  });

  app.get("/api/appointments/:id", autoLoginForDev, async (req, res) => {
    const id = parseInt(req.params.id);
    const appointment = await storage.getAppointment(id);
    if (!appointment) {
      return res.status(404).json({ message: "Appointment not found" });
    }
    res.json(appointment);
  });

  app.post("/api/appointments", autoLoginForDev, async (req, res) => {
    try {
      console.log("=== POST /api/appointments ===");
      console.log("Recebendo dados de agendamento:", req.body);
      console.log("User from session:", req.user);
      console.log("Is authenticated:", req.isAuthenticated());
      
      const user = req.user as any;
      
      if (!user || !user.id) {
        console.log("Usuário não autenticado - retornando 401");
        return res.status(401).json({ message: "Usuário não autenticado" });
      }
      
      // Preparar dados do agendamento
      let appointmentData = { ...req.body };
      
      // Garantir que appointmentDate seja uma data
      if (appointmentData.appointmentDate && typeof appointmentData.appointmentDate === 'string') {
        appointmentData.appointmentDate = new Date(appointmentData.appointmentDate);
      }
      
      // Validar se a data é válida
      if (appointmentData.appointmentDate && isNaN(appointmentData.appointmentDate.getTime())) {
        return res.status(400).json({ message: "Data de agendamento inválida" });
      }
      
      // Definir o usuário que criou
      appointmentData.createdBy = user.id;
      
      // Limpar campos vazios
      Object.keys(appointmentData).forEach(key => {
        if (appointmentData[key] === '' || appointmentData[key] === undefined) {
          if (['clientId', 'technicianId', 'serviceOrderId', 'endTime', 'location', 'contactPerson', 'contactPhone', 'contactEmail', 'notes', 'recurringPattern', 'description'].includes(key)) {
            appointmentData[key] = null;
          }
        }
      });
      
      // Validar campos obrigatórios
      if (!appointmentData.title || !appointmentData.appointmentDate || !appointmentData.startTime) {
        return res.status(400).json({ message: "Campos obrigatórios: título, data e hora de início" });
      }
      
      console.log("Dados processados:", appointmentData);
      
      // Validar dados
      console.log("Validando dados do agendamento:", appointmentData);
      const validatedData = insertAppointmentSchema.parse(appointmentData);
      console.log("Dados validados:", validatedData);
      
      // Criar agendamento
      const appointment = await storage.createAppointment(validatedData);
      
      console.log("Agendamento criado:", appointment);
      res.status(201).json(appointment);
    } catch (error) {
      console.error("Erro ao criar agendamento:", error);
      if (error instanceof z.ZodError) {
        console.error("Erros de validação Zod:", error.errors);
        return res.status(400).json({ 
          message: "Dados inválidos", 
          errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        });
      }
      
      // Tratar outros tipos de erro
      const errorMessage = error instanceof Error ? error.message : "Erro interno do servidor";
      res.status(500).json({ message: errorMessage });
    }
  });

  app.put("/api/appointments/:id", autoLoginForDev, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updatedAppointment = await storage.updateAppointment(id, req.body);
      if (!updatedAppointment) {
        return res.status(404).json({ message: "Appointment not found" });
      }
      res.json(updatedAppointment);
    } catch (error) {
      console.error("Error updating appointment:", error);
      res.status(400).json({ message: "Error updating appointment" });
    }
  });

  app.delete("/api/appointments/:id", autoLoginForDev, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteAppointment(id);
      if (!success) {
        return res.status(404).json({ message: "Appointment not found" });
      }
      res.json({ message: "Appointment deleted successfully" });
    } catch (error) {
      console.error("Error deleting appointment:", error);
      res.status(500).json({ message: "Error deleting appointment" });
    }
  });

  app.patch("/api/appointments/:id/status", autoLoginForDev, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status } = req.body;
      const updatedAppointment = await storage.updateAppointmentStatus(id, status);
      if (!updatedAppointment) {
        return res.status(404).json({ message: "Appointment not found" });
      }
      res.json(updatedAppointment);
    } catch (error) {
      console.error("Error updating appointment status:", error);
      res.status(400).json({ message: "Error updating appointment status" });
    }
  });

  app.patch("/api/appointments/:id/complete", autoLoginForDev, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const completedAppointment = await storage.completeAppointment(id);
      if (!completedAppointment) {
        return res.status(404).json({ message: "Appointment not found" });
      }
      res.json(completedAppointment);
    } catch (error) {
      console.error("Error completing appointment:", error);
      res.status(400).json({ message: "Error completing appointment" });
    }
  });

  app.patch("/api/appointments/:id/cancel", autoLoginForDev, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { reason } = req.body;
      const cancelledAppointment = await storage.cancelAppointment(id, reason);
      if (!cancelledAppointment) {
        return res.status(404).json({ message: "Appointment not found" });
      }
      res.json(cancelledAppointment);
    } catch (error) {
      console.error("Error cancelling appointment:", error);
      res.status(400).json({ message: "Error cancelling appointment" });
    }
  });

  // System information endpoint for billing/subscription section
  app.get("/api/system-info", isAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      const systemStats = {
        active: true,
        status: "sistema_operacional",
        systemVersion: "1.0.0",
        plan: "Sistema Local de Gestão",
        features: [
          "Gestão de Ordens de Serviço",
          "Controle de Clientes",
          "Gestão de Estoque",
          "Relatórios Avançados",
          "Sistema de Pagamentos"
        ],
        statistics: {
          totalServiceOrders: await storage.getServiceOrders().then(orders => orders.length),
          totalClients: await storage.getClients().then(clients => clients.length),
          totalEquipment: await storage.getEquipments().then(equipment => equipment.length),
          totalInventoryItems: await storage.getInventoryItems().then(items => items.length),
        },
        lastAccess: new Date(),
        stripeConfigured: !!process.env.STRIPE_SECRET_KEY
      };
      
      res.json(systemStats);
    } catch (error) {
      console.error("Error fetching system info:", error);
      res.status(500).json({ error: "Erro ao buscar informações do sistema" });
    }
  });

  // Create HTTP server
  const httpServer = createServer(app);

  return httpServer;
}