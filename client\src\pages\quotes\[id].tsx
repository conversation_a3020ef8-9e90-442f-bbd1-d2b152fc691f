import { useState } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Quote, Client, ServiceOrder } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { formatCurrency, formatDate } from "@/lib/utils";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>X, <PERSON><PERSON><PERSON><PERSON>gle, Arrow<PERSON><PERSON><PERSON>, Printer } from "lucide-react";

export default function QuoteDetails() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const params = useParams();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const quoteId = parseInt(params.id);

  const { data: quote, isLoading: isLoadingQuote } = useQuery<Quote>({
    queryKey: ['/api/quotes', quoteId],
    queryFn: async () => {
      const response = await apiRequest("GET", `/api/quotes/${quoteId}`);
      return response.json();
    },
    enabled: !isNaN(quoteId),
  });

  const { data: client, isLoading: isLoadingClient } = useQuery<Client>({
    queryKey: ['/api/clients', quote?.clientId],
    queryFn: async () => {
      const response = await apiRequest("GET", `/api/clients/${quote?.clientId}`);
      return response.json();
    },
    enabled: !!quote?.clientId,
  });

  const { data: serviceOrder, isLoading: isLoadingServiceOrder } = useQuery<ServiceOrder>({
    queryKey: ['/api/service-orders', quote?.serviceOrderId],
    queryFn: async () => {
      const response = await apiRequest("GET", `/api/service-orders/${quote?.serviceOrderId}`);
      return response.json();
    },
    enabled: !!quote?.serviceOrderId,
  });

  const isLoading = isLoadingQuote || isLoadingClient || (!!quote?.serviceOrderId && isLoadingServiceOrder);

  // Approve quote mutation
  const approveMutation = useMutation({
    mutationFn: async () => {
      return apiRequest("PATCH", `/api/quotes/${quoteId}/approve`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/quotes'] });
      queryClient.invalidateQueries({ queryKey: ['/api/quotes', quoteId] });
      toast({
        title: "Orçamento aprovado",
        description: "O orçamento foi aprovado com sucesso",
      });
      setShowApproveDialog(false);
    },
    onError: (error) => {
      toast({
        title: "Erro ao aprovar orçamento",
        description: error.message || "Ocorreu um erro ao aprovar o orçamento",
        variant: "destructive",
      });
    },
  });

  // Reject quote mutation
  const rejectMutation = useMutation({
    mutationFn: async () => {
      return apiRequest("PATCH", `/api/quotes/${quoteId}/reject`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/quotes'] });
      queryClient.invalidateQueries({ queryKey: ['/api/quotes', quoteId] });
      toast({
        title: "Orçamento rejeitado",
        description: "O orçamento foi rejeitado com sucesso",
      });
      setShowRejectDialog(false);
    },
    onError: (error) => {
      toast({
        title: "Erro ao rejeitar orçamento",
        description: error.message || "Ocorreu um erro ao rejeitar o orçamento",
        variant: "destructive",
      });
    },
  });

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return "bg-yellow-100 text-yellow-800";
      case 'approved':
        return "bg-green-100 text-green-800";
      case 'rejected':
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return "Pendente";
      case 'approved':
        return "Aprovado";
      case 'rejected':
        return "Rejeitado";
      default:
        return status;
    }
  };

  const isExpired = (validUntil?: string) => {
    if (!validUntil) return false;
    return new Date(validUntil) < new Date();
  };

  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes do Orçamento" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!quote) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Detalhes do Orçamento" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4 flex items-center justify-center">
            <div className="max-w-md p-6 bg-white rounded-lg shadow-sm text-center">
              <h2 className="text-2xl font-bold mb-4">Orçamento não encontrado</h2>
              <p className="text-gray-500 mb-6">O orçamento que você está tentando visualizar não existe ou foi removido.</p>
              <Button 
                onClick={() => navigate("/quotes")}
                className="bg-primary hover:bg-primary-dark"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Voltar para Orçamentos
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Detalhes do Orçamento" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center mb-6">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => navigate("/quotes")}
                className="mr-4"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Voltar
              </Button>
              
              <h1 className="text-2xl font-bold">Orçamento #{quote.quoteNumber}</h1>
              
              <div className="ml-auto flex space-x-2">
                <Button
                  variant="outline"
                  onClick={handlePrint}
                  className="print:hidden"
                >
                  <Printer className="mr-2 h-4 w-4" />
                  Imprimir
                </Button>
                
                {quote.status === 'pending' && (
                  <>
                    <Button 
                      variant="outline" 
                      className="bg-green-50 text-green-600 hover:bg-green-100 border-green-200 print:hidden"
                      onClick={() => setShowApproveDialog(true)}
                    >
                      <FileCheck className="mr-2 h-4 w-4" />
                      Aprovar
                    </Button>
                    <Button 
                      variant="outline" 
                      className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200 print:hidden"
                      onClick={() => setShowRejectDialog(true)}
                    >
                      <FileX className="mr-2 h-4 w-4" />
                      Rejeitar
                    </Button>
                  </>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Informações do Orçamento</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Status:</span>
                      <Badge className={getStatusBadgeColor(quote.status)}>
                        {getStatusLabel(quote.status)}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Criado em:</span>
                      <span>{formatDate(quote.createdAt)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Válido até:</span>
                      <div className="flex items-center">
                        {isExpired(quote.validUntil) && quote.status === 'pending' && (
                          <AlertTriangle className="h-4 w-4 text-red-500 mr-1" />
                        )}
                        <span>{formatDate(quote.validUntil)}</span>
                      </div>
                    </div>
                    {quote.status === 'approved' && quote.approvedAt && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Aprovado em:</span>
                        <span>{formatDate(quote.approvedAt)}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Cliente</CardTitle>
                </CardHeader>
                <CardContent>
                  {client ? (
                    <div className="space-y-2">
                      <div className="font-medium">{client.name}</div>
                      {client.email && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Email:</span>
                          <span>{client.email}</span>
                        </div>
                      )}
                      {client.phone && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Telefone:</span>
                          <span>{client.phone}</span>
                        </div>
                      )}
                      {client.document && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">CPF/CNPJ:</span>
                          <span>{client.document}</span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-gray-500">Cliente não encontrado</div>
                  )}
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Ordem de Serviço</CardTitle>
                </CardHeader>
                <CardContent>
                  {serviceOrder ? (
                    <div className="space-y-2">
                      <div className="font-medium">OS #{serviceOrder.orderNumber}</div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Status:</span>
                        <span className="capitalize">{serviceOrder.status.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Criada em:</span>
                        <span>{formatDate(serviceOrder.createdAt)}</span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-gray-500">
                      {quote.serviceOrderId 
                        ? "Ordem de serviço não encontrada" 
                        : "Nenhuma ordem de serviço associada"}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium">Detalhes do Orçamento</h3>
              </div>
              
              <div className="p-6">
                {quote.description && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Descrição</h4>
                    <p className="whitespace-pre-wrap">{quote.description}</p>
                  </div>
                )}
                
                {quote.notes && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Observações</h4>
                    <p className="whitespace-pre-wrap">{quote.notes}</p>
                  </div>
                )}
                
                <Separator className="my-6" />
                
                <div className="flex justify-end text-right">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="font-medium mr-8">Total:</span>
                      <span className="font-bold text-lg">{formatCurrency(quote.total || 0)}</span>
                    </div>
                    
                    {isExpired(quote.validUntil) && quote.status === 'pending' && (
                      <div className="text-red-500 text-sm flex items-center justify-end mt-2">
                        <AlertTriangle className="h-4 w-4 mr-1" />
                        Este orçamento está expirado
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Approve Dialog */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Aprovar Orçamento</DialogTitle>
            <DialogDescription>
              Você está prestes a aprovar o orçamento #{quote.quoteNumber}. Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowApproveDialog(false)}
              disabled={approveMutation.isPending}
            >
              Cancelar
            </Button>
            <Button 
              onClick={() => approveMutation.mutate()}
              className="bg-green-600 hover:bg-green-700"
              disabled={approveMutation.isPending}
            >
              {approveMutation.isPending ? "Aprovando..." : "Confirmar Aprovação"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rejeitar Orçamento</DialogTitle>
            <DialogDescription>
              Você está prestes a rejeitar o orçamento #{quote.quoteNumber}. Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowRejectDialog(false)}
              disabled={rejectMutation.isPending}
            >
              Cancelar
            </Button>
            <Button 
              onClick={() => rejectMutation.mutate()}
              variant="destructive"
              disabled={rejectMutation.isPending}
            >
              {rejectMutation.isPending ? "Rejeitando..." : "Confirmar Rejeição"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}