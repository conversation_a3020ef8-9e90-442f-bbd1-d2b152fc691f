import { pgTable, text, serial, integer, boolean, timestamp, pgEnum, date, time, json, numeric, varchar } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Enum for user roles
export const roleEnum = pgEnum('role', ['admin', 'technician', 'receptionist']);

// Enum for service order status
export const serviceOrderStatusEnum = pgEnum('service_order_status', [
  'received', 
  'in_analysis', 
  'waiting_parts', 
  'waiting_approval', 
  'in_execution', 
  'completed', 
  'delivered'
]);

// Enum for technician status
export const technicianStatusEnum = pgEnum('technician_status', [
  'available',
  'on_service',
  'off_duty'
]);

// Enum for schedule status
export const scheduleStatusEnum = pgEnum('schedule_status', [
  'scheduled',
  'in_progress',
  'completed',
  'cancelled'
]);

// Enum for payment status
export const paymentStatusEnum = pgEnum('payment_status', [
  'pending',
  'paid',
  'partially_paid',
  'cancelled',
  'refunded'
]);

// Enum for payment method
export const paymentMethodEnum = pgEnum('payment_method', [
  'credit_card',
  'debit_card',
  'bank_transfer',
  'cash',
  'pix',
  'boleto'
]);

// Enum for invoice status
export const invoiceStatusEnum = pgEnum('invoice_status', [
  'draft',
  'issued',
  'sent',
  'paid',
  'cancelled',
  'overdue'
]);

// Enum for appointment types
export const appointmentTypeEnum = pgEnum('appointment_type', [
  'technical_visit',
  'equipment_delivery',
  'equipment_pickup',
  'supplier_visit',
  'other'
]);

// Enum for appointment status
export const appointmentStatusEnum = pgEnum('appointment_status', [
  'scheduled',
  'confirmed',
  'in_progress',
  'completed',
  'cancelled',
  'rescheduled'
]);

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: roleEnum("role").notNull().default('receptionist'),
  phone: text("phone"),
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// Clients table
export const clients = pgTable("clients", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email"),
  phone: text("phone"),
  document: text("document"), // CPF/CNPJ
  address: text("address"),
  city: text("city"),
  state: text("state"),
  zipCode: text("zip_code"),
  notes: text("notes"),
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// Equipment categories
export const equipmentCategories = pgTable("equipment_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
});

// Equipment table
export const equipment = pgTable("equipment", {
  id: serial("id").primaryKey(),
  clientId: integer("client_id").notNull().references(() => clients.id),
  categoryId: integer("category_id").references(() => equipmentCategories.id),
  brand: text("brand"),
  model: text("model"),
  serialNumber: text("serial_number"),
  description: text("description"),
  status: text("status").default('active'),
  purchaseDate: timestamp("purchase_date"),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Technicians table
export const technicians = pgTable("technicians", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  specialties: text("specialties"),
  status: technicianStatusEnum("status").default('available'),
  notes: text("notes"),
});

// Inventory categories
export const inventoryCategories = pgTable("inventory_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
});

// Inventory items
export const inventoryItems = pgTable("inventory_items", {
  id: serial("id").primaryKey(),
  categoryId: integer("category_id").references(() => inventoryCategories.id),
  name: text("name").notNull(),
  description: text("description"),
  sku: text("sku"),
  quantity: integer("quantity").default(0),
  minQuantity: integer("min_quantity").default(0),
  price: integer("price"), // in cents
  supplier: text("supplier"),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Service orders
export const serviceOrders = pgTable("service_orders", {
  id: serial("id").primaryKey(),
  orderNumber: text("order_number").notNull().unique(),
  clientId: integer("client_id").notNull().references(() => clients.id),
  equipmentId: integer("equipment_id").references(() => equipment.id),
  technicianId: integer("technician_id").references(() => technicians.id),
  status: serviceOrderStatusEnum("status").default('received'),
  description: text("description").notNull(),
  diagnostics: text("diagnostics"),
  solution: text("solution"),
  internalNotes: text("internal_notes"),
  // Removemos a coluna totalAmount por enquanto
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  completedAt: timestamp("completed_at"),
  estimatedCompletionDate: timestamp("estimated_completion_date"),
});

// Service order items (parts used)
export const serviceOrderItems = pgTable("service_order_items", {
  id: serial("id").primaryKey(),
  serviceOrderId: integer("service_order_id").notNull().references(() => serviceOrders.id),
  inventoryItemId: integer("inventory_item_id").references(() => inventoryItems.id),
  description: text("description").notNull(),
  quantity: integer("quantity").notNull(),
  unitPrice: integer("unit_price").notNull(), // in cents
});

// Enum for quote status
export const quoteStatusEnum = pgEnum('quote_status', [
  'pending',
  'approved',
  'rejected'
]);

// Quotes
export const quotes = pgTable("quotes", {
  id: serial("id").primaryKey(),
  quoteNumber: text("quote_number").notNull().unique(),
  serviceOrderId: integer("service_order_id").references(() => serviceOrders.id),
  clientId: integer("client_id").notNull().references(() => clients.id),
  status: quoteStatusEnum("status").default('pending'),
  total: integer("total").default(0), // in cents
  description: text("description"),
  notes: text("notes"),
  validUntil: timestamp("valid_until"),
  createdAt: timestamp("created_at").defaultNow(),
  approvedAt: timestamp("approved_at"),
});

// Technician schedules
export const technicianSchedules = pgTable("technician_schedules", {
  id: serial("id").primaryKey(),
  technicianId: integer("technician_id").notNull().references(() => technicians.id),
  serviceOrderId: integer("service_order_id").references(() => serviceOrders.id),
  title: text("title").notNull(),
  description: text("description"),
  scheduleDate: date("schedule_date").notNull(),
  startTime: time("start_time").notNull(),
  endTime: time("end_time").notNull(),
  status: scheduleStatusEnum("status").default('scheduled'),
  location: text("location"),
  clientId: integer("client_id").references(() => clients.id),
  isAllDay: boolean("is_all_day").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  completedAt: timestamp("completed_at"),
});

// Appointments table
export const appointments = pgTable("appointments", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description"),
  type: appointmentTypeEnum("type").notNull(),
  status: appointmentStatusEnum("status").default('scheduled'),
  appointmentDate: timestamp("appointment_date").notNull(),
  startTime: time("start_time").notNull(),
  endTime: time("end_time"),
  location: text("location"),
  clientId: integer("client_id").references(() => clients.id),
  technicianId: integer("technician_id").references(() => technicians.id),
  serviceOrderId: integer("service_order_id").references(() => serviceOrders.id),
  contactPerson: text("contact_person"),
  contactPhone: text("contact_phone"),
  contactEmail: text("contact_email"),
  notes: text("notes"),
  createdBy: integer("created_by").notNull().references(() => users.id),
  isRecurring: boolean("is_recurring").default(false),
  recurringPattern: text("recurring_pattern"), // JSON string for recurring rules
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  completedAt: timestamp("completed_at"),
  cancelledAt: timestamp("cancelled_at"),
  cancelReason: text("cancel_reason"),
});

// Payment methods table
export const paymentMethods = pgTable("payment_methods", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  active: boolean("active").default(true),
  description: text("description"),
  requiresApproval: boolean("requires_approval").default(false),
  requiresDocument: boolean("requires_document").default(false),
  type: paymentMethodEnum("type").notNull(),
  processingFee: integer("processing_fee").default(0), // em centavos (%)
  additionalInfo: json("additional_info"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Payments table
export const payments = pgTable("payments", {
  id: serial("id").primaryKey(),
  transactionId: text("transaction_id").unique(),
  amount: integer("amount").notNull(), // in cents
  paymentMethodId: integer("payment_method_id").notNull().references(() => paymentMethods.id),
  serviceOrderId: integer("service_order_id").references(() => serviceOrders.id),
  invoiceId: integer("invoice_id"),  // Will reference invoices, but defined after to avoid circular dependency
  status: paymentStatusEnum("status").default('pending'),
  paidAt: timestamp("paid_at"),
  refundedAt: timestamp("refunded_at"),
  clientId: integer("client_id").notNull().references(() => clients.id),
  receivedBy: integer("received_by").references(() => users.id),
  notes: text("notes"),
  receiptUrl: text("receipt_url"),
  additionalInfo: json("additional_info"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Invoices table
export const invoices = pgTable("invoices", {
  id: serial("id").primaryKey(),
  invoiceNumber: text("invoice_number").notNull().unique(),
  clientId: integer("client_id").notNull().references(() => clients.id),
  serviceOrderId: integer("service_order_id").references(() => serviceOrders.id),
  status: invoiceStatusEnum("status").default('draft'),
  subtotal: integer("subtotal").notNull(), // in cents
  discount: integer("discount").default(0), // in cents
  tax: integer("tax").default(0), // in cents
  totalAmount: integer("total_amount").notNull(), // in cents
  paidAmount: integer("paid_amount").default(0), // in cents
  dueDate: date("due_date"),
  issueDate: date("issue_date"),
  paidAt: timestamp("paid_at"),
  sentAt: timestamp("sent_at"),
  cancelledAt: timestamp("cancelled_at"),
  notes: text("notes"),
  terms: text("terms"),
  additionalInfo: json("additional_info"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Services table
export const services = pgTable("services", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  price: integer("price").notNull(), // in cents
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Parts table
export const parts = pgTable("parts", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  brand: text("brand"),
  description: text("description"),
  barcode: text("barcode"),
  internalCode: text("internal_code"),
  sku: text("sku"),
  categoryId: integer("category_id").references(() => inventoryCategories.id),
  purchaseValue: integer("purchase_value").notNull(), // in cents
  saleValue: integer("sale_value").notNull(), // in cents
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Invoice items table
export const invoiceItems = pgTable("invoice_items", {
  id: serial("id").primaryKey(),
  invoiceId: integer("invoice_id").notNull().references(() => invoices.id),
  description: text("description").notNull(),
  quantity: integer("quantity").notNull(),
  unitPrice: integer("unit_price").notNull(), // in cents
  serviceOrderItemId: integer("service_order_item_id").references(() => serviceOrderItems.id),
  inventoryItemId: integer("inventory_item_id").references(() => inventoryItems.id),
  discount: integer("discount").default(0), // in cents
  tax: integer("tax").default(0), // in cents
  totalPrice: integer("total_price").notNull(), // in cents (quantity * unitPrice - discount + tax)
});

// Create Zod schemas for insert operations
export const insertUserSchema = createInsertSchema(users).omit({ id: true, createdAt: true });
export const insertClientSchema = createInsertSchema(clients).omit({ id: true, createdAt: true });
export const insertEquipmentCategorySchema = createInsertSchema(equipmentCategories).omit({ id: true });
// Criar schema personalizado para equipment
export const insertEquipmentSchema = createInsertSchema(equipment)
  .omit({ id: true, createdAt: true })
  .transform((data) => {
    // Se purchaseDate for uma string, converte para Date
    if (data.purchaseDate && typeof data.purchaseDate === 'string') {
      return {
        ...data,
        purchaseDate: new Date(data.purchaseDate)
      };
    }
    return data;
  });
export const insertTechnicianSchema = createInsertSchema(technicians).omit({ id: true });
export const insertInventoryCategorySchema = createInsertSchema(inventoryCategories).omit({ id: true });
export const insertInventoryItemSchema = createInsertSchema(inventoryItems).omit({ id: true, createdAt: true });
export const insertServiceOrderSchema = createInsertSchema(serviceOrders)
  .omit({ 
    id: true, 
    orderNumber: true, // orderNumber is auto-generated
    createdAt: true, 
    updatedAt: true, 
    completedAt: true 
  })
  .transform((data) => {
    const transformedData = { ...data };

    // Converter estimatedCompletionDate de string para Date
    if (transformedData.estimatedCompletionDate && typeof transformedData.estimatedCompletionDate === 'string') {
      transformedData.estimatedCompletionDate = new Date(transformedData.estimatedCompletionDate);
    }

    return transformedData;
  });
export const insertServiceOrderItemSchema = createInsertSchema(serviceOrderItems).omit({ id: true });
export const insertQuoteSchema = createInsertSchema(quotes).omit({ 
  id: true, 
  createdAt: true, 
  approvedAt: true 
}).extend({
  validUntil: z.date().optional().or(z.string().transform(val => new Date(val)))
});

export const insertTechnicianScheduleSchema = createInsertSchema(technicianSchedules).omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true, 
  completedAt: true 
});

// Add appointment schema
export const insertAppointmentSchema = createInsertSchema(appointments).omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true, 
  completedAt: true,
  cancelledAt: true
}).extend({
  appointmentDate: z.union([z.date(), z.string()]).transform((val) => typeof val === 'string' ? new Date(val) : val),
  contactEmail: z.string().email().optional().or(z.literal("")).nullable(),
  createdBy: z.number()
});

export type InsertAppointment = z.infer<typeof insertAppointmentSchema>;

// Financial module schemas
export const insertPaymentMethodSchema = createInsertSchema(paymentMethods).omit({
  id: true,
  createdAt: true
});

export const insertPaymentSchema = createInsertSchema(payments).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  paidAt: true,
  refundedAt: true
});

export const insertInvoiceSchema = createInsertSchema(invoices).omit({
  id: true,
  invoiceNumber: true, // auto-generated
  createdAt: true,
  updatedAt: true,
  paidAt: true,
  sentAt: true,
  cancelledAt: true,
  paidAmount: true
});

export const insertServiceSchema = createInsertSchema(services).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertPartSchema = createInsertSchema(parts).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertInvoiceItemSchema = createInsertSchema(invoiceItems).omit({
  id: true
});

// Export types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Client = typeof clients.$inferSelect;
export type InsertClient = z.infer<typeof insertClientSchema>;

export type EquipmentCategory = typeof equipmentCategories.$inferSelect;
export type InsertEquipmentCategory = z.infer<typeof insertEquipmentCategorySchema>;

export type Equipment = typeof equipment.$inferSelect;
export type InsertEquipment = z.infer<typeof insertEquipmentSchema>;

export type Technician = typeof technicians.$inferSelect;
export type InsertTechnician = z.infer<typeof insertTechnicianSchema>;

export type InventoryCategory = typeof inventoryCategories.$inferSelect;
export type InsertInventoryCategory = z.infer<typeof insertInventoryCategorySchema>;

export type InventoryItem = typeof inventoryItems.$inferSelect;
export type InsertInventoryItem = z.infer<typeof insertInventoryItemSchema>;

export type ServiceOrder = typeof serviceOrders.$inferSelect;
export type InsertServiceOrder = z.infer<typeof insertServiceOrderSchema>;

export type ServiceOrderItem = typeof serviceOrderItems.$inferSelect;
export type InsertServiceOrderItem = z.infer<typeof insertServiceOrderItemSchema>;

export type Quote = typeof quotes.$inferSelect;
export type InsertQuote = z.infer<typeof insertQuoteSchema>;

export type TechnicianSchedule = typeof technicianSchedules.$inferSelect;
export type InsertTechnicianSchedule = z.infer<typeof insertTechnicianScheduleSchema>;

export type Appointment = typeof appointments.$inferSelect;
export type InsertAppointment = z.infer<typeof insertAppointmentSchema>;

// Financial module types
export type PaymentMethod = typeof paymentMethods.$inferSelect;
export type InsertPaymentMethod = z.infer<typeof insertPaymentMethodSchema>;

export type Payment = typeof payments.$inferSelect;
export type InsertPayment = z.infer<typeof insertPaymentSchema>;

export type Invoice = typeof invoices.$inferSelect;
export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;

export type InvoiceItem = typeof invoiceItems.$inferSelect;
export type InsertInvoiceItem = z.infer<typeof insertInvoiceItemSchema>;

export type Service = typeof services.$inferSelect;
export type InsertService = z.infer<typeof insertServiceSchema>;

export type Part = typeof parts.$inferSelect;
export type InsertPart = z.infer<typeof insertPartSchema>;