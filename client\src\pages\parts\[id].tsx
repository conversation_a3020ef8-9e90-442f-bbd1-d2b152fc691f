import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useLocation, useParams } from 'wouter';
import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import Sidebar from '@/components/layout/sidebar';
import Header from '@/components/layout/header';
import { Skeleton } from '@/components/ui/skeleton';

// Schema para validação do formulário
const partFormSchema = z.object({
  name: z.string().min(2, { message: 'O nome deve ter pelo menos 2 caracteres' }),
  brand: z.string().optional(),
  description: z.string().nullable().optional(),
  barcode: z.string().optional(),
  internalCode: z.string().nullable().optional(),
  purchaseValue: z.coerce.number().min(0, { message: 'O valor de compra deve ser positivo' }),
  saleValue: z.coerce.number().min(0, { message: 'O valor de venda deve ser positivo' }),
  active: z.boolean().default(true),
});

type PartFormValues = z.infer<typeof partFormSchema>;

export default function EditPartPage() {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [location, navigate] = useLocation();

  // Inicializar o formulário
  const form = useForm<PartFormValues>({
    resolver: zodResolver(partFormSchema),
    defaultValues: {
      name: '',
      brand: '',
      description: '',
      barcode: '',
      internalCode: '',
      purchaseValue: 0,
      saleValue: 0,
      active: true,
    },
  });

  // Query para obter os dados da peça
  const { data: part, isLoading, error } = useQuery({
    queryKey: [`/api/parts/${id}`],
    enabled: !!id,
    select: (data: any) => {
      // Ajustar os valores monetários de centavos para reais
      return {
        ...data,
        purchaseValue: data.purchaseValue / 100, // Converte de centavos para reais
        saleValue: data.saleValue / 100, // Converte de centavos para reais
      };
    }
  });

  // Atualizar o formulário quando os dados chegarem
  useEffect(() => {
    if (part) {
      form.reset(part);
    }
  }, [part, form]);

  // Mutação para atualizar peça
  const updatePartMutation = useMutation({
    mutationFn: (data: PartFormValues) => {
      // Converter valores monetários para centavos
      const transformedData = {
        ...data,
        purchaseValue: Math.round(data.purchaseValue * 100), // Converte para centavos
        saleValue: Math.round(data.saleValue * 100), // Converte para centavos
      };
      
      return apiRequest('PATCH', `/api/parts/${id}`, transformedData);
    },
    onSuccess: () => {
      toast({
        title: 'Peça atualizada',
        description: 'A peça foi atualizada com sucesso.',
      });
      queryClient.invalidateQueries({ queryKey: [`/api/parts/${id}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/parts'] });
      navigate('/parts');
    },
    onError: (error) => {
      toast({
        title: 'Erro ao atualizar peça',
        description: 'Ocorreu um erro ao atualizar a peça. Tente novamente.',
        variant: 'destructive',
      });
    },
  });

  // Manipulador de envio do formulário
  function onSubmit(data: PartFormValues) {
    updatePartMutation.mutate(data);
  }

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  if (isLoading) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Editar Peça" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden p-6">
              <div className="max-w-3xl mx-auto space-y-6">
                <Skeleton className="h-10 w-1/3" />
                <Skeleton className="h-36 w-full" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
        
        <div className="flex-1 flex flex-col h-screen overflow-hidden">
          <Header title="Editar Peça" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
          
          <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden p-6">
              <div className="max-w-3xl mx-auto">
                <div className="p-6 bg-red-50 border border-red-200 rounded-lg text-red-600">
                  <h3 className="text-lg font-bold">Erro ao carregar peça</h3>
                  <p>Não foi possível carregar os dados da peça. Tente novamente mais tarde.</p>
                  <Button variant="outline" className="mt-4" onClick={() => navigate('/parts')}>
                    Voltar para lista de peças
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Editar Peça" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex flex-col md:flex-row md:items-center justify-between px-6 py-4 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-medium text-gray-800">Editar Peça</h3>
                <p className="text-sm text-gray-500">Atualizar informações da peça</p>
              </div>
            </div>
            
            <div className="p-6">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome da Peça*</FormLabel>
                          <FormControl>
                            <Input placeholder="Digite o nome da peça" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="brand"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Marca</FormLabel>
                          <FormControl>
                            <Input placeholder="Digite a marca (opcional)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="barcode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Código de Barras</FormLabel>
                          <FormControl>
                            <Input placeholder="Digite o código de barras (opcional)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="internalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Código Interno</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="Digite o código interno (opcional)" 
                              {...field} 
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="purchaseValue"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor de Compra (R$)*</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.01" 
                              min="0"
                              placeholder="0.00" 
                              value={field.value || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                field.onChange(value === '' ? 0 : parseFloat(value));
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="saleValue"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor de Venda (R$)*</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.01" 
                              min="0"
                              placeholder="0.00" 
                              value={field.value || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                field.onChange(value === '' ? 0 : parseFloat(value));
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Descrição</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Digite uma descrição detalhada da peça (opcional)"
                            className="min-h-[120px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Ativo</FormLabel>
                          <FormDescription>
                            Marque esta opção para que a peça apareça como disponível no sistema.
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end space-x-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate('/parts')}
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      disabled={updatePartMutation.isPending}
                    >
                      {updatePartMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Salvando...
                        </>
                      ) : (
                        'Salvar Alterações'
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}