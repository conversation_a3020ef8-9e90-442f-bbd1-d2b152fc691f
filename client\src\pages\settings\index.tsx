import { useState } from "react";
import { useLocation } from "wouter";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Users,
  ShieldCheck,
  CreditCard,
  Smartphone,
  Settings as SettingsIcon,
  ArrowRight,
  User,
  UserCog,
  Bell,
  Lock,
  MessageSquare,
  DollarSign,
  Calendar,
} from "lucide-react";

export default function Settings() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [, navigate] = useLocation();

  interface SettingsItem {
    title: string;
    description: string;
    icon: React.ReactNode;
    path: string;
    disabled?: boolean;
  }

  interface SettingsGroup {
    title: string;
    description: string;
    items: SettingsItem[];
  }

  const settingsGroups: SettingsGroup[] = [
    {
      title: "Usuários e Permissões",
      description: "<PERSON><PERSON><PERSON><PERSON> usuários, técnicos e controle de acesso",
      items: [
        {
          title: "Usuários",
          description: "Gerenciar contas de usuários do sistema",
          icon: <Users className="h-6 w-6" />,
          path: "/settings/users",
        },
        {
          title: "Permissões",
          description: "Controlar permissões por função",
          icon: <ShieldCheck className="h-6 w-6" />,
          path: "/settings/permissions",
        },
      ],
    },
    {
      title: "Integrações",
      description: "Configure integrações com sistemas externos",
      items: [
        {
          title: "WhatsApp",
          description: "Configurar notificações via WhatsApp",
          icon: <Smartphone className="h-6 w-6" />,
          path: "/settings/whatsapp",
        },
        {
          title: "Stripe",
          description: "Configurar pagamento online",
          icon: <CreditCard className="h-6 w-6" />,
          path: "/settings/stripe",
        },
      ],
    },
    {
      title: "Financeiro",
      description: "Configure opções relacionadas a cobranças e pagamentos",
      items: [
        {
          title: "Formas de Pagamento",
          description: "Gerenciar métodos de pagamento aceitos",
          icon: <DollarSign className="h-6 w-6" />,
          path: "/settings/payment-methods",
        },
      ],
    },
    {
      title: "Sistema",
      description: "Configurações gerais do sistema",
      items: [
        {
          title: "Notificações",
          description: "Gerenciar notificações do sistema",
          icon: <Bell className="h-6 w-6" />,
          path: "/settings/notifications",
          disabled: true,
        },
        {
          title: "Backup",
          description: "Configurar backup de dados",
          icon: <Lock className="h-6 w-6" />,
          path: "/settings/backup",
          disabled: true,
        },
      ],
    },
  ];

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar open={mobileMenuOpen} setOpen={setMobileMenuOpen} />
      
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header title="Configurações" onMenuButtonClick={() => setMobileMenuOpen(!mobileMenuOpen)} />
        
        <div className="flex-1 overflow-y-auto bg-slate-100 p-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-3">
              {settingsGroups.map((group, index) => (
                <Card key={index} className="w-full overflow-hidden">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2 text-base md:text-lg flex-nowrap">
                      <SettingsIcon className="h-5 w-5 text-primary flex-shrink-0" />
                      <span className="truncate max-w-[200px]">{group.title}</span>
                    </CardTitle>
                    <CardDescription className="w-full">
                      <div className="whitespace-nowrap overflow-hidden text-ellipsis w-full">
                        {group.description}
                      </div>
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3 px-3 pb-3">
                    {group.items.map((item, itemIndex) => (
                      <div 
                        key={itemIndex} 
                        className={`flex items-start p-2 rounded-lg border transition-colors ${
                          item.disabled
                            ? "bg-gray-50 cursor-not-allowed opacity-60"
                            : "bg-card hover:bg-accent hover:text-accent-foreground cursor-pointer"
                        }`}
                        onClick={() => !item.disabled && navigate(item.path)}
                      >
                        <div className="mr-2 mt-0.5 rounded-full bg-primary/10 p-1.5 text-primary flex-shrink-0">
                          {item.icon}
                        </div>
                        <div className="flex-1 min-w-0 pr-2">
                          <h3 className="font-medium leading-none mb-1 flex items-center text-sm">
                            <span className="truncate max-w-[150px]">{item.title}</span>
                            {item.disabled && (
                              <span className="ml-1 text-xs bg-gray-200 text-gray-600 px-1 py-0.5 rounded flex-shrink-0">
                                Em breve
                              </span>
                            )}
                          </h3>
                          <div className="w-full whitespace-nowrap overflow-hidden text-ellipsis">
                            <p className="text-xs text-muted-foreground">
                              {item.description}
                            </p>
                          </div>
                        </div>
                        {!item.disabled && (
                          <div className="flex-shrink-0">
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              className="h-7 w-7 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                navigate(item.path);
                              }}
                            >
                              <ArrowRight className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
            
            <div className="mt-8 text-center text-sm text-muted-foreground">
              <p>Simplesmed TechServer • Versão 1.0.0</p>
              <p className="mt-1">© 2025 Simplesmed. Todos os direitos reservados.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}